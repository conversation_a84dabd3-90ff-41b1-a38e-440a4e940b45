# Azure Container Apps configuration for CPA Backend - Development
# Replaces GCP Cloud Run configuration

apiVersion: 2022-03-01
location: East US
name: ca-cpa-backend-dev
properties:
  managedEnvironmentId: /subscriptions/SUBSCRIPTION_ID/resourceGroups/rg-kraken-dev/providers/Microsoft.App/managedEnvironments/cae-kraken-dev
  configuration:
    activeRevisionsMode: Single
    ingress:
      external: true
      targetPort: 80
      transport: auto
      traffic:
      - weight: 100
        latestRevision: true
    registries:
    - server: krakenacr.azurecr.io
      identity: /subscriptions/SUBSCRIPTION_ID/resourceGroups/rg-kraken-dev/providers/Microsoft.ManagedIdentity/userAssignedIdentities/mi-kraken-dev
    secrets:
    - name: connection-string
      keyVaultUrl: https://kv-kraken-dev-001.vault.azure.net/secrets/ConnectionStrings--DefaultConnection
      identity: /subscriptions/SUBSCRIPTION_ID/resourceGroups/rg-kraken-dev/providers/Microsoft.ManagedIdentity/userAssignedIdentities/mi-kraken-dev
    - name: azure-ad-client-secret
      keyVaultUrl: https://kv-kraken-dev-001.vault.azure.net/secrets/AzureAd--ClientSecret
      identity: /subscriptions/SUBSCRIPTION_ID/resourceGroups/rg-kraken-dev/providers/Microsoft.ManagedIdentity/userAssignedIdentities/mi-kraken-dev
    - name: sendgrid-api-key
      keyVaultUrl: https://kv-kraken-dev-001.vault.azure.net/secrets/SendGrid--APIKey
      identity: /subscriptions/SUBSCRIPTION_ID/resourceGroups/rg-kraken-dev/providers/Microsoft.ManagedIdentity/userAssignedIdentities/mi-kraken-dev
  template:
    containers:
    - image: krakenacr.azurecr.io/cpa-backend:latest
      name: cpa-backend
      resources:
        cpu: 2.0
        memory: 4Gi
      env:
      - name: ASPNETCORE_ENVIRONMENT
        value: Development
      - name: AZURE_CLIENT_ID
        value: CLIENT_ID_FOR_MANAGED_IDENTITY
      - name: ConnectionStrings__DefaultConnection
        secretRef: connection-string
      - name: AzureAd__ClientSecret
        secretRef: azure-ad-client-secret
      - name: SendGrid__APIKey
        secretRef: sendgrid-api-key
      - name: BlobStorage__APMStorageAccountName
        value: stakrakendev001
      - name: BlobStorage__APMBlobContainerName
        value: apm-dev
      - name: BlobStorage__APMWOStorageAccountName
        value: stakrakendev001
      - name: BlobStorage__APMWOBlobContainerName
        value: apm-workorders-dev
      - name: BlobStorage__KeyVaultName
        value: kv-kraken-dev-001
      - name: Connections__KeyVaultName
        value: kv-kraken-dev-001
      - name: Connections__ResourceGroupName
        value: rg-kraken-dev
      - name: Connections__SubscriptionId
        value: SUBSCRIPTION_ID
      - name: Connections__DatabaseName
        value: cosmos-clientportalapi-dev-001
      - name: Connections__AnteaAttachmentsBlobContainer
        value: antea-attachments-dev
      - name: Connections__AnteaSubmissionsBlobContainer
        value: antea-submissions-dev
      - name: ApplicationInsights__ConnectionString
        value: InstrumentationKey=INSTRUMENTATION_KEY;IngestionEndpoint=https://eastus-8.in.applicationinsights.azure.com/
    scale:
      minReplicas: 1
      maxReplicas: 5
      rules:
      - name: http-scaling
        http:
          metadata:
            concurrentRequests: '10'
  identity:
    type: UserAssigned
    userAssignedIdentities:
      /subscriptions/SUBSCRIPTION_ID/resourceGroups/rg-kraken-dev/providers/Microsoft.ManagedIdentity/userAssignedIdentities/mi-kraken-dev: {}
tags:
  environment: development
  application: cpa-backend
type: Microsoft.App/containerApps
