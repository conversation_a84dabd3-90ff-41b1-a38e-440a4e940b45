trigger:
  branches:
    include:
    - main
    - develop
  paths:
    include:
    - api/*

variables:
  - group: kraken-variables
  - name: containerRegistry
    value: 'krakenacr.azurecr.io'
  - name: imageRepository
    value: 'kraken-api'
  - name: dockerfilePath
    value: 'api/OrderTracking.API/OrderTracking.API/Dockerfile'
  - name: tag
    value: '$(Build.BuildId)'

stages:
- stage: Build
  displayName: Build and Test
  jobs:
  - job: Build
    displayName: Build
    pool:
      vmImage: ubuntu-latest
    steps:
    - task: UseDotNet@2
      displayName: 'Use .NET 6.0'
      inputs:
        packageType: 'sdk'
        version: '6.0.x'

    - task: DotNetCoreCLI@2
      displayName: 'Restore packages'
      inputs:
        command: 'restore'
        projects: 'api/OrderTracking.API/**/*.csproj'

    - task: DotNetCoreCLI@2
      displayName: 'Build solution'
      inputs:
        command: 'build'
        projects: 'api/OrderTracking.API/**/*.csproj'
        arguments: '--configuration Release --no-restore'

    - task: DotNetCoreCLI@2
      displayName: 'Run tests'
      inputs:
        command: 'test'
        projects: 'api/OrderTracking.API/**/*Tests.csproj'
        arguments: '--configuration Release --no-build --collect:"XPlat Code Coverage"'

    - task: Docker@2
      displayName: 'Build and push Docker image'
      inputs:
        containerRegistry: 'kraken-acr-connection'
        repository: $(imageRepository)
        command: 'buildAndPush'
        Dockerfile: $(dockerfilePath)
        buildContext: 'api/OrderTracking.API'
        tags: |
          $(tag)
          latest

- stage: DeployDev
  displayName: Deploy to Development
  dependsOn: Build
  condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/develop'))
  jobs:
  - deployment: DeployDev
    displayName: Deploy to Development
    pool:
      vmImage: ubuntu-latest
    environment: 'kraken-dev'
    strategy:
      runOnce:
        deploy:
          steps:
          - task: AzureCLI@2
            displayName: 'Deploy to Container Apps'
            inputs:
              azureSubscription: 'azure-service-connection'
              scriptType: 'bash'
              scriptLocation: 'inlineScript'
              inlineScript: |
                az containerapp update \
                  --name "ca-kraken-api-dev" \
                  --resource-group "rg-kraken-dev-001" \
                  --image "$(containerRegistry)/$(imageRepository):$(tag)"

- stage: DeployProd
  displayName: Deploy to Production
  dependsOn: Build
  condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/main'))
  jobs:
  - deployment: DeployProd
    displayName: Deploy to Production
    pool:
      vmImage: ubuntu-latest
    environment: 'kraken-prod'
    strategy:
      runOnce:
        deploy:
          steps:
          - task: AzureCLI@2
            displayName: 'Deploy to Container Apps'
            inputs:
              azureSubscription: 'azure-service-connection'
              scriptType: 'bash'
              scriptLocation: 'inlineScript'
              inlineScript: |
                az containerapp update \
                  --name "ca-kraken-api-prod" \
                  --resource-group "rg-kraken-prod-001" \
                  --image "$(containerRegistry)/$(imageRepository):$(tag)"
