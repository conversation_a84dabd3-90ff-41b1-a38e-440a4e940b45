import { Component, ViewChild } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import {
    BehaviorSubject,
    Observable,
    ReplaySubject,
    Subject,
    of,
    throwError
} from 'rxjs';
import { catchError, shareReplay, switchMap, tap } from 'rxjs/operators';
import {
    AssetsDetailsComponent,
    WorkOrdersGridComponent
} from '../../components';
import {
    NewTask,
    ProjectUpdate,
    ProjectVm,
    TaskUpdate,
    WorkOrderDetailTabInfo,
    WorkOrderGridRow
} from '../../models';
import { ApmService } from '../../services';

@Component({
    selector: 'app-work-orders',
    templateUrl: './work-orders.component.html',
    styleUrls: ['./work-orders.component.scss']
})
export class WorkOrdersComponent {
    private _selectedWorkOrderId = new Subject<string>();
    private _loading = new BehaviorSubject<boolean>(false);
    private _project = new ReplaySubject<ProjectVm>();
    private _currentWorkOrderId: string;
    private _currentWorkOrderProjectId: string;
    private destroy = new Subject();

    @ViewChild(WorkOrdersGridComponent) workOrdersGrid: WorkOrdersGridComponent;
    @ViewChild(AssetsDetailsComponent) assetDetails: AssetsDetailsComponent;

    selectedWorkOrder$ = this._selectedWorkOrderId.pipe(
        switchMap((selectedWorkOrderId) => {
            if (selectedWorkOrderId) {
                this._loading.next(true);
                this._currentWorkOrderId = selectedWorkOrderId;
                return this._apm.getWorkOrder(
                    selectedWorkOrderId,
                    this._currentWorkOrderProjectId
                );
            } else {
                this.assetDetails.tabPanel.instance.option('selectedIndex', 0);
                return of(undefined);
            }
        }),
        catchError((error) => {
            this._loading.next(false);
            return throwError(() => new Error('Work order details not found'));
        })
        // shareReplay(1)
    );
    get project$(): Observable<ProjectVm> {
        return this._project.asObservable();
    }
    location$ = this.project$.pipe(
        switchMap((project) => {
            if (project) {
                return this._apm.getLocation(project?.locationId);
            } else {
                return of(undefined);
            }
        }),
        tap((_) => this._loading.next(false)),
        shareReplay()
    );

    get loading$() {
        return this._loading.asObservable();
    }

    users$ = this._apm.getUsers();
    allowEditing$ = this._apm.allowEditing$;

    constructor(
        private readonly _apm: ApmService,
        private readonly _toasts: ToastrService
    ) {
        this.selectedWorkOrder$
            .pipe(
                switchMap((w) =>
                    w ? this._apm.getProject(w.projectId) : of(undefined)
                )
                // shareReplay()
            )
            .subscribe((project) => {
                this._project.next(project);
            });
    }

    onSelectionChanged(workOrder: WorkOrderGridRow) {
        this._currentWorkOrderProjectId = workOrder.projectId;
        this._selectedWorkOrderId.next(workOrder?.id);
    }

    onWODetailsSave(update: Partial<WorkOrderDetailTabInfo>) {
        this._apm.updateWorkOrder(update).subscribe((wos) => {
            this.workOrdersGrid.refreshGrid();
            this.assetDetails.woDetailsTab.isSaving = false;
            this.assetDetails.woDetailsTab.isEditing = false;
            this._toasts.success(
                'Changes were saved successfully',
                'Saved successfully'
            );
        });
    }

    onProjectSave(update: Partial<ProjectUpdate>) {
        this._apm.putProject(update).subscribe((updatedProject) => {
            this.assetDetails.projectDetailsTab.isSaving.next(false);
            this._project.next(updatedProject);
            this.workOrdersGrid.refreshGrid();
            this._toasts.success(
                'Changes to project successfully saved',
                'Project updated'
            );
        });
    }

    taskAdded(newTask: NewTask) {
        this._apm.createTask(newTask).subscribe((task) => {
            this.workOrdersGrid.refreshGrid();
            this.rebroadcastSelectedWorkOrder();
            this._toasts.success(
                'Task was added successfully',
                'Saved successfully'
            );
        });
    }

    taskUpdated(taskUpdate: TaskUpdate) {
        this._apm.updateTask(taskUpdate).subscribe((wos) => {
            this.workOrdersGrid.refreshGrid();
            this.rebroadcastSelectedWorkOrder();
            this._toasts.success(
                'Task was updated successfully',
                'Saved successfully'
            );
        });
    }

    private rebroadcastSelectedWorkOrder() {
        this._selectedWorkOrderId.next(this._currentWorkOrderId);
    }
}
