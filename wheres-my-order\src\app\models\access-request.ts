import { RequestAccessModule, WMOSpecialAccess } from '../core/services';

export class AccessRequest {
    modules: RequestAccessModule[];
    districts: string[];
    companies: string[];
    wmoSpecialAccess: WMOSpecialAccess;
    companyNameAndSite: string;
    teamContact: string;
    managementSites: string;

    constructor(options?: Partial<AccessRequest>) {
        if (options) {
            for (const [key, value] of Object.entries(options)) {
                this[key] = value;
            }
        }
    }
}
