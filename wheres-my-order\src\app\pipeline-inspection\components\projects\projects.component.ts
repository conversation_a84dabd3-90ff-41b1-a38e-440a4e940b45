import { Component } from '@angular/core';
import cloneDeep from 'clone-deep';
import CustomStore from 'devextreme/data/custom_store';
import dxDataGrid from 'devextreme/ui/data_grid';
import { ToastrService } from 'ngx-toastr';
import { Observable, of } from 'rxjs';
import { v4 as uuidv4 } from 'uuid';
import { UserProfile } from '../../../profile/models';
import { UsersService } from '../../../shared/services';
import { Client, PIAResponse, Project } from '../../models';
import { PipelineInspectionService } from '../../services';

@Component({
    selector: 'app-projects',
    templateUrl: './projects.component.html',
    styleUrls: ['./projects.component.scss']
})
export class ProjectsComponent {
    clients$: Observable<Client[]>;
    currentUser$: Observable<UserProfile>;
    dataSource: CustomStore;

    private _oldProject: Project;
    private _newProject: Project;
    private _oldClientId: string;
    private _newClientId: string;
    private _clientsCache: Client[];

    constructor(
        private readonly _pia: PipelineInspectionService,
        private readonly _toasts: ToastrService,
        private readonly _users: UsersService
    ) {
        this.clients$ = this._pia.clients$;
        this.currentUser$ = this._users.currentProfile$;
        this.dataSource = new CustomStore({
            key: 'id',
            load: () => this.loadProjects(),
            insert: (values) => this.insertProject(values),
            update: (key, values) => this.updateProject(values)
            // remove: (key) => {
            // TODO: This needs to not delete the project, but instead
            // it needs to "expire" the project.  Projects are "active"
            // if "now" is between start and end dates.  To "expire" a
            // project is to change the end date to be before "now" so
            // that it is no longer active.

            // We are waiting on Brendan to change the types of the start
            // and end dates to be more web-friendly than ticks.  At that
            // point we can finish this and implement the active column.
            // }
        });
        this.clients$.subscribe((clients) => {
            this._clientsCache = clients;
            this.dataSource.load();
        });
    }

    onToolbarPreparing(e) {
        const addBtn = e.toolbarOptions.items.find(
            (i) => i.name === 'addRowButton'
        );

        if (!addBtn) return;

        addBtn.showText = 'always';
        addBtn.options = {
            ...addBtn.options,
            icon: null,
            text: 'Create',
            type: 'success',
            stylingMode: 'contained'
        };
    }

    onEditorPreparing(event: {
        cancel: boolean;
        component: dxDataGrid;
        dataField: string;
        disabled: boolean;
        editorElement: HTMLElement;
        editorName: string;
        editorOptions: any;
        element: HTMLElement;
        model: any;
        parentType: string;
        readOnly: boolean;
        row: { isNewRow: boolean; rowType: string };
        rtlEnabled: boolean;
        setValue: (newValue, newText?) => any;
        updateValueTimeout: number;
        value: any;
        width: number;
    }) {
        // Only allow client dropdown when adding a new project so we can associate
        // the project to the right client.  Updating clients with changed projectIDs
        // arrays doesn't persist the project association changes at this time.  This
        // component is written to handle those changes if we change our minds about
        // allowing projects to be switched to a different client.
        const inserting =
            event.row && event.row.rowType === 'data' && event.row.isNewRow;
        if (event.dataField === 'clientId' && !inserting) {
            event.editorOptions.disabled = true;
        }
    }

    onRowUpdating(event: {
        cancel: boolean;
        component: dxDataGrid;
        element: HTMLElement;
        key: string;
        newData: Partial<Project>;
        oldData: Project;
    }) {
        this._oldProject = { ...event.oldData };
        this._newProject = { ...this._oldProject, ...event.newData };
        this._oldClientId = this._oldProject.clientId;
        this._newClientId = this._newProject.clientId;
    }

    logEvent(event: any, eventName: string) {
        // TODO: Review how to handle the editing events in the grid.  RowInserting vs. RowInserted is so that we
        // handle the actual saving to the server part in the RowInserting, and prevent RowInserted from firing
        // if something doesn't go right.  Please review here: https://js.devexpress.com/Documentation/ApiReference/UI_Components/dxDataGrid/Configuration/#onRowInserting
        if (eventName === 'RowInserted') {
            this._toasts.success('Project added successfully');
        } else if (eventName === 'RowUpdated') {
            this._toasts.success('Project updated successfully');
        }
        console.log(eventName, event);
    }

    private async loadProjects(): Promise<Project[]> {
        const clients =
            this._clientsCache === null ||
            this._clientsCache === undefined ||
            this._clientsCache.length <= 0
                ? await this._pia.getClients().toPromise()
                : this._clientsCache;
        const projects = await this._pia.getProjects().toPromise();
        return projects.map((project) => ({
            ...project,
            client: clients.find((c) => c.projectIDs?.includes(project.id))
        }));
    }

    private async insertProject(values: any) {
        const { client, ...project } = values;
        project.id = uuidv4();
        const response = await this._pia
            .createProject(project, project.clientId)
            .toPromise();

        return response;
    }

    private async updateProject(values: any) {
        const { client, ...newProjectData } = values;

        // Start with no-op client update calls...
        let removeClient$: Observable<PIAResponse> = of(null);
        let addClient$: Observable<PIAResponse> = of(null);

        if (client) {
            // Find the two clients that need to update
            const clientToRemoveProjectFrom = this._clientsCache.find(
                (c) => c.id === this._oldClientId
            );
            const clientToAddProjectTo = this._clientsCache.find(
                (c) => c.id === this._newClientId
            );

            // Get the projectId we need to change the associations with clients for
            const projectId = this._oldProject.id;

            // Prepare the client project removal update call
            const newRemovedClient = cloneDeep(clientToRemoveProjectFrom);
            newRemovedClient.projectIDs.splice(
                clientToRemoveProjectFrom.projectIDs.indexOf(projectId),
                1
            );
            removeClient$ = this._pia.updateClient(
                clientToRemoveProjectFrom,
                newRemovedClient
            );

            // Prepare the client project addition update call
            const newAddedClient = cloneDeep(clientToAddProjectTo);
            newAddedClient.projectIDs.push(projectId);
            addClient$ = this._pia.updateClient(
                clientToAddProjectTo,
                newAddedClient
            );
        }

        // Start with a no-op project update call...
        let projectUpdate$: Observable<PIAResponse> = of(null);

        // If edits were made to project properties...
        if (newProjectData && Object.keys(newProjectData).length) {
            // Prepare the project update call

            projectUpdate$ = this._pia.updateProject(
                this._oldProject,
                this._newProject
            );
        }

        // Return a promise of both update calls completing
        let clientResponse = await removeClient$.toPromise();
        if (clientResponse?.error) {
            throw new Error(clientResponse.error);
        }
        clientResponse = await addClient$.toPromise();
        if (clientResponse?.error) {
            throw new Error(clientResponse.error);
        }
        const projectResponse = await projectUpdate$.toPromise();
        if (projectResponse?.error) {
            throw new Error(projectResponse.error);
        }
        return projectResponse;
    }
}
