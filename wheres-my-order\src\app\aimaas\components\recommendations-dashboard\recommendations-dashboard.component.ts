import { Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import {
    DxChartComponent,
    DxDataGridComponent,
    DxPieChartComponent
} from 'devextreme-angular';
import { exportDataGrid } from 'devextreme/excel_exporter';
import { Workbook } from 'exceljs';
import { saveAs } from 'file-saver';
import { createSafeResizeObserver } from '../../../shared/helpers';
import { AnomaliesRecommendations } from '../../models/anomalies-recommendations';
import { InspectionAnomaliesComponent } from '../inspection-anomalies/inspection-anomalies.component';
interface AnomalyState {
    Open: number;
    Closed: number;
}

@Component({
    selector: 'app-recommendations-dashboard',
    templateUrl: './recommendations-dashboard.component.html',
    styleUrls: ['./recommendations-dashboard.component.scss']
})
export class RecommendationsDashboardComponent implements OnInit {
    @ViewChild('chart') chart: DxChartComponent;
    @ViewChild('pie') pie: DxPieChartComponent;
    @ViewChild('OpenClosedChart') OpenClosedChart: DxChartComponent;
    @ViewChild('P6AnomaliesGroupedByStatusChart')
    P6AnomaliesGroupedByStatusChart: DxChartComponent;
    @ViewChild('AnomalyOperationChart')
    AnomalyOperationChart: DxChartComponent;
    @ViewChild(DxDataGridComponent, { static: false })
    dataGrid: DxDataGridComponent;
    @ViewChild('anomaliesComponent')
    anomaliesComponent: InspectionAnomaliesComponent;

    submissionPopupVisible: boolean = false;
    priorityMetrics: any;
    pieChartData: any;
    chartData: any[] = [];
    operationData: any[] = [];
    anomalyOperationData: any[] = [];
    anomaliesData: any;
    initialAnomaly: AnomaliesRecommendations = null;
    submissionPopupTitle: string = 'Anomaly Update';
    filterValue: Array<any>;
    dataSource: any[];
    agedMetric: number = 0;
    dropdownItems: string[] = ['Days', 'Years'];
    selectedDropdownValue: string = 'Days';
    timeInputValue: number = 0;
    monthlyDataMap: Map<string, any[]>;
    yearlyData: any[];
    colors: string[] = ['#1db2f5', '#f5564a', '#8051c6'];
    isFirstLevel: boolean = true;
    selectedYear: number | null = null;
    anamoliesPopupData: any;
    anomalyPopupVisible: boolean = false;
    private _observer: ResizeObserver;
    @Input() set anamolies(value: any) {
        this.anomaliesData = value;
        this.isFirstLevel = true;
        this.prepareAnomalyOperationData(value);
        this.priorityMetrics = this.countAnomalyPriorities(value);
        this.prepareChartData(value);
        this.preparePieChartData(value);
        this.updateDashboard;
        this.calculateAgedMetric(this.anomaliesData);
    }

    constructor(
        private readonly _router: Router,
        private readonly _hostElement: ElementRef
    ) {}
    ngOnInit(): void {
        this._observer = createSafeResizeObserver(() => {
            this.renderCharts();
        });
        this._observer.observe(this._hostElement.nativeElement);
    }
    async onExporting(event) {
        const workbook = new Workbook();
        const worksheet = workbook.addWorksheet('Anomaly');
        await exportDataGrid({
            component: event.component,
            worksheet
        });
        const buffer: BlobPart = await workbook.xlsx.writeBuffer();
        saveAs(
            new Blob([buffer], { type: 'application/octet-stream' }),
            'Anomaly.xlsx'
        );
    }
    calculateAgedMetric(value: any) {
        const today = new Date();
        const timeMultiplier = this.selectedDropdownValue === 'Days' ? 1 : 365;
        const timeInMilliseconds =
            this.timeInputValue * timeMultiplier * 24 * 60 * 60 * 1000;

        this.agedMetric = value.filter((anomaly) => {
            const detectionDate = new Date(anomaly.detectiondate);
            const priority = anomaly.anomalypriority;
            const priorityNumber = parseInt(priority, 10);

            if (
                priorityNumber >= 4 &&
                priorityNumber <= 6 &&
                anomaly.resolutionstate != null
            ) {
                const daysOpen = today.getTime() - detectionDate.getTime();
                return (
                    daysOpen > timeInMilliseconds &&
                    anomaly.resolutionstate?.toLowerCase() === 'recommended'
                );
            }
            return false;
        }).length;
    }

    onDropdownValueChanged(event) {
        this.selectedDropdownValue = event.value;
        this.calculateAgedMetric(this.anomaliesData);
    }

    calculateSerialNumber(rowData: any, rowIndex: number): number {
        return rowIndex + 1;
    }
    clientSubmissionTitleValueChange(e: any) {
        this.submissionPopupTitle = e;
    }
    clientDataFormSubmitted(e: any) {
        this.submissionPopupVisible = false;
    }
    anomalyCellTemplate = (cellElement, cellInfo) => {
        const anomalyNumber = cellInfo.value ? cellInfo.value : '';
        const link = document.createElement('a');
        link.href = '#';
        link.innerText = anomalyNumber;
        link.onclick = (event) => {
            event.preventDefault();
            this.onAnomalyClick(cellInfo.data);
        };
        cellElement.appendChild(link);
    };
    onAnomalyClick(data: AnomaliesRecommendations) {
        this.initialAnomaly = data;
        this.clientSubmitDataOnclick('fromanomaly');
    }
    prepareAnomalyOperationData(value: any) {
        const currentYear = new Date().getFullYear();
        const yearRange = Array.from({ length: 7 }, (_, i) =>
            (currentYear - 6 + i).toString()
        ); // Generate ascending year range
        const monthNames = [
            'Jan',
            'Feb',
            'Mar',
            'Apr',
            'May',
            'Jun',
            'Jul',
            'Aug',
            'Sep',
            'Oct',
            'Nov',
            'Dec'
        ];

        const dataMap = new Map(
            yearRange.map((year) => [
                year,
                Array.from({ length: 12 }, (_, month) => ({
                    year,
                    month,
                    openAnomalies: 0,
                    closedAnomalies: 0,
                    operationCount: 0,
                    monthYear: `${monthNames[month]} ${year}`,
                    operationId: []
                }))
            ])
        );

        value.forEach((anomaly) => {
            const detectionDate = new Date(anomaly.detectiondate);
            const operationDate = new Date(anomaly.operationdate);
            const detectionYear = detectionDate.getFullYear().toString();
            const detectionMonth = detectionDate.getMonth(); // 0-11
            const operationYear = operationDate.getFullYear().toString();
            const operationMonth = operationDate.getMonth(); // 0-11
            const status = anomaly.resolutionstate?.toLowerCase();

            if (dataMap.has(detectionYear)) {
                const yearData = dataMap.get(detectionYear);
                if (status === 'recommended') {
                    yearData[detectionMonth].openAnomalies++;
                } else if (status === 'resolved' || status === 'rejected') {
                    yearData[detectionMonth].closedAnomalies++;
                }
            }

            if (dataMap.has(operationYear)) {
                const yearData = dataMap.get(operationYear);
                if (
                    !yearData[operationMonth].operationId.includes(
                        anomaly.operationid
                    )
                ) {
                    yearData[operationMonth].operationCount++;
                    yearData[operationMonth].operationId.push(
                        anomaly.operationid
                    );
                }
            }
        });

        this.anomalyOperationData = [];
        dataMap.forEach((months, year) => {
            months.forEach((monthData) => {
                this.anomalyOperationData.push(monthData);
            });
        });

        this.yearlyData = yearRange.map((year) => ({
            year,
            openAnomalies: dataMap
                .get(year)
                .reduce((acc, monthData) => acc + monthData.openAnomalies, 0),
            closedAnomalies: dataMap
                .get(year)
                .reduce((acc, monthData) => acc + monthData.closedAnomalies, 0),
            operationCount: dataMap
                .get(year)
                .reduce((acc, monthData) => acc + monthData.operationCount, 0)
        }));

        this.monthlyDataMap = dataMap;
        this.dataSource = this.yearlyData;
    }

    onButtonClick() {
        if (!this.isFirstLevel) {
            this.isFirstLevel = true;
            this.dataSource = this.yearlyData;
            this.selectedYear = null;
        }
    }
    onPointClick(e) {
        if (this.isFirstLevel) {
            this.isFirstLevel = false;
            this.selectedYear = e.target.originalArgument;
            this.dataSource =
                this.monthlyDataMap.get(this.selectedYear.toString()) || [];
        } else {
            let year = e.target.data.year;
            let month = e.target.data.month;
            const monthNames = [
                'Jan',
                'Feb',
                'Mar',
                'Apr',
                'May',
                'Jun',
                'Jul',
                'Aug',
                'Sep',
                'Oct',
                'Nov',
                'Dec'
            ];
            const months = monthNames.indexOf(month);
            let oppid = [];
            if (this.monthlyDataMap.has(year)) {
                const yearData = this.monthlyDataMap.get(year);
                const monthData = yearData[month];
                oppid = monthData.operationId;
            }

            let startOfYears = new Date(year, month, 1);
            let endOfYears = new Date(year, month + 1, 0);

            if (e.target._label._data.seriesName == 'Inspection') {
                this.filterValue = [
                    ['inspectiondate', '>=', startOfYears],
                    ['inspectiondate', '<=', endOfYears],
                    ['planoperationid', 'anyof', oppid]
                ];
                const currentFilter = this.filterValue;
                this._router.navigate(['/aimaas/inspection-drilldown'], {
                    state: {
                        data: { currentFilter },
                        breadCrumbLabel:
                            'Anomaly and Inspection Count Over Time'
                    }
                });
            } else if (e.target._label._data.seriesName == 'Open Anomalies') {
                this.filterValue = [
                    ['detectiondate', '>=', startOfYears],
                    'and',
                    ['detectiondate', '<=', endOfYears],
                    'and',
                    ['resolutionstate', '=', 'recommended'],
                    'and'
                ];
                const currentFilter = this.filterValue;
                this.drillDown(
                    currentFilter,
                    'Anomaly and Inspection Count Over Time'
                );
            } else {
                this.filterValue = [
                    ['detectiondate', '>=', startOfYears],
                    ['detectiondate', '<=', endOfYears],
                    ['resolutionstate', 'anyof', ['resolved', 'rejected']]
                ];
                const currentFilter = this.filterValue;
                this.drillDown(
                    currentFilter,
                    'Anomaly and Inspection Count Over Time'
                );
            }
        }
    }

    yearLabelFormat(value: any): string {
        return Math.floor(value).toString();
    }
    clientSubmitDataOnclick(e: string) {
        if (e === 'frombuttonclick') {
            this.initialAnomaly = null;
        }
        this.submissionPopupVisible = !this.submissionPopupVisible;
    }
    onChartClick(e) {
        this.anomalyPopupVisible = true;
        const point = e.target;
        if (point) {
            const data = point.data;
            const stateType = point.series.name;
            if (
                stateType != undefined &&
                stateType == 'Recommended' &&
                stateType != null
            ) {
                this.filterValue = [
                    ['resolutionstate', '=', 'Recommended'],
                    ['anomalytype', '=', data.type],
                    ['anomalypriority', 'contains', '6']
                ];
            } else if (
                stateType != undefined &&
                stateType == 'Rejected' &&
                stateType != null
            ) {
                this.filterValue = [
                    ['resolutionstate', '=', 'Rejected'],
                    ['anomalytype', '=', data.type],
                    ['anomalypriority', 'contains', '6']
                ];
            } else if (
                stateType != undefined &&
                stateType == 'Resolved' &&
                stateType != null
            ) {
                this.filterValue = [
                    ['resolutionstate', '=', 'Resolved'],
                    ['anomalytype', '=', data.type],
                    ['anomalypriority', 'contains', '6']
                ];
            }
        }
        this.drillDown(
            this.filterValue,
            'Types of P6 Anomalies, grouped by status (all time)'
        );
    }
    updateDashboard() {
        this.renderCharts();
    }
 
    

    renderCharts() {
        // this.chart?.instance?.render();
        // this.pie?.instance?.render();
        this.AnomalyOperationChart?.instance?.render();
        this.P6AnomaliesGroupedByStatusChart?.instance?.render();
        this.OpenClosedChart?.instance?.render();
    }
    countAnomalyPriorities(records) {
        const priorityCounts = { 4: 0, 5: 0, 6: 0 };

        for (const record of records) {
            if (
                record.resolutionstate !== 'Resolved' &&
                record.resolutionstate !== 'Rejected'
            ) {
                const priorityNumber = parseInt(
                    record.anomalypriority.split(' - ')[0],
                    10
                );
                if (priorityCounts.hasOwnProperty(priorityNumber)) {
                    priorityCounts[priorityNumber]++;
                }
            }
        }
        return priorityCounts;
    }

    customizeLabel(arg) {
        return arg.percentText;
    }
    preparePieChartData(value: any) {
        const chartData = [
            { type: 'P4', Open: 0, Closed: 0 },
            { type: 'P5', Open: 0, Closed: 0 },
            { type: 'P6', Open: 0, Closed: 0 }
        ];

        value.forEach((anomaly) => {
            const priority = anomaly.anomalypriority;
            if (priority != null) {
                const priorityNumber = parseInt(priority.split(' - ')[0], 10);
                if (
                    priorityNumber >= 4 &&
                    priorityNumber <= 6 &&
                    anomaly.resolutionstate != null
                ) {
                    const status = anomaly.resolutionstate.toLowerCase();
                    const priorityIndex = priorityNumber - 4;
                    if (status === 'recommended') {
                        chartData[priorityIndex].Open++;
                    } else if (status === 'resolved' || status === 'rejected') {
                        chartData[priorityIndex].Closed++;
                    }
                }
            }
        });

        this.pieChartData = chartData;
    }
    onPieChartClick(e) {
        this.anomalyPopupVisible = true;
        const point = e.target;
        if (point) {
            const priority = point.argument;
            const status = point.series.name;
            this.applyFilterToDataGrid(
                status,
                priority,
                'P4, P5, P6 Priorities OPEN vs CLOSED'
            );
        }
    }
    private drillDown(currentFilter: any[], breadCrumbLabel: string) {
        this._router.navigate(['/aimaas/anomaly-drilldown'], {
            state: { data: { currentFilter }, breadCrumbLabel: breadCrumbLabel }
        });
    }
    applyFilterToDataGrid(
        status: string,
        priority: string,
        breadcrumb: string
    ) {
        const filterValues =
            status === 'Open'
                ? 'Recommended'
                : status === 'Closed'
                ? ['Resolved', 'Rejected']
                : '';
        var priorityFilter;
        if (priority == 'P4') {
            priorityFilter = ['anomalypriority', 'contains', '4'];
        } else if (priority == 'P5') {
            priorityFilter = ['anomalypriority', 'contains', '5'];
        } else {
            priorityFilter = ['anomalypriority', 'contains', '6'];
        }
        if (Array.isArray(filterValues)) {
            this.filterValue = [
                [
                    ['resolutionstate', 'contains', filterValues[0]],
                    'or',
                    ['resolutionstate', 'contains', filterValues[1]]
                ],
                'and',
                priorityFilter
            ];
        } else {
            this.filterValue = [
                ['resolutionstate', '=', filterValues],
                'and',
                priorityFilter
            ];
        }
        this.drillDown(this.filterValue, breadcrumb);
    }

    prepareChartData(value: any) {
        const groupedData = {};
        value.forEach((anomaly) => {
            const type = anomaly.anomalytype;
            const status = anomaly.resolutionstate;
            const priority = anomaly.anomalypriority;
            if (priority != null) {
                var priorityNumber = parseInt(priority.split(' - ')[0], 10);
                if (priorityNumber == 6 && anomaly.resolutionstate != null) {
                    if (!groupedData[type]) {
                        groupedData[type] = { type };
                    }

                    if (!groupedData[type][status]) {
                        groupedData[type][status] = 0;
                    }

                    groupedData[type][status]++;
                }
            }
        });
        this.chartData = Object.values(groupedData);
    }
    customizeTooltip = ({ valueText, seriesName }) => ({
        text: `${seriesName} : ${valueText}`
    });
    getColor(info: any): string {
        return info.data.color;
    }
}
