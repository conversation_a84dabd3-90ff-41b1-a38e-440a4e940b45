import {
    Component,
    EventEmitter,
    Input,
    OnDestroy,
    OnInit,
    Output,
    ViewChild
} from '@angular/core';
import {
    DxFileUploaderComponent,
    DxSelectBoxComponent
} from 'devextreme-angular';
import * as moment from 'moment-timezone';

import { UserProfile } from '../../../profile/models';
import {
    ToastNotificationService,
    ToastType,
    UsersService
} from '../../../shared/services';
import { AnomaliesRecommendations, AssetInspection } from '../../models';
import { CredoSoftService } from '../../services';

interface FileObject {
    filename: string;
    filetype: string;
    filedata: string;
}
@Component({
    selector: 'app-client-data-submission',
    templateUrl: './client-data-submission.component.html',
    styleUrls: ['./client-data-submission.component.scss']
})
export class ClientDataSubmissionComponent implements OnInit, OnDestroy {
    availableAssets: any;
    commentBoxLabel: string = 'Update Comment per Client';
    @Output() clientSubmissionTitleValueChange = new EventEmitter<any>();
    @Output() formSubmittedValueChange = new EventEmitter<any>();
    anomalies: AnomaliesRecommendations[] = [];
    @Input() initialAnomaly: AnomaliesRecommendations;
    @ViewChild('selectionBoxClientSubmission', { static: false })
    selectionBoxClientSubmission: DxSelectBoxComponent;
    @ViewChild('assetSelectionBox', { static: false })
    assetSelection: DxSelectBoxComponent;
    @ViewChild('selectBox', { static: false })
    selectBox: DxSelectBoxComponent;
    @ViewChild('selectionServiceTypeBox', { static: false })
    serviceTypeSelection: DxSelectBoxComponent;
    selectedAnomaly: any = {};
    selectedAsset: any = {};
    anomalyProposedRecommendation: string;
    files: any;
    checkAsset: AnomaliesRecommendations[];
    currentUser: UserProfile;
    selectedDocuments: FileObject[] = [];
    clientcloseddate: any;
    loadingVisible = false;
    availableInspections: AssetInspection[] = [];
    currentInspections: any = [];
    selectedInspections: any;
    isCommentsMandatory: boolean = false;
    availableAnomalyAssets: any[] = [];
    clientSubmissionDataOptions = [
        'Anomaly Update',
        'Document Upload',
        'Request Service'
    ];
    serviceTypeDataOptions = [
        'NDE',
        'API',
        'Engineering',
        // 'Inspection Service',
        'Other'
    ];
    comment: string = '';
    @ViewChild('fileUploader', { static: false })
    fileUploader: DxFileUploaderComponent;
    allowedToSubmit: boolean = false;

    constructor(
        private readonly credoService: CredoSoftService,
        private readonly _users: UsersService,
        private readonly _toasts: ToastNotificationService
    ) {}
    selectionBoxClientSubmissionValueChange(e: any) {
        if (e.value === 'Anomaly Update') {
            this.clientSubmissionTitleValueChange.emit(
                e.value +
                    (this.selectedAnomaly.anomaly
                        ? ' For : ' + this.selectedAnomaly.anomaly
                        : '')
            );
        } else {
            this.clientSubmissionTitleValueChange.emit(e.value);
        }
        if (e.value == 'Anomaly Update') {
            this.commentBoxLabel = 'Update Comment per Client';
            this.isCommentsMandatory = false;
        } else if (e.value == 'Request Service') {
            this.commentBoxLabel = 'Comment about Service Request';
            this.isCommentsMandatory = true;
        } else if (e.value == 'Document Upload') {
            this.commentBoxLabel = 'Comment';
            this.isCommentsMandatory = false;
        }
    }

    onAnomalyChanged(e: any) {
        if (e.value) {
            this.selectedAnomaly = e.value;
            let selectedAsset = this.availableAssets.filter(
                (asset) => asset.id === this.selectedAnomaly?.assetid
            );
            this.selectedAsset = selectedAsset[0];
        } else {
            this.selectedAnomaly = {};
        }
        this.clientSubmissionTitleValueChange.emit(
            'Anomaly Update' +
                (this.selectedAnomaly.anomaly
                    ? ' For : ' + this.selectedAnomaly.anomaly
                    : '')
        );
    }
    onItemClick(e: any) {
        const isEmpty = (obj) => JSON.stringify(obj) === '{}';
        if (isEmpty(this.selectedAsset)) {
            this.checkAsset = this.anomalies;
        }
    }
    onAssetChanged1(e: any) {
        const isEmpty = (obj) => JSON.stringify(obj) === '{}';
        const objCheck = {};
        if (e.event != undefined) {
            this.selectedAnomaly = {};
            this.selectedAnomaly.assetid = this.selectedAsset.id;
        }
        if (!isEmpty(this.selectedAsset) && this.selectedAsset != null) {
            this.checkAsset = this.anomalies.filter(
                (anomaly) => anomaly.assetid === this.selectedAsset.id
            );
        } else {
            this.selectedAnomaly = {};
            this.checkAsset = this.anomalies;
        }
    }
    onFileSelect(e: any) {
        if (e.value.length !== 0) {
            for (let file of e.value) {
                this.convertFileToBase64(file).then((base64: string) => {
                    this.selectedDocuments.push({
                        filename: file.name,
                        filetype: file.type,
                        filedata: base64
                    });
                });
                this.fileUploader.instance.option('value', []);
            }
        }
    }
    removeFile(e: any) {
        const fileName = e.row.data.name;
        this.selectedDocuments = this.selectedDocuments.filter(
            (file) => file.filename !== fileName
        );
    }
    private convertFileToBase64(file: File): Promise<string> {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.readAsDataURL(file);
            reader.onload = () => {
                const base64String = (reader.result as string).split(',')[1];
                resolve(base64String);
            };
            reader.onerror = (error) => reject(error);
        });
    }
    submitForm() {
        let _submissionData: any = null;
        const centralTimeWithTimeZone = moment
            .tz(new Date(), 'America/Chicago')
            .format('MM/DD/YYYY hh:mm A z');
        if (this.selectionBoxClientSubmission?.value === 'Document Upload') {
            if (
                this.assetSelection.value == null ||
                this.assetSelection.value == undefined ||
                this.selectedDocuments.length === 0 ||
                this.assetSelection?.value.length == 0
            ) {
                this._toasts.show(
                    'Missing required fields',
                    'Please fill the required fields',
                    ToastType.error,
                    3000
                );

                return;
            } else {
                _submissionData = {
                    ClientFacility: this.assetSelection.value[0]?.locationname,
                    locationid: this.assetSelection.value[0]?.locationid,
                    submissiontype: this.selectionBoxClientSubmission?.value,
                    anomalyinfo: null,
                    // comment: this.comment.toString(),
                    comment: this.stripHtmlTags(this.comment),
                    assetinfo: this.assetSelection?.value?.map((asset) => ({
                        assetid: asset.id,
                        assetdescription: asset.description,
                        assetname: asset.assetid
                    })),
                    createddate: centralTimeWithTimeZone.toString(),
                    senderinfo: {
                        emailid: this.currentUser?.id,
                        givenname: this.currentUser?.givenName,
                        surname: this.currentUser?.surname
                    },
                    clientcloseddate: null,
                    documents: this.selectedDocuments,
                    servicetype: 'upload'
                };
            }
        } else if (
            this.selectionBoxClientSubmission?.value === 'Anomaly Update'
        ) {
            if (
                this.selectedAnomaly == null ||
                this.selectedAnomaly == undefined ||
                !this.selectedAnomaly.anomaly ||
                this.clientcloseddate == null ||
                this.clientcloseddate == undefined ||
                (this.selectedDocuments.length > 0 &&
                    (this.comment === null || this.comment.trim() === ''))
            ) {
                this._toasts.show(
                    'Missing required fields',
                    'Please fill required fields',
                    ToastType.error,
                    3000
                );
                return;
            } else {
                _submissionData = {
                    ClientFacility: this.selectedAsset?.locationname,
                    locationid: this.selectedAsset?.locationid,
                    submissiontype: this.selectionBoxClientSubmission?.value,
                    anomalyinfo: {
                        anomalynumber: this.selectedAnomaly?.anomaly,
                        anomalytype: this.selectedAnomaly?.anomalytype,
                        anomalyinspectionoperation:
                            this.selectedAnomaly?.inspectionoperationinstance,
                        anomalypriority: this.selectedAnomaly?.anomalypriority
                    },
                    comment: this.stripHtmlTags(this.comment),
                    assetinfo: [
                        {
                            assetid: this.selectedAsset.id,
                            assetdescription: this.selectedAsset.description,
                            assetname: this.selectedAsset.assetid
                        }
                    ],
                    createddate: centralTimeWithTimeZone.toString(),
                    senderinfo: {
                        emailid: this.currentUser?.id,
                        givenname: this.currentUser?.givenName,
                        surname: this.currentUser?.surname
                    },
                    clientcloseddate: moment
                        .tz(this.clientcloseddate, 'America/Chicago')
                        .format('MM/DD/YYYY hh:mm A z'),
                    documents: this.selectedDocuments,
                    servicetype: 'upload'
                };
            }
        } else {
            if (
                this.serviceTypeSelection.value == undefined ||
                this.serviceTypeSelection.value == null ||
                this.comment == null ||
                this.comment == undefined ||
                this.comment.trim() === '' ||
                !this.comment
            ) {
                this._toasts.show(
                    'Missing required fields',
                    'Please fill the required fields',
                    ToastType.error,
                    3000
                );
                return;
            } else {
                _submissionData = {
                    ClientFacility: this.assetSelection.value[0]?.locationname,
                    locationid: this.assetSelection.value[0]?.locationid,
                    submissiontype: this.selectionBoxClientSubmission?.value,
                    anomalyinfo: null,
                    comment: this.stripHtmlTags(this.comment),
                    assetinfo: this.assetSelection?.value?.map((asset) => ({
                        assetid: asset.id,
                        assetdescription: asset.description,
                        assetname: asset.assetid
                    })),
                    createddate: centralTimeWithTimeZone.toString(),
                    senderinfo: {
                        emailid: this.currentUser?.id,
                        givenname: this.currentUser?.givenName,
                        surname: this.currentUser?.surname
                    },
                    clientcloseddate: null,
                    documents: this.selectedDocuments,
                    servicetype: this.serviceTypeSelection?.value
                };
            }
        }
        this.loadingVisible = true;
        this.credoService.uploadSubmissions(_submissionData).subscribe(
            (result) => {
                this._toasts.show('', result?.message, ToastType.success, 3000);
                this.loadingVisible = false;
                this.formSubmittedValueChange.emit('form submitted');
            },
            (err) => {
                this._toasts.show('Error', err.error, ToastType.error, 3000);
                this.loadingVisible = false;
                console.error(err, 'error');
            }
        );
    }
    stripHtmlTags(htmlString: string): string {
        return htmlString.replace(/<p>/i, '').replace(/<\/p>/i, '');
    }
    formatFileType(data) {
        switch (data.filetype) {
            case 'application/pdf':
                return 'PDF';
            case 'image/png':
                return 'PNG';
            case 'text/plain':
                return 'TEXT';
            case 'image/jpeg':
                return 'JPEG';
            case 'image/jpeg':
                return 'JPEG';
            case 'application/kswps':
                return 'WORD';
            case 'application/kset':
                return 'EXCEL';
            case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
                return 'WORD';
            default:
                return data.filetype;
        }
    }
    convertHtmlToText(html: string): string {
        if (html == null) {
            return '';
        }
        const doc = new DOMParser().parseFromString(html, 'text/html');
        return doc.documentElement.textContent ?? ' ';
    }
    getDateOnly(dateTimeStr: string): string {
        if (!dateTimeStr) {
            return '';
        }
        const date = new Date(dateTimeStr);
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0'); // Months are zero-indexed
        const day = date.getDate().toString().padStart(2, '0');

        return `${day}-${month}-${year}`;
    }
    ngOnInit(): void {
        const locationid = localStorage.getItem('selectedSite');
        this.credoService.anomalies$.subscribe(async (anomaly: any) => {
            if (locationid !== null || locationid !== undefined) {
                this.anomalies = anomaly.filter(
                    (asset) => asset.locationid == Number(locationid)
                );
            } else this.anomalies = anomaly;
            this.credoService.assets$.subscribe(async (assests: any) => {
                if (locationid !== null || locationid !== undefined) {
                    this.availableAssets = assests.filter(
                        (asset) => asset.locationid == Number(locationid)
                    );
                } else this.availableAssets = assests;
                this.availableAnomalyAssets = this.availableAssets?.filter(
                    (asset) => {
                        return this.anomalies?.some(
                            (anomaly) => anomaly.assetid == asset.id
                        );
                    }
                );
            });
        });

        this.credoService.inspections$.subscribe(async (inspection: any) => {
            this.availableInspections = inspection;
        });
        this._users.currentProfile$.subscribe((user: any) => {
            this.currentUser = user;
            const roles = user?.roles?.map((r) => r.toLowerCase()) || [];
            this.allowedToSubmit =
                roles.includes('app:admin') ||
                roles.includes('aimaas:admin') ||
                roles.includes('aimaas:all') ||
                roles.includes('aimaas:edit') ||
                roles.includes('aimaas:demo')
        });
        if (this.initialAnomaly) {
            this.selectedAnomaly = this.initialAnomaly;
            let selectedAsset = this.availableAssets.filter(
                (asset) => asset.id === this.selectedAnomaly?.assetid
            );
            this.selectedAsset = selectedAsset[0];
            this.clientSubmissionTitleValueChange.emit(
                'Anomaly Update' +
                    (this.selectedAnomaly.anomaly
                        ? ' For : ' + this.selectedAnomaly.anomaly
                        : '')
            );
        } else {
            this.clientSubmissionTitleValueChange.emit('Anomaly Update');
        }
    }
    ngOnDestroy(): void {
        this.initialAnomaly = null;
    }
}
