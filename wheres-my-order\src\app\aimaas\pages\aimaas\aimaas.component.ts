import {
    Component,
    ElementRef,
    OnD<PERSON>roy,
    OnInit,
    ViewChild
} from '@angular/core';
import { BehaviorSubject, combineLatest, Observable } from 'rxjs';
import { shareReplay } from 'rxjs/operators';
import { UserProfile } from '../../../profile/models';
import { createSafeResizeObserver } from '../../../shared/helpers';
import { ToastNotificationService, UsersService } from '../../../shared/services';
import {
    EquipmentDashboardComponent,
    InspectionsDashboardComponent
} from '../../components';
import { RecommendationsDashboardComponent } from '../../components/recommendations-dashboard/recommendations-dashboard.component';
import {
    AnomaliesRecommendations,
    Asset,
    AssetManagementSite
} from '../../models';
import { SiteLabelPipe } from '../../pipes';
import { CredoSoftService } from '../../services';
import { UtilsService } from '../../services/utils.service';

@Component({
    selector: 'app-aimaas',
    templateUrl: './aimaas.component.html',
    styleUrls: ['./aimaas.component.scss']
})
export class AIMaaSComponent implements OnInit, OnDestroy {
    @ViewChild(InspectionsDashboardComponent)
    inspectionsDashboard: InspectionsDashboardComponent;
    @ViewChild(EquipmentDashboardComponent)
    equipmentDashboard: EquipmentDashboardComponent;
    @ViewChild(RecommendationsDashboardComponent)
    recommendationDashboard: RecommendationsDashboardComponent;
    selectionOption = [
        'Equipment Dashboard',
        'Inspection Dashboard',
        'Recommendations Dashboard'
    ];
    availableSites: AssetManagementSite[];
    selectedSite: AssetManagementSite;
    allInspections: any[];
    allAssets: any[];
    currentUser: UserProfile;
    allAnomalies: any[];
    isLoading: boolean;
    anomaliesData: any;
    initialAnomaly: AnomaliesRecommendations = null;
    submissionPopupTitle: string = 'Anomaly Update';
    submissionPopupVisible: boolean = false;
    selectedDashboard: string = 'Equipment Dashboard';
    private _observer: ResizeObserver;
    private _assetsForSite = new BehaviorSubject<Asset[]>([]);
    private _inspectionsForSite = new BehaviorSubject<any[]>([]);
    private _anamoliesForSite = new BehaviorSubject<AnomaliesRecommendations[]>(
        []
    );
    districtOptions: { id: string; name: string }[];
    districtvalues: { id: string; name: string }[];
    locationOptions: { id: string; name: string }[];
    districtToClientMap: Map<string, any[]>;
    clientToDistrictMap: Map<string, any[]>;
    districtToSiteMap: Map<string, any[]>;
    filteredCostCentreOptions: { id: string; name: string }[];
    clientOptions: { id: string; name: string }[];
    selectedCompany: any;
    selectedCostCentres: string;
    clientcostcentre: any;
    filteredSites: AssetManagementSite[];
    clientLocationData: any[];

    constructor(
        private readonly _credoSoft: CredoSoftService,
        private readonly _users: UsersService,
        private readonly _hostElement: ElementRef,
        private readonly _sitePipe: SiteLabelPipe,
        private readonly _toasts: ToastNotificationService,
        private utilsService: UtilsService
    ) { }

    get assetsForSite$(): Observable<Asset[]> {
        return this._assetsForSite.asObservable().pipe(shareReplay());
    }
    get anomaliesForSite$(): Observable<AnomaliesRecommendations[]> {
        return this._anamoliesForSite.asObservable().pipe(shareReplay());
    }

    get inspectionsForSite$(): Observable<any[]> {
        return this._inspectionsForSite.asObservable().pipe(shareReplay());
    }

    ngOnInit(): void {
        this.isLoading = true;
        this._observer = createSafeResizeObserver(() => {
            this.inspectionsDashboard?.renderCharts();
            this.equipmentDashboard?.renderCharts();
            this.recommendationDashboard?.renderCharts();
        });
        this._observer.observe(this._hostElement.nativeElement);

        combineLatest([
            this._credoSoft.assets$,
            this._credoSoft.assetManagementSites$,
            this._credoSoft.inspections$,
            this._users.currentProfile$,
            this._credoSoft.anomalies$,
            this._credoSoft.clientLocationData$
        ]).subscribe(
            ([
                assets,
                sites,
                inspections,
                currentUser,
                anomalies,
                clientLocationData
            ]) => {
                this.availableSites = sites;
                this.currentUser = currentUser;
                const roles = currentUser.roles.map((role) =>
                    role.toLowerCase()
                );
                this.clientLocationData = clientLocationData;
                if (!clientLocationData || clientLocationData.length === 0) {
                    console.warn('No client location data available');
                    this._toasts.error(
                        'No client data for selected district',
                        'Validation Error'
                    );
                    this.isLoading = false;
                    return;
                }
                if (roles?.includes('aimaas:demo')) {
                    this.clientcostcentre = clientLocationData?.filter(
                        (site) =>
                            site?.locationid == Number('635140707384299520')
                    );
                } else if (
                    !roles.includes('app:admin') &&
                    !roles.includes('aimaas:admin') &&
                    !roles.includes('aimaas:all') &&
                    !roles.includes('aimaas:district') &&
                    !roles.includes('aimaas:client') &&
                    currentUser.assetManagementSiteIds
                ) {
                    this.availableSites = this.availableSites.filter((site) =>
                        currentUser.assetManagementSiteIds.includes(
                            site.locationid
                        )
                    );
                    this.clientcostcentre = clientLocationData.filter((site) =>
                        currentUser.assetManagementSiteIds.includes(
                            site?.locationid
                        )
                    );
                } else this.clientcostcentre = clientLocationData;
                this.processLocations(this.clientcostcentre);
                this.allAnomalies = anomalies;
                this.allInspections = inspections;
                this.allAssets = assets;

                // --- Improved selection logic ---
                // Cost Centre
                let savedCostCentre = localStorage.getItem(
                    'selectedDistrict'
                );
                this.selectedCostCentres = savedCostCentre ?? null;

                // Always get districts for the selected cost centre
                let clientsForDistrict = this.utilsService.getUniqueBy(this.districtToClientMap.get(savedCostCentre) || [], 'id');
                this.districtToClientMap.get(savedCostCentre ?? null) || [];
                if (
                    !clientsForDistrict.length &&
                    this.districtvalues.length
                ) {
                    // fallback: all districts if none for cost centre
                    clientsForDistrict = this.utilsService.getUniqueBy(this.districtvalues, 'id');
                }
                this.clientOptions = [{ id: '', name: '' }, ...clientsForDistrict.sort((a, b) => a.name.localeCompare(b.name))];

                // District
                let savedClient = localStorage.getItem('selectedClient');
                this.selectedCompany = savedClient ?? null;

                // Sites
                let filteredLocations =
                    this.districtToSiteMap.get(savedClient ?? null) || [];
                if (filteredLocations.length === 0) {
                    // fallback: all sites
                    filteredLocations = Array.from(
                        this.districtToSiteMap.values()
                    ).flat();
                }
                this.availableSites = filteredLocations.map((site) => ({
                    locationid: site.locationid,
                    locationname: site.locationname,
                    clientid: site.clientid ? Number(site.clientid) : 0,
                    clientname: site.clientname || ''
                }));

                // Site
                let savedSite = localStorage.getItem('selectedSite');
                let selectedSiteObj = this.availableSites.find(
                    (site) => site.locationid == Number(savedSite)
                );
                if (!selectedSiteObj && this.availableSites.length > 0) {
                    selectedSiteObj = this.availableSites[0];
                    localStorage.setItem(
                        'selectedSite',
                        String(selectedSiteObj.locationid)
                    );
                }
                this.selectedSite = selectedSiteObj;

                // Dashboard
                this.selectedDashboard =
                    localStorage.getItem('selecteddashboard') ||
                    'Equipment Dashboard';

                this.isLoading = false;
            }
        );
    }

    ngOnDestroy(): void {
        //this._observer.unobserve(this._hostElement.nativeElement);
    }
    clientSubmitDataOnclick(e: string) {
        if (e === 'frombuttonclick') {
            this.initialAnomaly = null;
        }
        this.submissionPopupVisible = !this.submissionPopupVisible;
    }
    clientSubmissionTitleValueChange(e: any) {
        this.submissionPopupTitle = e;
    }
    clientDataFormSubmitted(e: any) {
        this.submissionPopupVisible = false;
    }

    customDisplayExpr = (site: AssetManagementSite): string => {
        if (site) return this._sitePipe.transform(site);
        else return '';
    };

    changeSite(selectedSite) {
        let _selectedSite = selectedSite.selectedItem;
        if (_selectedSite == null) {
            setTimeout(() => {
                this.selectedSite = this.availableSites[0];
                localStorage.setItem(
                    'selectedSite',
                    String(this.selectedSite.locationid)
                );
            }, 1);
        } else {
            this.selectedSite = selectedSite.selectedItem;
            localStorage.setItem(
                'selectedSite',
                String(this.selectedSite.locationid)
            );
        }
        this.broadcastSiteData();
    }

    dashboardChanged(event: any): void {
        this.selectedDashboard = event.value; // Ensure selectedDashboard is updated
        localStorage.setItem('selecteddashboard', this.selectedDashboard); // Save to localStorage

        // Trigger any additional logic if needed
        this.broadcastSiteData();
    }
    convertHtmlToText(html: string): string {
        if (html == null) {
            return ' ';
        }
        const doc = new DOMParser().parseFromString(html, 'text/html');
        return doc.documentElement.textContent ?? ' ';
    }
    private broadcastSiteData() {
        if (this.selectedSite) {
            this._inspectionsForSite.next(
                this.allInspections.filter(
                    (inspection) =>
                        inspection.locationid === this.selectedSite.locationid
                )
            );
            const anomaliesWithTextDescription = this.allAnomalies
                .filter(
                    (anomaly) =>
                        anomaly.locationid === this.selectedSite.locationid
                )
                .map((anomaly, index) => ({
                    ...anomaly,
                    anomalydescription: this.convertHtmlToText(
                        anomaly.anomalydescription
                    ),
                    serialnumber: index + 1
                }));

            this._anamoliesForSite.next(anomaliesWithTextDescription);
            this._assetsForSite.next(
                this.allAssets.filter(
                    (asset) => asset.locationid === this.selectedSite.locationid
                )
            );
        }
    }

    processLocations(data: any[]) {
        if (!data || !Array.isArray(data) || data.length === 0) {
            return;
        }

        const costCentreMap = new Map<string, string>();
        const clientMap = new Map<string, string>();
        const locationMap = new Map<string, string>(); // Add this for locations
        const districtToSiteMap = new Map<string, Set<any>>(); // New map for district -> locations

        const CostCentresToDistrictMap = new Map<string, Set<any>>();
        const DistrictsToCostCentreMap = new Map<string, Set<any>>();

        // Process the input data
        data.forEach((item) => {
            // Process cost centre information
            if (item?.costcenterid && item?.costcentername) {
                costCentreMap.set(
                    item.costcenterid,
                    item.costcentername.trim()
                );
            }

            // Process client information
            if (item?.clientid && item?.clientname) {
                clientMap.set(item.clientid, item.clientname.trim());
            }

            // Process location information
            if (item?.locationid && item?.locationname) {
                locationMap.set(item.locationid, item.locationname.trim());
            }

            // Cost Centre → Districts (Ensure uniqueness)
            if (item?.costcenterid && item?.clientid && item?.clientname) {
                if (!CostCentresToDistrictMap.has(item.costcenterid)) {
                    CostCentresToDistrictMap.set(item.costcenterid, new Set());
                }

                // Use a Map for uniqueness based on ID
                let uniqueDistricts = CostCentresToDistrictMap.get(
                    item.costcenterid
                );
                if (
                    !Array.from(uniqueDistricts).some(
                        (d) => d.id === item.clientid
                    )
                ) {
                    uniqueDistricts.add({
                        id: item.clientid,
                        name: item.clientname.trim()
                    });
                }
            }

            // District → Cost Centres
            if (item?.clientid && item?.costcenterid && item?.costcentername) {
                if (!DistrictsToCostCentreMap.has(item.clientid)) {
                    DistrictsToCostCentreMap.set(item.clientid, new Set());
                }
                DistrictsToCostCentreMap.get(item.clientid)?.add({
                    id: item.costcenterid,
                    name: item.costcentername.trim()
                });
            }
            if (item?.clientid && item?.locationid && item?.locationname) {
                if (!districtToSiteMap.has(item.clientid)) {
                    districtToSiteMap.set(item.clientid, new Set());
                }

                let uniqueLocations = districtToSiteMap.get(item.clientid);
                uniqueLocations.add({
                    locationid: item.locationid, // ✅ Ensure 'locationid' is used
                    locationname: item.locationname.trim()
                });
            }
        });

        // Transform Map data into sorted arrays for the dropdown options
        this.districtOptions = Array.from(costCentreMap.entries())
            .map(([id, name]) => ({ id, name }))
            .sort((a, b) => a.name.localeCompare(b.name));
        this.districtvalues = Array.from(clientMap.entries())
            .map(([id, name]) => ({ id, name }))
            .sort((a, b) => a.name.localeCompare(b.name));

        this.locationOptions = Array.from(locationMap.entries())
            .map(([id, name]) => ({ id, name }))
            .sort((a, b) => a.name.localeCompare(b.name));

        if (!this.districtToClientMap) {
            this.districtToClientMap = new Map<string, any[]>();
        }

        CostCentresToDistrictMap.forEach((districtSet, costCentreId) => {
            this.districtToClientMap.set(
                costCentreId,
                Array.from(districtSet)
            );
        });

        if (!this.clientToDistrictMap) {
            this.clientToDistrictMap = new Map<string, any[]>();
        }

        DistrictsToCostCentreMap.forEach((costCentreId, districtSet) => {
            this.clientToDistrictMap.set(
                districtSet,
                Array.from(costCentreId)
            );
        });

        this.districtToSiteMap = new Map<string, any[]>();
        districtToSiteMap.forEach((locationSet, districtId) => {
            this.districtToSiteMap.set(districtId, Array.from(locationSet));
        });
        const savedCostCentreId = localStorage.getItem('selectedDistrict') || '';
        const savedClientId = localStorage.getItem('selectedClient') || '';

        // Filter clientOptions based on savedCostCentreId (District)
        let clientOptions: any[] = [];
        if (savedCostCentreId && this.districtToClientMap.has(savedCostCentreId)) {
            clientOptions = this.utilsService.getUniqueBy(this.districtToClientMap.get(savedCostCentreId) || [], 'id');
        } else {
            clientOptions = this.utilsService.getUniqueBy(this.districtvalues, 'id');
        }
        this.clientOptions = [
            { id: '', name: '' },
            ...clientOptions.sort((a, b) => a.name.localeCompare(b.name))
        ];

        // Filter districtOptions based on savedClientId (Client)
        let districtOptions: any[] = [];
        if (savedClientId && this.clientToDistrictMap.has(savedClientId)) {
            districtOptions = this.utilsService.getUniqueBy(this.clientToDistrictMap.get(savedClientId).sort((a, b) => a.name.localeCompare(b.name)) || [], 'id');
        } else {
            districtOptions = this.utilsService.getUniqueBy(this.districtOptions.sort((a, b) => a.name.localeCompare(b.name)), 'id');
        }
        this.districtOptions = [
            { id: '', name: '' },
            ...districtOptions.sort((a, b) => a.name.localeCompare(b.name))
        ];

        // Filter locationOptions (sites) based on savedClientId
        if (savedClientId && this.districtToSiteMap.has(savedClientId)) {
            const filteredLocations = this.districtToSiteMap.get(savedClientId) || [];
            this.locationOptions = filteredLocations.sort((a, b) =>
                a.locationname.localeCompare(b.locationname)
            );
        }
    }

    onDistrictChange(event: any) {
        const selectedDistrict = event.value;
        this.selectedCostCentres = selectedDistrict;
        localStorage.setItem('selectedDistrict', selectedDistrict);

        if (selectedDistrict) {
            const uniqueClients = this.utilsService.getUniqueBy(
                Array.from(new Set(this.districtToClientMap.get(selectedDistrict) || [])),
                'id'
            );
            this.clientOptions = [{ id: '', name: '' }, ...uniqueClients.sort((a, b) => a.name.localeCompare(b.name))];

            setTimeout(() => {
                if (!this.selectedCompany) {
                    this.selectedCompany = this.clientOptions[0]?.id || null;
                }
            });
        } else {
            const allClients = this.utilsService.getUniqueBy(
                Array.from(this.districtToClientMap.values()).flat(),
                'id'
            );
            this.clientOptions = [{ id: '', name: '' }, ...allClients.sort((a, b) => a.name.localeCompare(b.name))];
        }

        localStorage.setItem('selectedClient', this.selectedCompany);

        this.filterSites();
    }

    onClientChange(event: any) {
        const selectedClient = event.value;
        this.selectedCompany = selectedClient;
        localStorage.setItem('selectedClient', selectedClient);

        if (selectedClient) {
            const districts = this.utilsService.getUniqueBy(
                Array.from(new Set(this.clientToDistrictMap.get(selectedClient) || [])),
                'id'
            );
            this.districtOptions = [{ id: '', name: '' }, ...districts.sort((a, b) => a.name.localeCompare(b.name))];
        } else {
            const allDistricts = this.utilsService.getUniqueBy(
                Array.from(this.clientToDistrictMap.values()).flat(),
                'id'
            );
            this.districtOptions = [{ id: '', name: '' }, ...allDistricts.sort((a, b) => a.name.localeCompare(b.name))];
        }

        this.filterSites();
    }

    filterSites() {
        const isValid = (val: any) =>
            val !== null && val !== undefined && val !== '' && val !== 'null' && val !== 'undefined';

        const district = isValid(this.selectedCostCentres);
        const client = isValid(this.selectedCompany);

        if (district && client) {
            this.updateAvailableSites(this.getFilteredSitesByBoth(this.selectedCostCentres, this.selectedCompany));
        } else if (district) {
            this.updateAvailableSites(this.getFilteredSitesByDistrict(this.selectedCostCentres));
        } else if (client) {
            this.updateAvailableSites(this.getFilteredSitesByClient(this.selectedCompany));
        } else {
            this.updateAvailableSites(Array.from(this.districtToSiteMap.values()).flat());
        }
    }

    updateAvailableSites(filteredLocations: any[]) {
        this.availableSites = filteredLocations;
        this.selectedSite = this.availableSites[0] || null;
        if (this.selectedSite) {
            localStorage.setItem('selectedSite', String(this.selectedSite.locationid));
        }
    }

    getFilteredSitesByDistrict(districtId: string): any[] {
        const clientIds = (this.districtToClientMap.get(districtId) || []).map(client => client.id);
        return clientIds.flatMap(id => this.districtToSiteMap.get(id) || []);
    }

    getFilteredSitesByClient(clientId: string): any[] {
        return this.districtToSiteMap.get(clientId) || [];
    }

    getFilteredSitesByBoth(districtId: string, clientId: string): any[] {
        const allSites = this.districtToSiteMap.get(clientId) || [];
        const validLocationIds = this.clientLocationData
            .filter(item => item.costcenterid === districtId && item.clientid == clientId)
            .map(item => item.locationid);
        return allSites.filter(loc => validLocationIds.includes(loc.locationid));
    }

    get canSubmit(): boolean {
        const rolesAllowed = [
            'aimaas:edit',
            'app:admin',
            'aimaas:admin',
            'aimaas:demo',
            'aimaas:all'
        ];

        const roles = this.currentUser?.roles?.map((role: string) =>
            role.toLowerCase()
        ) || [];

        return roles.some(role => rolesAllowed.includes(role));
    }
}
