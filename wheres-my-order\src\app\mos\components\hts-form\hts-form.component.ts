import {
    Component,
    EventEmitter,
    OnInit,
    Output,
    ViewChild
} from '@angular/core';
import { DxFormComponent, DxPopoverComponent } from 'devextreme-angular';
import { ScrollService } from '../../../shared/services/scroll.service';
import { HtsFormData, LineContent } from '../../models/hts-form-data';

@Component({
    selector: 'app-hts-form',
    templateUrl: './hts-form.component.html',
    styleUrls: ['./hts-form.component.scss']
})
export class HtsFormComponent implements OnInit {
    tempUnits = ['°F', '°C'] as const;

    htsFormData: HtsFormData = new HtsFormData({
        lineContents: [new LineContent()],
        lineContentCount: 1,
        selectedService: undefined,
        selectedFitting: undefined,
        selectedSection: undefined,
        packageType: undefined,
        designTemperature: null,
        mdmtTemperature: null,
        designPressure: null,
        operatingTemperature: null,
        operatingTemperatureUnits: '°F',
        operatingPressure: null,
        corrosionAllowance: null,
        cutterDiameter: null,
        runLength: null,
        branchHeight: null,
        flowRate: null,
        branchHeightUnits: 'in',
        designCode: null
    });

    package = [
        'Price Only',
        'Quotation for Approval',
        'Ball Park (Price and Delivery Only)'
    ] as const;

    packageRequirements = ['Drawings', 'Calculations'] as const;

    qualityRequirements = [
        "MTR's",
        "COC's",
        'PMI',
        'NDE',
        'P.E. Stamp'
    ] as const;

    nextStep = [
        'Move Straight to Manufacture',
        'Approval Required before Manufacture'
    ] as const;

    engineeringPriorityLevel = ['Routine', 'Priority'] as const;

    bid = ['Competitive Bid'] as const;

    Units = ['in', 'mm'] as const;

    pressureUnits = ['psi', 'bar', 'kPa'] as const;

    binary = ['No', 'Yes'] as const;

    lineLocation = [
        'Above Ground',
        'Buried',
        'Over Water',
        'Sub-sea',
        'Offshore',
        'Platform'
    ] as const;

    designCode = ['B31.1', 'B31.3', 'B31.4', 'B31.8', 'AWWA'] as const;

    designFactor = ['.72', '.60', '.50', '.40'] as const;

    flangeRating = ['150', '300', '600', '900', '1500', '2500'] as const;

    cutterTypeDiameter = ['Standard Hot Tap', 'Line Stop', 'Other'] as const;

    runLength = ['Standard ', 'Client Specified (Maximum)'] as const;

    branchHeight = [
        'Standard ',
        'Client Specified (From Top of Pipe)'
    ] as const;

    flowrateUnits = ['ft/s', 'm/s'] as const;

    fittingTypeOptions = ['Stock', 'Custom'] as const;

    cutterTypeOptions = ['Hot Tap', 'Line Stop', 'Short Stop', 'Hot Stop'];

    private _packageType: string;

    get typeOfPackage(): string {
        return this._packageType;
    }

    // eslint-disable-next-line @angular-eslint/no-empty-lifecycle-method
    ngOnInit(): void {}

    private _isValid = false;

    get isValid(): boolean {
        return this._isValid;
    }

    @Output() continueSelected = new EventEmitter<void>();

    onSave = (e) => {
        const result = e.validationGroup.validate();
        this._isValid = result.isValid;

        if (this._isValid == true) {
            this.continueSelected.next();
            this._scrollService.scrollToTop();
        }
    };

    private _selectedSection: string;

    isSelected: boolean = false;

    threeWayOutlet: boolean = true;

    private _selectedFitting: string;

    hotTapOptions = [
        'Standard Hot Tap',
        'Tank Tap',
        'Angle Tap',
        'Elbow Tap'
    ] as const;

    lineStopOptions = [
        'Pivot Head',
        'Smart Stop',
        'Folding Head',
        'Inflatable Line Stop',
        'Across-the-Line Stop',
        'Hot Stop',
        'Hi-Stop',
        'Sure Stop',
        'High Temperature Plug (HTP)',
        'Across-the-Header Stop'
    ] as const;

    sections = ['Hot Tap', 'Line Stop'] as const;

    fittings = ['Weld On', 'Bolt On'] as const;

    @Output() sectionSelected = new EventEmitter<string>();
    set selectedSection(value: string) {
        this._selectedSection = value;
        this.sectionSelected.next(value);
    }
    get selectedSection(): string {
        return this._selectedSection;
    }

    @Output() fittingSelected = new EventEmitter<string>();
    set selectedFitting(value: string) {
        this._selectedFitting = value;
        this.fittingSelected.next(value);
    }
    get selectedFitting(): string {
        return this._selectedFitting;
    }

    temperatureFormat: string;
    temperatureButton: any;
    tempValue: number;

    pressureFormat: string;
    pressureButton: any;
    pressureValue: number;

    lengthFormat: string;
    lengthButton: any;
    lengthValue: number;

    rateFormat: string;
    rateButton: any;

    showPhotoPopup: boolean;
    alert: boolean;

    lineContentCount: number;
    iconButton: any;
    lineContentButton: any;

    private _selectedOpTemp: boolean;

    @Output() operatingTemperatureHigher = new EventEmitter<boolean>();
    set selectedOpTemp(value: boolean) {
        this._selectedOpTemp = value;
        this.operatingTemperatureHigher.next(value);
    }
    get selectedOpTemp(): boolean {
        return this._selectedOpTemp;
    }

    onOperatingTempChanged = (e) => {
        if (this.htsFormData.operatingTemperatureUnits === this.tempUnits[0]) {
            if (this.htsFormData.operatingTemperature >= 400) {
                this.selectedOpTemp = true;
            } else {
                this.selectedOpTemp = false;
            }
        } else if (
            this.htsFormData.operatingTemperatureUnits === this.tempUnits[1]
        ) {
            if (this.htsFormData.operatingTemperature >= 226) {
                this.selectedOpTemp = true;
            } else {
                this.selectedOpTemp = false;
            }
        }
    };

    branchHeightUnitsClicked = (e) => {
        if (this.htsFormData.branchHeightUnits === 'in') {
            if (this.htsFormData.branchHeight) {
                this.htsFormData.branchHeight =
                    this.htsFormData.branchHeight * 25.4;
            }
            this.htsFormData.branchHeightUnits = 'mm';
        } else {
            if (this.htsFormData.branchHeight) {
                this.htsFormData.branchHeight =
                    this.htsFormData.branchHeight / 25.4;
            }
            this.htsFormData.branchHeightUnits = 'in';
        }
    };

    @ViewChild(DxFormComponent) form: DxFormComponent;
    @ViewChild(DxPopoverComponent) popover: DxPopoverComponent;
    @ViewChild('lineContentPopOver') lineContentPopOver: DxPopoverComponent;

    constructor(private _scrollService: ScrollService) {
        this.temperatureFormat = '#.##';
        this.temperatureButton = {
            text: this.tempUnits[0],
            stylingMode: 'text',
            width: 32,
            elementAttr: {
                class: 'Temperature Units'
            },
            onClick: (e) => {
                if (e.component.option('text') === this.tempUnits[0]) {
                    e.component.option('text', this.tempUnits[1]);
                    this.htsFormData.operatingTemperatureUnits =
                        this.tempUnits[1];
                    this.temperatureFormat = '#.##';
                } else {
                    e.component.option('text', this.tempUnits[0]);
                    this.htsFormData.operatingTemperatureUnits =
                        this.tempUnits[0];
                    this.temperatureFormat = '#.##';
                }

                this.onOperatingTempChanged(e);
            }
        };

        this.pressureFormat = '#.##';
        this.pressureButton = {
            text: this.pressureUnits[0],
            stylingMode: 'text',
            width: 32,
            elementAttr: {
                class: 'Pressure Units'
            },
            onClick: (e) => {
                if (e.component.option('text') === this.pressureUnits[0]) {
                    e.component.option('text', this.pressureUnits[1]);
                    this.pressureFormat = '#.##';
                } else if (
                    e.component.option('text') === this.pressureUnits[1]
                ) {
                    e.component.option('text', this.pressureUnits[2]);
                    this.pressureFormat = '#.##';
                } else {
                    e.component.option('text', this.pressureUnits[0]);
                    this.pressureFormat = '#.##';
                }
            }
        };

        this.lengthFormat = '#.##';
        this.lengthButton = {
            text: this.Units[0],
            stylingMode: 'text',
            width: 32,
            elementAttr: {
                class: 'Pressure Units'
            },
            onClick: (e) => {
                if (e.component.option('text') === this.Units[0]) {
                    e.component.option('text', this.Units[1]);
                    this.lengthFormat = '#.##';
                } else {
                    e.component.option('text', this.Units[0]);
                    this.lengthFormat = '#.##';
                }
            }
        };

        this.rateFormat = '#.##';
        this.rateButton = {
            text: this.flowrateUnits[0],
            stylingMode: 'text',
            width: 32,
            elementAttr: {
                class: 'Flow Rate Units'
            },
            onClick: (e) => {
                if (e.component.option('text') === this.flowrateUnits[0]) {
                    e.component.option('text', this.flowrateUnits[1]);
                    this.rateFormat = '#.##';
                } else {
                    e.component.option('text', this.flowrateUnits[0]);
                    this.rateFormat = '#.##';
                }
            }
        };

        this.iconButton = {
            icon: 'info',
            onClick: (e) => {
                const id = Array.from(
                    this.form.instance
                        .getEditor('partNumber')
                        .element()
                        .getElementsByTagName('input')
                ).filter((element) => element.hasAttribute('id'))[0].id;
                if (this.popover.visible) {
                    this.popover.instance.hide();
                } else {
                    this.popover.instance.show(`#${id}`);
                }
            }
        };

        this.lineContentButton = {
            icon: 'info',
            onClick: (e) => {
                const id = Array.from(
                    this.form.instance
                        .getEditor('lineContentCount')
                        .element()
                        .getElementsByTagName('input')
                ).filter((element) => element.hasAttribute('id'))[0].id;
                if (this.lineContentPopOver.visible) {
                    this.lineContentPopOver.instance.hide();
                } else {
                    this.lineContentPopOver.instance.show(`#${id}`);
                }
            }
        };
    }

    showInfo() {
        this.showPhotoPopup = true;
        console.log(this.showPhotoPopup);
    }

    onLineContentCountChanged = (e) => {
        // Temporary solution that needs to be accounted for.
        const lineContentsCurrentLength = this.htsFormData.lineContents.length;
        let target = 0;
        if (e.value > lineContentsCurrentLength) {
            target = e.value - lineContentsCurrentLength;

            for (let i = 0; i < target; i++) {
                this.htsFormData.lineContents.push(new LineContent());
            }
        } else if (e.value < lineContentsCurrentLength) {
            target = e.value;
            let i = lineContentsCurrentLength - 1;
            while (i >= e.value) {
                this.htsFormData.lineContents.splice(i, 1);
                i = i - 1;
            }
        }
    };

    onServiceChanged = (e) => {
        const newValue = e.value;

        this.isSelected = true;

        if (newValue === 'Hot Tap') {
            this.htsFormData.selectedService = 'Hot Tap';
        } else if (newValue === 'Line Stop') {
            this.htsFormData.selectedService = 'Line Stop';
        }
    };

    onFittingChanged = (e) => {
        if (e.value === 'Weld On') {
            this.htsFormData.selectedFitting = 'Weld On';
            this.selectedFitting = 'Weld On';
        } else {
            this.htsFormData.selectedFitting = 'Bolt On';
            this.selectedFitting = 'Bolt On';
        }
    };

    onSectionChanged = (e) => {
        this.htsFormData.selectedSection = e.value;
        this.selectedSection = e.value;
    };

    onPackageChanged = (e) => {
        this.htsFormData.packageType = e.value;
    };

    thumbnailClicked = (e: MouseEvent) => {
        this.showPhotoPopup = !this.showPhotoPopup;
    };

    onLineContentChanged = (e, index) => {
        this.htsFormData.lineContents[index].lineContent = e.value;
    };

    onBranchHeightChanged = (e) => {
        if (
            (this.htsFormData.branchHeightUnits === 'mm' && e.value > 1270) ||
            (this.htsFormData.branchHeightUnits === 'in' && e.value > 50)
        ) {
            this.alert = !this.alert;
        }
    };

    onOkClicked = (e) => {
        this.alert = !this.alert;
    };

    tempCompare = (e) => {
        if (
            this.htsFormData.designTemperature !== null &&
            this.htsFormData.operatingTemperature != null
        ) {
            if (
                this.htsFormData.designTemperature <
                this.htsFormData.operatingTemperature
            ) {
                return false;
            } else {
                return true;
            }
        } else {
            return true;
        }
    };

    pressureCompare = (e) => {
        if (
            this.htsFormData.designPressure !== null &&
            this.htsFormData.operatingPressure != null
        ) {
            if (
                this.htsFormData.designPressure <
                this.htsFormData.operatingPressure
            ) {
                return false;
            } else {
                return true;
            }
        } else {
            return true;
        }
    };
}
