export interface CPDxTreeViewItem {
    text?: string;
    icon?: string;
    path?: string;
    items?: CPDxTreeViewItem[];
}

export const homeMenu: CPDxTreeViewItem = {
    text: 'Home',
    icon: 'fa fa-home',
    path: '/'
};

export const requestAccessMenu: CPDxTreeViewItem = {
    text: 'Request Access',
    icon: 'fa fa-unlock-alt',
    path: '/request-access'
};

export const wmoMenu: CPDxTreeViewItem = {
    text: "Where's My Order",
    icon: 'fa fa-map-pin',
    items: [
        {
            text: 'Dashboard',
            icon: 'fa fa-tachometer',
            path: '/wmo/dashboard'
        },
        {
            text: 'Order Tracker',
            icon: 'fa fa-tasks',
            path: '/wmo/orders'
        },
        {
            text: 'Archive',
            icon: 'fa fa-archive',
            path: '/wmo/orders/archived'
        }
    ]
};

export const boltedFlangeCalculatorMenu: CPDxTreeViewItem = {
    text: 'Calculators',
    icon: 'fa fa-calculator',
    items: [
        {
            text: 'Bolted Joint Calculator',
            path: '/bolted-joint-calculator/calculator'
        }
    ]
};

export const fullCalculatorsMenu: CPDxTreeViewItem = {
    text: 'Calculators',
    icon: 'fa fa-calculator',
    items: [
        {
            text: 'Steam Loss Calculator',
            path: '/steam-loss-calculator'
        }
        //{
        //     text: 'Bolted Joint Calculator',
        //     path: '/bolted-joint-calculator/calculator'
        // }
    ]
};

export const chemicalReferenceDictionaryMenu: CPDxTreeViewItem = {
    text: 'Chemical Reference Dictionary',
    path: '/chemical-reference-dictionary'
};
export const chemicalReferenceDictionaryAdminMenu: CPDxTreeViewItem = {
    text: 'Chemical Reference Dictionary',
    items: [
        {
            text: 'Chemical Reference Dictionary',
            path: '/chemical-reference-dictionary'
        },
        {
            text: 'CRD Audit Trail',
            path: '/chemical-reference-dictionary/audit'
        }
    ]
};
export const pipelineDigsiteStatusOnly: CPDxTreeViewItem = {
    text: 'Dig Site Status',
    path: '/pipeline-inspection/digsite-status'
};

export const pipelineDigsiteDetailsOnly: CPDxTreeViewItem = {
    text: 'Dig Site Details',
    path: '/pipeline-inspection/digsite'
};

export const blankPipelineIntegrityMenu: CPDxTreeViewItem = {
    text: 'Pipeline Integrity',
    items: []
};

export const pipelineIntegritySetupOnly: CPDxTreeViewItem = {
    text: 'Setup',
    path: '/pipeline-inspection/setup'
};

export const pipelineIntegrityMetricsOnly: CPDxTreeViewItem = {
    text: 'Metrics',
    path: '/pipeline-inspection/metrics'
};

export const apmMenu: CPDxTreeViewItem = {
    text: 'Asset Performance Management',
    items: [
        { text: 'Dashboard', path: '/apm/dashboard' },
        {
            text: 'Projects',
            path: '/apm/projects'
        },
        {
            text: 'Assets',
            path: '/apm/asset-management'
        },
        {
            text: 'Work Orders',
            path: '/apm/work-orders'
        },
        { text: 'Tasks', path: '/apm/tasks' }
    ]
};

export const leakReportingMenu: CPDxTreeViewItem = {
    text: 'Leak Reporting',
    path: '/apm/leak-reporting'
};

export const apmClientManagement: CPDxTreeViewItem = {
    text: 'Client Management',
    path: '/apm/client-management'
};

export const apmToolsMenu: CPDxTreeViewItem = {
    text: 'Tools',
    items: [
        {
            text: 'Assign Tasks',
            path: '/apm/assign-tasks'
        }
    ]
};

export const apmAdminMenu = {
    ...apmMenu,
    items: [
        ...apmMenu.items,
        leakReportingMenu,
        apmClientManagement,
        apmToolsMenu
    ]
};

export const fullPipelineIntegrityMenu: CPDxTreeViewItem = {
    text: 'Pipeline Integrity',
    items: [
        {
            text: 'Dig Site Status',
            path: '/pipeline-inspection/digsite-status'
        },
        {
            text: 'Dig Site Details',
            path: '/pipeline-inspection/digsite'
        },
        {
            text: 'Setup',
            path: '/pipeline-inspection/setup'
        },
        { text: 'Metrics', path: '/pipeline-inspection/metrics' }
    ]
};

export const aiToolsMenu: CPDxTreeViewItem = {
    text: 'AI Tools',
    icon: 'fa fa-cloud',
    items: [
        {
            text: 'ESG',
            path: '/ai-tools/esg'
        }
    ]
};

export const emptyWorkmanagementMenu: CPDxTreeViewItem = {
    text: 'Work Management',
    icon: 'event',
    items: []
};

export const mechanicalDataCollectionMenu: CPDxTreeViewItem = {
    text: 'Mechanical Data Collection',
    path: '/mos/data-intake'
};

export const connectedWorkerMenu: CPDxTreeViewItem = {
    text: 'Connected Worker',
    items: []
};

export const fullConnectedWorkerMenu: CPDxTreeViewItem = {
    text: 'Connected Worker',
    items: [
        {
            text: 'Chevron',
            path: '/connected-worker/chevron'
        },
        {
            text: 'GPC',
            path: '/connected-worker/gpc'
        }
    ]
};

export const fieldServiceManagementMenu: CPDxTreeViewItem = {
    text: 'Field Service Management',
    items: [
        {
            text: 'Dashboard',
            path: '/fsm/dashboard'
        },
        { text: 'Trades Request', path: '/fsm/trades-request' }
    ]
};

export const fullWorkManagementMenu: CPDxTreeViewItem = {
    text: 'Work Management',
    icon: 'event',
    items: [
        // chemicalReferenceDictionaryAdminMenu,
        fieldServiceManagementMenu,
        // fullPipelineIntegrityMenu,
        // mechanicalDataCollectionMenu,
        fullConnectedWorkerMenu,
        apmAdminMenu
    ]
};

export const fsmOnlyWorkManagementMenu: CPDxTreeViewItem = {
    text: 'Work Management',
    icon: 'event',
    items: [fieldServiceManagementMenu]
};

export const equipmentCenterMenu: CPDxTreeViewItem = {
    text: 'Equipment Center',
    icon: 'toolbox',
    items: [
        {
            text: 'Equipment Request Form',
            path: '/edr/equipment-request-form'
        },
        {
            text: 'Equipment Tracker',
            path: '/edr/equipment-tracker'
        }
    ]
};

export const assetManagementMenu: CPDxTreeViewItem = {
    text: 'Asset Management',
    icon: 'fa fa-industry',
    items: []
};

export const aimaasMenu: CPDxTreeViewItem = {
    text: 'Asset Integrity Hub',
    items: [
        {
            text: 'Overview Dashboards',
            path: '/aimaas/overview-dashboard'
        },
        {
            text: 'KPI Dashboards',
            path: '/aimaas/dashboards'
        },
        {
            text: 'Equipment List',
            path: '/aimaas/drilldown'
        },
        {
            text: 'Inspections List',
            path: '/aimaas/inspection-drilldown'
        },
        {
            text: 'Recommendation List',
            path: '/aimaas/anomaly-drilldown'
        }
    ]
};

export const administrationMenu: CPDxTreeViewItem = {
    text: 'Administration',
    icon: 'key',
    items: [
        {
            text: 'Users',
            path: '/admin/users'
        },
        {
            text: 'Roles',
            path: '/admin/roles'
        }
    ]
};

export const authHistoryMenu: CPDxTreeViewItem = {
    text: 'History',
    path: '/admin/auth-history'
};

export const userAuditMenu: CPDxTreeViewItem = {
    text: 'User Audit',
    path: '/admin/user-audit'
};

export const wmoAdministrationMenu: CPDxTreeViewItem = {
    text: 'ETL',
    icon: 'fa fa-database',
    path: '/wmo/etl'
};

export const remoteMonitoringMenu: CPDxTreeViewItem = {
    text: 'Remote Asset Monitoring',
    path: '/remote-asset-monitoring'
};
