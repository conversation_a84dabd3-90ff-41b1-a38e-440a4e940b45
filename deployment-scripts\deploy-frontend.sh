#!/bin/bash

# Azure Frontend Deployment Script
# Usage: ./deploy-frontend.sh <environment> <subscription-id>

set -e

ENVIRONMENT=${1:-dev}
SUBSCRIPTION_ID=${2}
RESOURCE_GROUP="rg-kraken-${ENVIRONMENT}-001"
STORAGE_ACCOUNT="stakraken${ENVIRONMENT}001"
CONTAINER_NAME="\$web"

echo "🚀 Starting frontend deployment to ${ENVIRONMENT} environment..."

# Login and set subscription
az login --service-principal -u $AZURE_CLIENT_ID -p $AZURE_CLIENT_SECRET --tenant $AZURE_TENANT_ID
az account set --subscription $SUBSCRIPTION_ID

# Get configuration values
echo "🔍 Retrieving configuration..."
API_URL=$(az containerapp show --name "ca-kraken-api-${ENVIRONMENT}" --resource-group $RESOURCE_GROUP --query "properties.configuration.ingress.fqdn" -o tsv)
SIGNALR_URL=$(az signalr show --name "signalr-kraken-${ENVIRONMENT}-001" --resource-group $RESOURCE_GROUP --query "hostName" -o tsv)
APP_INSIGHTS_KEY=$(az monitor app-insights component show --app "ai-kraken-${ENVIRONMENT}" --resource-group $RESOURCE_GROUP --query "connectionString" -o tsv)

# Update environment configuration
echo "⚙️ Updating environment configuration..."
cd wheres-my-order

# Replace placeholders in environment file
if [ "$ENVIRONMENT" = "prod" ]; then
  ENV_FILE="src/environments/environment.prod.ts"
else
  ENV_FILE="src/environments/environment.ts"
fi

sed -i "s|___API_URL___|https://${API_URL}/api|g" $ENV_FILE
sed -i "s|___SIGNALR_URL___|https://${SIGNALR_URL}|g" $ENV_FILE
sed -i "s|___APPLICATION_INSIGHTS_CONNECTION_STRING___|${APP_INSIGHTS_KEY}|g" $ENV_FILE
sed -i "s|___AZURE_STORAGE_ACCOUNT___|${STORAGE_ACCOUNT}|g" $ENV_FILE

# Install dependencies and build
echo "📦 Installing dependencies..."
npm ci

echo "🔨 Building application..."
if [ "$ENVIRONMENT" = "prod" ]; then
  npm run build:prod
else
  npm run build
fi

# Enable static website hosting
echo "🌐 Enabling static website hosting..."
az storage blob service-properties update \
  --account-name $STORAGE_ACCOUNT \
  --static-website \
  --404-document "index.html" \
  --index-document "index.html"

# Upload files to storage
echo "📤 Uploading files to Azure Storage..."
az storage blob upload-batch \
  --account-name $STORAGE_ACCOUNT \
  --destination $CONTAINER_NAME \
  --source "dist/wheres-my-order" \
  --overwrite

# Get static website URL
WEBSITE_URL=$(az storage account show --name $STORAGE_ACCOUNT --resource-group $RESOURCE_GROUP --query "primaryEndpoints.web" -o tsv)

echo "✅ Frontend deployment completed successfully!"
echo "🌐 Website URL: ${WEBSITE_URL}"

# Configure CDN (optional)
if [ "$ENVIRONMENT" = "prod" ]; then
  echo "🚀 Setting up CDN for production..."
  az cdn profile create \
    --name "cdn-kraken-${ENVIRONMENT}" \
    --resource-group $RESOURCE_GROUP \
    --sku Standard_Microsoft
  
  az cdn endpoint create \
    --name "kraken-${ENVIRONMENT}" \
    --profile-name "cdn-kraken-${ENVIRONMENT}" \
    --resource-group $RESOURCE_GROUP \
    --origin "${STORAGE_ACCOUNT}.z13.web.core.windows.net" \
    --origin-host-header "${STORAGE_ACCOUNT}.z13.web.core.windows.net"
  
  echo "🌐 CDN URL: https://kraken-${ENVIRONMENT}.azureedge.net"
fi
