import { PhotosReport, ReportTypes } from './report-data.model';
import { FullPackage, GenericAttribute } from './report-source.model';

export const getQuestionSections = (input: Record<string, any>) => {
    // console.log(input);
    const questionSections = Object.values(input)
        .filter((value) => typeof value === 'object' && value !== null)
        .map((value) => {
            if (!value.options) {
                return getQuestionSections(value);
            }
            return value;
        })
        .flat();
    return questionSections as GenericAttribute[];
};

export const getPhotosReports = (
    fullPackage: FullPackage,
    headerInfo: any
): PhotosReport[] => {
    const photoObjects = fullPackage.fullInspection
        ? fullPackage.fullInspection.photoBlobs
        : fullPackage.assetWalkdown.photoBlobs;
    return Array.from(
        { length: Math.ceil(photoObjects.length / 4) },
        (_, i) =>
            ({
                type: ReportTypes.Photos,
                headerInfo: headerInfo,
                title: 'Inspection Photo(s)',
                photos: photoObjects.slice(i * 4, i * 4 + 4)
            } as PhotosReport)
    );
};

export const getQuestionSectionsFromWalkdown = (input: Record<string, any>) => {
    const questionSections = Object.values(input)
        .filter((value) => typeof value === 'object' && value !== null)
        .map((value) => {
            if (!value.options) {
                return extractInnerQuestions(value);
                // return getQuestionSectionsFromWalkdown(value);
            }
            return value;
        })
        .flat();
    return questionSections;
};

export const extractInnerQuestions = (value) => {
    if (value.displayName) {
        const obj = {
            displayName: value.displayName,
            options: [],
            currentValue: []
        };
        let initialOptions = [],
            initialSelected = [];
        Object.values(value).forEach((section: any) => {
            if (section.options) {
                if (section.attributeType == 'PredefinedValue') {
                    initialOptions.push(section?.displayName ?? '');
                    initialSelected.push(
                        section?.selectedOption == 'Yes'
                            ? section?.displayName ?? ''
                            : ''
                    );
                } else if (section.attributeType == 'MultiPredefinedValue') {
                    initialOptions.push(
                        section?.options.map((item) => item.value)
                    );
                    initialSelected.push(section?.selectedOptions);
                }
            } else {
                Object.values(section).map((innerSection: any) => {
                    if (innerSection?.attributeType == 'PredefinedValue') {
                        initialOptions.push(innerSection?.displayName ?? '');
                        initialSelected.push(
                            innerSection.currentValue == 'Yes'
                                ? innerSection?.displayName ?? ''
                                : ''
                        );
                    } else if (
                        innerSection?.attributeType == 'MultiPredefinedValue'
                    ) {
                        initialOptions.push(
                            innerSection?.options.map(
                                (section) => section.value
                            )
                        );
                        initialSelected.push(innerSection.currentValue);
                    }
                });
            }
        });
        obj.options = initialOptions.flat();
        obj.currentValue = initialSelected.flat().filter((item) => !!item);
        return obj;
    } else {
        extractInnerQuestions(value);
    }
};
