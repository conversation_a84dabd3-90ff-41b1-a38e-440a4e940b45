using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ClientPortal.Shared.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using OrderTracking.API.Repositories;

namespace OrderTracking.API.Services
{
    /// <summary>
    /// Azure Cosmos DB service for Role operations (migrated from Firebase)
    /// </summary>
    public class RolesCosmosService : RolesCosmosRepository, IRolesService
    {
        #region Fields and Constants

        private readonly IUserProfileRepository _userProfileRepository;
        private readonly IAuthHistoryService _authHistory;
        private readonly IHttpContextAccessor _httpContextAccessor;

        #endregion

        #region Constructors

        public RolesCosmosService(IContainerFactory containerFactory, IServiceProvider serviceProvider, IConfiguration configuration) 
            : base(containerFactory, configuration)
        {
            _authHistory = (IAuthHistoryService)serviceProvider.GetService(typeof(IAuthHistoryService));
            _httpContextAccessor = (IHttpContextAccessor)serviceProvider.GetService(typeof(IHttpContextAccessor));

            _userProfileRepository = new UserProfileCosmosRepository(containerFactory, configuration);
        }

        #endregion

        #region Interface Implementation

        public new async Task<Role> AddAsync(Role role)
        {
            try
            {
                role = VerifyProperties(role);
                var newRole = await base.AddAsync(role);
                if (newRole != null) await CreateChangeEventAsync(null, newRole);

                return newRole;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        public async Task<IEnumerable<string>> GetGroupsAsync()
        {
            try
            {
                var roles = await GetAllAsync();
                return roles.Select(r => r.Group).Distinct();
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        public new async Task<Role> UpdateAsync(Role role)
        {
            try
            {
                var originalRole = await GetAsync(role.Id);
                role = VerifyProperties(role);
                var updatedRole = await base.UpdateAsync(role);
                if (updatedRole != null) await CreateChangeEventAsync(originalRole, updatedRole);

                return updatedRole;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        public new async Task RemoveAsync(string id)
        {
            try
            {
                var originalRole = await GetAsync(id);
                await base.RemoveAsync(id);
                if (originalRole != null) await CreateChangeEventAsync(originalRole, null);
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        #endregion

        #region Private Methods

        private Role VerifyProperties(Role role)
        {
            if (role == null) return null;

            // Role.Id is read-only and computed from RoleKey
            // Role doesn't have CreatedDate/ModifiedDate properties
            // These would be handled by Azure Cosmos DB timestamps if needed

            return role;
        }

        private async Task CreateChangeEventAsync(Role originalRole, Role newRole)
        {
            try
            {
                if (_authHistory == null || _httpContextAccessor?.HttpContext == null) return;

                var userEmail = _httpContextAccessor.HttpContext.User?.Identity?.Name;
                if (string.IsNullOrEmpty(userEmail)) return;

                var changeEvent = new ChangeEvent
                {
                    Id = Guid.NewGuid().ToString(),
                    Old = originalRole,
                    New = newRole,
                    CreatedAt = DateTime.UtcNow,
                    User = await GetCurrentUserAsync()
                };

                await _authHistory.AddItemAsync(changeEvent);
            }
            catch (Exception e)
            {
                // Log but don't throw - change events are not critical
                Console.WriteLine($"Failed to create change event: {e.Message}");
            }
        }

        #endregion

        #region Private Methods

        private async Task<UserProfile> GetCurrentUserAsync() => await _userProfileRepository.GetAsync(_httpContextAccessor.HttpContext.User.Identity.Name.ToLower());

        #endregion
    }
}
