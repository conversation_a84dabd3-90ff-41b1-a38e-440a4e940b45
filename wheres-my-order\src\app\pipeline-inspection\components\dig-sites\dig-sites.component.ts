import { Component, EventEmitter, Output } from '@angular/core';
import cloneDeep from 'clone-deep';
import { DxDataGridComponent } from 'devextreme-angular/ui/data-grid';
import CustomStore from 'devextreme/data/custom_store';
import dxDataGrid from 'devextreme/ui/data_grid';
import { ToastrService } from 'ngx-toastr';
import { Observable, of } from 'rxjs';
import { UserProfile } from '../../../profile/models';
import { UsersService } from '../../../shared/services';
import {
    DigSite,
    DigSiteState,
    InspectionData,
    PIAResponse,
    Project
} from '../../models';
import { PipelineInspectionService } from '../../services';

@Component({
    selector: 'app-dig-sites',
    templateUrl: './dig-sites.component.html',
    styleUrls: ['./dig-sites.component.scss']
})
export class DigSitesComponent {
    projects$: Observable<Project[]>;
    currentUser$: Observable<UserProfile>;
    dataSource: CustomStore;
    @Output() focusedDigSite = new EventEmitter<DigSite>();

    private _oldDigSite: DigSite;
    private _newDigSite: DigSite;

    private _oldInspectionData: InspectionData;
    private _newInspectionData: InspectionData;

    constructor(
        private readonly _pia: PipelineInspectionService,
        private readonly _toasts: ToastrService,
        private readonly _users: UsersService
    ) {
        this.projects$ = this._pia.projects$;
        this.currentUser$ = this._users.currentProfile$;
        this.dataSource = new CustomStore({
            key: 'identity',
            load: () => this.loadDigsites(),
            insert: async (values) => {
                const { project, ...digSite } = values;
                const response = await this._pia
                    .createDigSite(digSite)
                    .toPromise();

                if (response?.error) {
                    throw new Error(response.error);
                } else {
                    const oldInspectionData = cloneDeep(
                        response.newInspectionData
                    );
                    const newInspectionData = cloneDeep(oldInspectionData);
                    newInspectionData.section1_SiteOverview.locationAndPipeInformation.latLongAttribute =
                        digSite.inspectionData?.section1_SiteOverview?.locationAndPipeInformation?.latLongAttribute;

                    await this._pia
                        .updateInspectionData(
                            oldInspectionData,
                            newInspectionData
                        )
                        .toPromise();
                }
                const d = await this._pia
                    .getDigSite(digSite.identity)
                    .toPromise();
                return { project, ...d };
            },
            update: async (key, values) => {
                const { inspectionData, ...newDigSiteData } = values;

                // Start with a no-op inspection data update call...
                let inspectionDataUpdate$: Observable<PIAResponse> = of(null);

                // If edits were made to inspection data properties (`digNumber`, `lineName`, and `locationDescription`)...
                if (inspectionData) {
                    this._newInspectionData.section1_SiteOverview.locationAndPipeInformation.latLongAttribute =
                        cloneDeep(
                            inspectionData.section1_SiteOverview
                                .locationAndPipeInformation.latLongAttribute
                        );
                    // Prepare the inspection data update call
                    inspectionDataUpdate$ = this._pia.updateInspectionData(
                        this._oldInspectionData,
                        this._newInspectionData
                    );
                }

                // Start with a no-op dig site data update call...
                let digSiteUpdate$: Observable<PIAResponse> = of(null);

                // If edits were made to dig site properties...
                if (newDigSiteData && Object.keys(newDigSiteData).length) {
                    // Prepare the dig site update call
                    digSiteUpdate$ = this._pia.updateDigSite(
                        this._oldDigSite,
                        this._newDigSite
                    );
                }

                // Return a promise of both update calls completing
                const inspectionDataResponse: any =
                    await inspectionDataUpdate$.toPromise();
                if (inspectionDataResponse?.error) {
                    throw new Error(inspectionDataResponse.error);
                }
                const digsiteResponse = await digSiteUpdate$.toPromise();
                if (digsiteResponse?.error) {
                    throw new Error(digsiteResponse.error);
                }
                return digsiteResponse;
            }
        });
        this.projects$.subscribe((projects) => {
            this.dataSource.load();
        });
    }

    onToolbarPreparing(e) {
        const addBtn = e.toolbarOptions.items.find(
            (i) => i.name === 'addRowButton'
        );

        if (!addBtn) return;

        addBtn.showText = 'always';
        addBtn.options = {
            ...addBtn.options,
            icon: null,
            text: 'Create',
            type: 'success',
            stylingMode: 'contained'
        };
    }

    stateText = (arg) => {
        return DigSiteState[arg.value];
    };

    onEditorPreparing(event: {
        cancel: boolean;
        component: dxDataGrid;
        dataField: string;
        disabled: boolean;
        editorElement: HTMLElement;
        editorName: string;
        editorOptions: any;
        element: HTMLElement;
        model: any;
        parentType: string;
        readOnly: boolean;
        row: { isNewRow: boolean; rowType: string };
        rtlEnabled: boolean;
        setValue: (newValue, newText) => any;
        updateValueTimeout: number;
        value: any;
        width: number;
    }) {
        const inserting =
            event.row && event.row.rowType === 'data' && event.row.isNewRow;
        if (event.dataField === 'project.id' && !inserting) {
            event.editorOptions.disabled = true;
        }
    }

    onRowUpdating(event: {
        cancel: boolean;
        component: dxDataGrid;
        element: HTMLElement;
        key: string;
        newData: Partial<DigSite>;
        oldData: DigSite;
    }) {
        // We have Dig Site and Inspection Data values to keep
        // track of updates for.  So let's handle them separately.
        const { inspectionData, ...newDigSiteData } = event.newData;
        // If there were any changes made to dig site properties...
        if (Object.keys(newDigSiteData).length) {
            // Construct old and new versions of the full dig site object
            // for the update call.
            this._oldDigSite = event.oldData;
            this._newDigSite = { ...this._oldDigSite, ...newDigSiteData };
        }
        // If there were any changes made to inspection data values...
        if (inspectionData) {
            // Save the whole old inspection data object
            this._oldInspectionData = event.oldData.inspectionData;

            // construct a full inspection data object with new values,
            // edited by the user in the grid.  There are methods of smart
            // merging, but we know that `digNumber`, `lineName`, `locationDescription`
            // are the only two properties from inspection data in the grid.
            this._newInspectionData = cloneDeep(this._oldInspectionData);
            const newLocationAndPipeInfo =
                this._newInspectionData.section1_SiteOverview
                    .locationAndPipeInformation;
            const newData =
                inspectionData.section1_SiteOverview.locationAndPipeInformation;
            // If digNumber was edited...
            if (newData.digNumberAttribute) {
                // If there is already digNumber on the inspection data...
                if (newLocationAndPipeInfo.digNumberAttribute) {
                    newLocationAndPipeInfo.digNumberAttribute.value =
                        newData.digNumberAttribute.value;
                } else {
                    newLocationAndPipeInfo.digNumberAttribute =
                        newData.digNumberAttribute;
                }
            }
            // If lineName was edited...
            if (newData.lineNameAttribute) {
                // If there is already a lineName on the inspection data...
                if (newLocationAndPipeInfo.lineNameAttribute) {
                    newLocationAndPipeInfo.lineNameAttribute.value =
                        newData.lineNameAttribute.value;
                } else {
                    newLocationAndPipeInfo.lineNameAttribute =
                        newData.lineNameAttribute;
                }
            }
            // If locationDescription was edited...
            if (newData.locationDescriptionAttribute) {
                // If there is already a locationDescription on the inspection data...
                if (newLocationAndPipeInfo.locationDescriptionAttribute) {
                    newLocationAndPipeInfo.locationDescriptionAttribute.value =
                        newData.locationDescriptionAttribute.value;
                } else {
                    newLocationAndPipeInfo.locationDescriptionAttribute =
                        newData.locationDescriptionAttribute;
                }
            }
        } else {
            // Set these to undefined so that we can avoid making an update
            // call to inspection data if there were no changes.
            this._oldInspectionData = undefined;
            this._newInspectionData = undefined;
        }
    }

    logEvent(event: any, eventName: string) {
        // TODO: Review how to handle the editing events in the grid.  RowInserting vs. RowInserted is so that we
        // handle the actual saving to the server part in the RowInserting, and prevent RowInserted from firing
        // if something doesn't go right.  Please review here: https://js.devexpress.com/Documentation/ApiReference/UI_Components/dxDataGrid/Configuration/#onRowInserting
        if (eventName === 'RowInserted') {
            this._toasts.success('Dig Site added successfully');
        } else if (eventName === 'RowUpdated') {
            this._toasts.success('Dig Site updated successfully');
        }
        console.log(eventName, event);
    }

    onFocusedRowChanged(e: {
        component: DxDataGridComponent;
        element: HTMLElement;
        row: any;
        rowElement: HTMLElement;
        rowIndex: number;
    }) {
        this.focusedDigSite.next(e.row as DigSite);
    }

    private async loadDigsites(): Promise<(DigSite & { project: Project })[]> {
        const digsites = await this._pia
            .getDigSitesWithInspectionData()
            .toPromise();
        const projects = await this._pia.getProjects().toPromise();
        return digsites.map((d) => ({
            ...d,
            project: projects.find((p) => p.digSiteCodes?.includes(d.identity))
        }));
    }
}
