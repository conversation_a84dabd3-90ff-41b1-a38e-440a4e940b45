import { HttpParams } from '@angular/common/http';
import { ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import cloneDeep from 'clone-deep';
import { DxDataGridComponent } from 'devextreme-angular/ui/data-grid';
import { DxFormComponent } from 'devextreme-angular/ui/form';
import ArrayStore from 'devextreme/data/array_store';
import DataSource from 'devextreme/data/data_source';
import { ClickEvent } from 'devextreme/ui/button';
import { FocusedRowChangedEvent } from 'devextreme/ui/data_grid';
import { confirm } from 'devextreme/ui/dialog';
import { ToastrService } from 'ngx-toastr';
import { forkJoin, Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';
import { AssetManagementSite } from '../../../aimaas/models';
import { CredoSoftService } from '../../../aimaas/services/credo-soft.service';
import { UserProfile } from '../../../profile/models';
import { SensorReading } from '../../../remote-monitoring/models';
import { SensorsService } from '../../../remote-monitoring/services';
import { Breadcrumb } from '../../../shared/components';
import { DistrictService, UsersService } from '../../../shared/services';
import { ClientService } from '../../../shared/services/client.service';
import { ExternalCustomer } from '../../models';
import { CustomerAccountsService } from '../../services';
import { RolesService } from './../../services';
import { customerAccountsDataSource } from './customer-accounts.data-source';

@Component({
    selector: 'app-users',
    templateUrl: './users.component.html',
    styleUrls: ['./users.component.scss']
})
export class UsersComponent implements OnInit {
    @ViewChild(DxFormComponent, { static: false }) userProfile: DxFormComponent;
    @ViewChild(DxDataGridComponent, { static: false })
    searchList: DxDataGridComponent;

    isFormValid = false;
    isUserSelected = false;
    focusedRowKey: any;
    usersDataSource: DataSource;
    userEdit: UserProfile = new UserProfile();
    userOriginal: UserProfile = new UserProfile();
    crumbs: Breadcrumb[] = [
        { label: 'Administration', route: '/admin' },
        { label: 'Users', route: '/admin/users' }
    ];
    customerAccounts: DataSource;

    districts$: Observable<{ value: string; label: string }[]>;
    clients$: Observable<string[]>;
    availableRoles$: Observable<string[]>;

    currentProfile$: Observable<UserProfile>;
    assetManagementSites$: Observable<AssetManagementSite[]>;
    allAssetManagementSites$: Observable<AssetManagementSite[]>;
    remoteMonitoringIds: any;
    customerAccountsTooltipVisible: boolean;

    availableRolesForUser$: Observable<string[]>;
    readings$: Observable<SensorReading[]>;
    clientLocationData: any;
    constructor(
        private readonly _users: UsersService,
        private readonly _roles: RolesService,
        private readonly _districts: DistrictService,
        private readonly _customerAccounts: ClientService,
        private readonly _toasts: ToastrService,
        private readonly _customerAccountsService: CustomerAccountsService,
        private readonly _credoSoftService: CredoSoftService,
        private readonly _remoteMonitoringService: SensorsService,
        private ref: ChangeDetectorRef
    ) {
        this.customerAccounts = customerAccountsDataSource(
            (params: HttpParams) =>
                this._customerAccountsService
                    .getCustomerAccounts(params)
                    .toPromise()
        );

        this.districts$ = this._districts.getDistrictNumbers().pipe(
            map((districts: string[]) =>
                districts.map(district => {
                    const districtWithCode_Name = district.split(' - ');
                    const value = districtWithCode_Name[0];
                    const label = `${districtWithCode_Name[0]} - ${districtWithCode_Name[2]}`;
                    return { value, label: label };
                })
            )
        );
        this.clients$ = this._customerAccounts.getClientNames();
        this.availableRoles$ = this._roles.getRoleKeys();

        this.currentProfile$ = this._users.currentProfile$;

        // Local Site example
        // TODO: Remove once refactored
        // var exampleSite = new AssetManagementSite();
        // exampleSite.clientid = 1;
        // exampleSite.clientname = 'Test Client Name';
        // exampleSite.locationid = 1;
        // exampleSite.locationname = 'Location Name';
        // this.assetManagementSites$ = of([exampleSite]);
        this._credoSoftService.clientLocationData$.subscribe((data) => {
            this.clientLocationData = data;
        });
        this._credoSoftService.assetManagementSites$.subscribe((data) => {
            this.assetManagementSites$ = of(data);
            this.allAssetManagementSites$ = of(data);
        });

        /* ====== OLD IMPLEMENTATION ======
           ======TODO: Remove once refactored ======
        this.assetManagementSites = 
            this._credoSoftService.assetManagementSites$.pipe(
                catchError((error) => of([]))
            ); */

        this.readings$ = this._remoteMonitoringService.readings$;
    }

    ngOnInit() {
        this.loadSearchList();
    }

    toggleCustomerAccountTooltip() {
        this.customerAccountsTooltipVisible =
            !this.customerAccountsTooltipVisible;
    }

    customerDisplayExpr(customer: ExternalCustomer): string {
        return `${customer.externalCustomer} - ${customer.externalCustomerName}`;
    }

    assetManagementSiteDisplayExpr(
        assetManagementSite: AssetManagementSite
    ): string {
        return `${assetManagementSite?.locationid} - ${assetManagementSite?.clientname} - ${assetManagementSite?.locationname}`;
    }

    hasCredoViewRole(user: UserProfile) {
        return user?.roles
            ?.map((role) => role.toLowerCase())
            .includes('aimaas:view');
    }

    userSelected(e: FocusedRowChangedEvent) {
        if (e.rowIndex >= 0) {
            this.userOriginal = new UserProfile();
            Object.assign(this.userOriginal, e.row.data);
            this.userEdit = cloneDeep(this.userOriginal);
            this.isUserSelected = true;
            this.availableRolesForUser$ = forkJoin([
                this.availableRoles$,
                of(this.userOriginal.roles)
            ]).pipe(map(([s1, s2]) => Array.from(new Set([...s1, ...s2]))));
            this.filterAssetManagementSitesByDistrict(this.userEdit.districtIds || []);
        }
    }

    async userDeleteClicked(e: ClickEvent) {
        const result = await confirm(
            'Are you sure you would like to delete this user from the OneInsight web portal?  This will not prevent them from logging in, but will remove all roles and permissions.',
            'Confirm Delete'
        );
        if (result) {
            this.deleteUser(e);
        }
    }

    updateClicked(e: ClickEvent) {
        const result = e.validationGroup.validate();
        this.isFormValid = result.isValid;
        if (this.isFormValid) {
            this.updateUser();
        }
    }

    refreshClicked(e: ClickEvent) {
        this.searchList.instance.clearFilter('search');
        this.setDefaultFormState(e);
        this.loadSearchList();
    }

    cancelClicked(e: ClickEvent) {
        this.setDefaultFormState(e);
    }

    onRolesChanged = (e) => {
        if (
            e.removedItems
                .map((item) => item.toLowerCase())
                .includes('aimaas:view' || 'connectedworker:view')
        ) {
            this.userEdit.assetManagementSiteIds = [];
        }
        if (
            e.removedItems
                .map((item) => item.toLowerCase())
                .includes('remotemonitoring:view')
        ) {
            this.userEdit.remoteMonitoringSiteIds = [];
        }
    };

    private loadSearchList() {
        this._users
            .getAll()
            .pipe(map((users) => this.getDataSource(users)))
            .subscribe((ds) => {
                this.usersDataSource = ds;
                this.usersDataSource.load();
                this.searchList.instance.refresh();
            });
    }

    private getDataSource(users: UserProfile[]): DataSource {
        return new DataSource({
            store: new ArrayStore({
                data: users,
                key: 'id'
            }),
            sort: 'name'
        });
    }

    private setDefaultFormState(e) {
        this.isUserSelected = false;
        this.focusedRowKey = undefined;
        this.userEdit = new UserProfile();
        this.userOriginal = new UserProfile();
        this.ref.detectChanges();
        if (e) e.validationGroup.reset();
    }
    private isFieldRequired(): string | null {
        const roles = this.userEdit.roles?.map((r) => r.toLowerCase());

        const isDistrictUser = roles.includes('aimaas:district');
        const isClientUser = roles.includes('aimaas:client');
        const isAllUser = roles.includes('aimaas:all');

        const hasDistrict =
            this.userEdit.districtIds && this.userEdit.districtIds.length > 0;
        const hasClient =
            this.userEdit.customerAccounts &&
            this.userEdit.customerAccounts.length > 0;

        if (isAllUser && (!hasDistrict || !hasClient)) {
            return 'At least one District and one Client are required.';
        }
        if (isDistrictUser && !hasDistrict) {
            return 'At least one District is required.';
        }
        if (isClientUser && !hasClient) {
            return 'At least one Client is required.';
        }

        return null; // No validation errors
    }

    get isAssetManagementSiteRequired(): boolean {
        const roles = this.userEdit.roles?.map((r) => r.toLowerCase()) || [];
        return (
            roles.includes('aimaas:edit') || roles.includes('aimaas:view')
        ) && !roles.includes('app:admin') && !roles.includes('aimaas:admin') && !roles.includes('aimaas:all');
    }
    get isDistrictRequired(): boolean {
        const roles = this.userEdit.roles?.map((r) => r.toLowerCase()) || [];
        return (
            roles.includes('aimaas:district')
        ) && !roles.includes('app:admin') && !roles.includes('aimaas:admin') && !roles.includes('aimaas:all');
    }
    get isClientRequired(): boolean {
        const roles = this.userEdit.roles?.map((r) => r.toLowerCase()) || [];
        return (
            roles.includes('aimaas:client')
        ) && !roles.includes('app:admin') && !roles.includes('aimaas:admin') && !roles.includes('aimaas:all');
    }

    private updateUser() {
        const roles = this.userEdit.roles?.map((r) => r.toLowerCase()) || [];

        const isDistrictUser = roles.includes('aimaas:district');
        const isClientUser = roles.includes('aimaas:client');
        const isAllUser = roles.includes('aimaas:all');
        const isAdminUser = roles.includes('app:admin') || roles.includes('aimaas:admin');
        const isEditUser = roles.includes('aimaas:edit');
        const isViewUser = roles.includes('aimaas:view');

        const hasDistrict =
            Array.isArray(this.userEdit.districtIds) &&
            this.userEdit.districtIds.length > 0;
        const hasClient =
            Array.isArray(this.userEdit.customerAccounts) &&
            this.userEdit.customerAccounts.length > 0;
        const selectedCount =
            Number(roles.includes('aimaas:all')) +
            Number(roles.includes('aimaas:district')) +
            Number(roles.includes('aimaas:client'));

        if (selectedCount > 1) {
            this._toasts.error(
                'Invalid selection: Only one role can be assigned. Choose either AIMaaS:ALL , AIMaas:District or AIMaas:Client ',
                'Validation Error'
            );
            return;
        }
        if (isDistrictUser && !hasDistrict) {
            if (!this.userEdit.isTeamEmployee) {
                this._toasts.error(
                    'Action not allowed: This role cannot be assigned to a non-TEAM Employee.',
                    'Validation Error'
                );
            } else {
                this._toasts.error(
                    'At least one District is required.',
                    'Validation Error'
                );
            }
            return;
        }
        if (isClientUser && !hasClient) {
            this._toasts.error(
                'At least one Client is required.',
                'Validation Error'
            );
            return;
        }
        if (!isAdminUser && (isEditUser || isViewUser)) {
            if (!this.userEdit.assetManagementSiteIds || this.userEdit.assetManagementSiteIds.length === 0) {
                this._toasts.error(
                    'Please select at least one Asset Management Site ID.',
                    'Validation Error'
                );
                return;
            }
        }
        if (!this.userEdit.roles || this.userEdit.roles.length === 0) {
            this._toasts.error(
                'Please assign at least one role to the selected User.',
                'Validation Error'
            );
            return;
        }
        if (!this.userEdit.isTeamEmployee && isAdminUser) {
            this._toasts.error(
                'Action not allowed: This role cannot be assigned to a non-TEAM Employee.',
                'Validation Error'
            );
            return;
        }
        if (!this.userEdit.isTeamEmployee && isAllUser) {
            this._toasts.error(
                'Action not allowed: This role cannot be assigned to a non-TEAM Employee.',
                'Validation Error'
            );
            return;
        }

        // If validation passes, proceed with update
        this._users.update(this.userEdit, this.userOriginal.id).subscribe({
            next: (success) => {
                this._toasts.success(
                    `User successfully updated (${this.userEdit.id})`,
                    'Success'
                );
                this.usersDataSource
                    .store()
                    .update(this.userEdit.id, this.userEdit)
                    .catch(() => {
                        this.loadSearchList(); // Reload from API if local update fails
                    });
            },
            error: (error) => {
                let message =
                    error.status === 400
                        ? `Cannot update user. User with new Id already exists (${this.userEdit.id}).`
                        : `Unable to update user (Error: ${error.message})`;

                if (error.status !== 401) {
                    this._toasts.error(message, 'Error');
                }
                console.log(error);
            }
        });
    }

    private deleteUser(e) {
        this._users.delete(this.userEdit.id).subscribe({
            next: (success) => {
                this._toasts.success(
                    `User successfully deleted (${this.userEdit.id})`,
                    'Success'
                );
                this.usersDataSource
                    .store()
                    .remove(this.userEdit.id)
                    .then(
                        (key) => {
                            this.setDefaultFormState(e);
                            this.searchList.instance.refresh();
                        },
                        (error) => {
                            this.loadSearchList(); // since local update failed, just reload from api
                        }
                    );
            },
            error: (error) => {
                console.error(error);
                const message = `Unable to delete user (${error.statusText})`;
                this._toasts.error(message, 'Error');
            }
        });
    }

    onDistrictsChanged = (e) => {
        this.filterAssetManagementSitesByDistrict(e.value);
    }

    filterAssetManagementSitesByDistrict = (districtCode: any[]) => {
        const selecteddistrict: string[] = districtCode || [];
        if (selecteddistrict?.length === 0) {
            this.allAssetManagementSites$.subscribe((data) => {
                this.assetManagementSites$ = of(data);
            });
            return;
        }
        const matchingLocations = this.clientLocationData?.filter(location => {
            const code = location.costcentername.split(" - ")[0]; // matching costcentername prefix - district code.
            return selecteddistrict.includes(code);
        });
        const matchingLocationIds = matchingLocations?.map(loc => loc.locationid);
        const filteredAssetSiteIds$ = this.allAssetManagementSites$.pipe(
            map(sites => sites.filter(site => matchingLocationIds?.includes(site.locationid))
            ));
        filteredAssetSiteIds$?.subscribe((data) => {
            this.assetManagementSites$ = of(data);
        });
    }
}
