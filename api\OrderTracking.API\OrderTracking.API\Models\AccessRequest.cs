﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using ClientPortal.Shared.Models;
using Newtonsoft.Json;

namespace OrderTracking.API.Models
{
    /// <summary>
    ///     Class that represents a request for feature module access in OneInsight
    /// </summary>
    public class AccessRequest
    {
        /// <summary>
        ///     Feature modules requested
        /// </summary>
        [JsonProperty(PropertyName = "modules")]
        public ICollection<string> Modules { get; set; }

        /// <summary>
        ///     District Ids requested
        /// </summary>
        [JsonProperty(PropertyName = "districts")]
        public ICollection<string> Districts { get; set; }

        [JsonProperty(PropertyName = "companies")]
        public ICollection<string> Companies { get; set; }

        /// <summary>
        ///     Manufacturing User, Engineering User, or District Manager roles requested
        /// </summary>
        [JsonProperty(PropertyName = "wmoSpecialAccess")]
        public string WMOSpecialAccess { get; set; }

        /// <summary>
        ///     Company and Site name provided by user
        /// </summary>
        [JsonProperty(PropertyName = "companyNameAndSite")]
        public string CompanyNameAndSite { get; set; }

        /// <summary>
        ///     TEAM Contact provided by user
        /// </summary>
        [JsonProperty(PropertyName = "teamContact")]
        public string TEAMContact { get; set; }

        /// <summary>
        ///     Management Sites Provided By User
        /// </summary>
        [JsonProperty(PropertyName = "managementSites")]
        public string ManagementSites { get; set; }

        /// <summary>
        ///     Generate HTML Message to be sent in an email for access request.
        /// </summary>
        /// <param name="user"></param>
        /// <returns></returns>
        public string HtmlContent(UserProfile user)
        {
            if (user == null) throw new ArgumentNullException(nameof(user));

            var sb = new StringBuilder();
            sb.AppendLine($"<p>Access request generated for {user.Id}</p>")
                .AppendLine($"<p><em>Name:</em> <strong>{user.GivenName} {user.Surname}</strong></p>")
                .AppendLine("<p><em>Modules requested:</em>")
                .AppendLine("<ul>");
            foreach (var module in Modules) sb.AppendLine($"<li><strong>{module}</strong></li>");

            sb.AppendLine("</ul>");

            if (user.IsTeamEmployee)
            {
                sb.AppendLine($"<p><em>WMO special access requested:</em> <strong>{WMOSpecialAccess}</strong>");

                sb.AppendLine("<p><em>District(s) requested:</em>");
                
                if (Districts != null && Districts.Any())
                {
                    sb.AppendLine("<ul>");
                    foreach (var district in Districts) sb.AppendLine($"<li><strong>{district}</strong></li>");

                    sb.AppendLine("</ul>");
                }

                sb.AppendLine($"<p><em>Company(s) requested:</em>");
                if (Companies != null && Companies.Any())
                {
                    sb.AppendLine("<ul>");
                    foreach (var company in Companies) sb.AppendLine($"<li><strong>{company}</strong></li>");

                    sb.AppendLine("</ul>");
                }
            }
            else
            {
                sb.AppendLine($"<p><em>Company:</em> <strong>{CompanyNameAndSite}</strong>");
                sb.AppendLine($"<p><em>TEAM Contact:</em> <strong>{TEAMContact}</strong>");
            }

            if (!string.IsNullOrWhiteSpace(ManagementSites))
            {
                sb.AppendLine($"<p><em>Client and Site names requested:</em> <strong>{ManagementSites}</strong>");
            }

            return sb.ToString();
        }
    }
}