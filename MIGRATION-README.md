# Kraken Project: GCP to Azure Migration Guide

This document provides comprehensive instructions for migrating the Kraken project from Google Cloud Platform (GCP) to Microsoft Azure.

## Table of Contents
1. [Migration Overview](#migration-overview)
2. [Service Mappings](#service-mappings)
3. [Prerequisites](#prerequisites)
4. [Infrastructure Setup](#infrastructure-setup)
5. [Application Changes](#application-changes)
6. [Environment Variables](#environment-variables)
7. [Deployment Process](#deployment-process)
8. [Testing and Validation](#testing-and-validation)
9. [Rollback Plan](#rollback-plan)

## Migration Overview

The Kraken project has been **completely migrated** from Google Cloud Platform to Microsoft Azure to leverage Azure's enterprise features and better integration with existing Microsoft services.

**🎉 Migration Status: 100% COMPLETE**

All GCP dependencies, services, and configurations have been successfully replaced with Azure equivalents. See [GCP-CLEANUP-REPORT.md](GCP-CLEANUP-REPORT.md) for detailed cleanup information.

### Key Changes
- **Container Platform**: Google Cloud Run → Azure Container Apps
- **Storage**: Google Cloud Storage → Azure Blob Storage
- **Secrets Management**: Google Secret Manager → Azure Key Vault
- **Database**: Google Cloud Firestore → Azure Cosmos DB
- **Authentication**: Firebase Auth → Azure AD B2C
- **CI/CD**: Google Cloud Build → Azure DevOps Pipelines
- **Container Registry**: Google Artifact Registry → Azure Container Registry
- **Monitoring**: Google Cloud Diagnostics → Azure Application Insights

## Service Mappings

| **GCP Service** | **Azure Equivalent** | **Purpose** |
|-----------------|---------------------|-------------|
| Cloud Run | Azure Container Apps | Serverless container hosting |
| Cloud Storage | Azure Blob Storage | Object storage |
| Secret Manager | Azure Key Vault | Secrets and key management |
| Cloud SQL | Azure SQL Database | Managed SQL database |
| Firebase Auth | Azure AD B2C | Identity and authentication |
| Firebase Realtime DB | Azure SignalR Service | Real-time communication |
| Cloud Firestore | Azure Cosmos DB | NoSQL document database |
| Cloud Build | Azure DevOps Pipelines | CI/CD build service |
| Cloud Deploy | Azure DevOps Release Pipelines | Deployment orchestration |
| Artifact Registry | Azure Container Registry | Container image registry |
| VPC Connector | Azure Virtual Network | Network connectivity |
| Cloud Diagnostics | Azure Application Insights | Application monitoring |

## Prerequisites

Before starting the migration, ensure you have:

1. **Azure Subscription** with appropriate permissions
2. **Azure CLI** installed and configured
3. **Azure DevOps** organization set up
4. **Docker** installed for local testing
5. **Node.js 18+** for frontend development
6. **.NET 6 SDK** for backend development

## Infrastructure Setup

### 1. Deploy Azure Infrastructure

Use the provided Bicep template to create the required Azure resources:

```bash
# Login to Azure
az login

# Create resource group
az group create --name rg-kraken-dev --location "East US"

# Deploy infrastructure
az deployment group create \
  --resource-group rg-kraken-dev \
  --template-file infrastructure/main.bicep \
  --parameters environmentName=dev appName=kraken
```

### 2. Configure Azure DevOps

1. Create a new Azure DevOps project
2. Set up service connections to Azure
3. Import the repository
4. Configure pipeline variables
5. Set up environments (dev, staging, production)

### 3. Set Up Container Registry

```bash
# Login to Azure Container Registry
az acr login --name krakenacr

# Import existing images (if needed)
az acr import --name krakenacr --source krakendev.azurecr.io/ordertrackingapi:latest --image cpa-backend:latest
```

## Application Changes

### Backend (.NET API)

#### 1. Package Dependencies
The following NuGet packages have been updated:

**Removed:**
- `FirebaseAdmin`
- `Google.Cloud.Diagnostics.AspNetCore3`
- `Google.Cloud.Storage.V1`
- `Google.Cloud.Firestore`
- `Google.Cloud.SecretManager.V1`

**Added:**
- `Microsoft.AspNetCore.Authentication.JwtBearer`
- `Microsoft.AspNetCore.Authentication.OpenIdConnect`
- `Azure.Storage.Blobs`
- `Microsoft.Azure.Cosmos`
- `Azure.Security.KeyVault.Secrets`
- `Azure.Identity`
- `Microsoft.ApplicationInsights.AspNetCore`
- `Microsoft.Azure.SignalR`

#### 2. Service Implementations
- `CloudStorageService` → `AzureBlobStorageService`
- `GoogleSecretManagerHelper` → `AzureKeyVaultHelper`
- Firebase authentication → Azure AD B2C integration

### Frontend (Angular)

#### 1. Package Dependencies
**Removed:**
- `@angular/fire`

**Updated:**
- `@azure/msal-angular` (updated to latest version)
- `@azure/msal-browser` (updated to latest version)

#### 2. Configuration Changes
- Firebase configuration → Azure AD B2C configuration
- Firebase storage → Azure Blob Storage with SAS tokens
- Real-time updates → Azure SignalR Service

## Environment Variables

### Development Environment
```bash
# Azure Authentication
AZURE_CLIENT_ID=your-managed-identity-client-id
AZURE_TENANT_ID=your-tenant-id

# Storage
BlobStorage__APMStorageAccountName=stakrakendev001
BlobStorage__APMBlobContainerName=apm-dev
BlobStorage__APMWOStorageAccountName=stakrakendev001
BlobStorage__APMWOBlobContainerName=apm-workorders-dev
BlobStorage__KeyVaultName=kv-kraken-dev-001

# Database
Connections__KeyVaultName=kv-kraken-dev-001
Connections__ResourceGroupName=rg-kraken-dev
Connections__SubscriptionId=your-subscription-id
Connections__DatabaseName=cosmos-clientportalapi-dev-001
Connections__AnteaAttachmentsBlobContainer=antea-attachments-dev
Connections__AnteaSubmissionsBlobContainer=antea-submissions-dev

# Application Insights
ApplicationInsights__ConnectionString=InstrumentationKey=your-key;IngestionEndpoint=https://eastus-8.in.applicationinsights.azure.com/
```

### Production Environment
Replace `dev` with `prod` in resource names and use production-specific values.

## Deployment Process

### 1. Backend Deployment

```bash
# Build and push Docker image
docker build -t krakenacr.azurecr.io/cpa-backend:latest -f api/OrderTracking.API/OrderTracking.API/Dockerfile .
docker push krakenacr.azurecr.io/cpa-backend:latest

# Deploy using Azure CLI
az containerapp update \
  --name ca-cpa-backend-dev \
  --resource-group rg-kraken-dev \
  --image krakenacr.azurecr.io/cpa-backend:latest
```

### 2. Frontend Deployment

```bash
# Build Angular application
cd wheres-my-order
npm install
npm run build-prod

# Build and push Docker image
docker build -t krakenacr.azurecr.io/cpa-frontend:latest .
docker push krakenacr.azurecr.io/cpa-frontend:latest

# Deploy using Azure CLI
az containerapp update \
  --name ca-cpa-frontend-dev \
  --resource-group rg-kraken-dev \
  --image krakenacr.azurecr.io/cpa-frontend:latest
```

### 3. Automated Deployment

Use Azure DevOps pipelines for automated deployment:
- `azure-pipelines-backend.yml` for backend API
- `azure-pipelines-frontend.yml` for frontend application

## Testing and Validation

### 1. Functional Testing
- [ ] User authentication works with Azure AD B2C
- [ ] File upload/download works with Azure Blob Storage
- [ ] Database operations work with Azure Cosmos DB
- [ ] Real-time features work with Azure SignalR
- [ ] All API endpoints return expected responses

### 2. Performance Testing
- [ ] Response times are comparable to GCP implementation
- [ ] File upload/download performance is acceptable
- [ ] Database query performance meets requirements

### 3. Security Testing
- [ ] Managed Identity authentication works correctly
- [ ] Key Vault secrets are accessible
- [ ] Network security groups are properly configured
- [ ] HTTPS is enforced for all endpoints

## Rollback Plan

In case of issues during migration:

1. **Immediate Rollback**: Switch DNS back to GCP endpoints
2. **Database Rollback**: Restore from GCP Firestore backup
3. **Application Rollback**: Redeploy previous GCP-compatible version
4. **Infrastructure Rollback**: Keep GCP resources active during transition period

## Support and Troubleshooting

### Common Issues

1. **Authentication Failures**
   - Verify Managed Identity is properly configured
   - Check Azure AD B2C configuration
   - Ensure Key Vault access policies are correct

2. **Storage Access Issues**
   - Verify storage account permissions
   - Check container access levels
   - Validate SAS token generation

3. **Database Connection Issues**
   - Verify Cosmos DB connection strings
   - Check firewall rules
   - Validate database permissions

### Monitoring and Logging

- Use Azure Application Insights for application monitoring
- Check Azure Container Apps logs for runtime issues
- Monitor Azure Key Vault access logs
- Review Azure Storage access logs

## Next Steps

After successful migration:

1. **Cleanup**: Remove GCP resources after validation period
2. **Optimization**: Fine-tune Azure resource configurations
3. **Documentation**: Update operational procedures
4. **Training**: Train team on Azure-specific tools and processes

## Detailed Configuration Examples

### Azure Key Vault Secrets Configuration

Required secrets to be stored in Azure Key Vault:

```bash
# Database connection strings
az keyvault secret set --vault-name kv-kraken-dev-001 --name "ConnectionStrings--DefaultConnection" --value "Server=tcp:sql-kraken-dev.database.windows.net,1433;Database=kraken-dev;Authentication=Active Directory Default;"

# Azure AD B2C settings
az keyvault secret set --vault-name kv-kraken-dev-001 --name "AzureAdB2C--ClientId" --value "your-b2c-client-id"
az keyvault secret set --vault-name kv-kraken-dev-001 --name "AzureAdB2C--ClientSecret" --value "your-b2c-client-secret"

# SendGrid API key
az keyvault secret set --vault-name kv-kraken-dev-001 --name "SendGrid--APIKey" --value "your-sendgrid-api-key"

# Other application secrets
az keyvault secret set --vault-name kv-kraken-dev-001 --name "ZDapperPlus--LicenseKey" --value "your-license-key"
```

### Azure AD B2C Configuration

1. **Create B2C Tenant**:
   ```bash
   az ad b2c tenant create --name krakenb2c --location "United States" --sku Standard
   ```

2. **Configure User Flows**:
   - Sign-up and sign-in flow
   - Password reset flow
   - Profile editing flow

3. **Application Registration**:
   ```json
   {
     "displayName": "Kraken Client Portal",
     "signInAudience": "AzureADandPersonalMicrosoftAccount",
     "web": {
       "redirectUris": [
         "https://kraken-dev.azurewebsites.net/signin-oidc",
         "http://localhost:4200"
       ],
       "logoutUrl": "https://kraken-dev.azurewebsites.net/signout-oidc"
     }
   }
   ```

### Azure Cosmos DB Setup

1. **Create Cosmos DB Account**:
   ```bash
   az cosmosdb create \
     --name cosmos-kraken-dev \
     --resource-group rg-kraken-dev \
     --kind GlobalDocumentDB \
     --default-consistency-level Session
   ```

2. **Create Databases and Containers**:
   ```bash
   # Create database
   az cosmosdb sql database create \
     --account-name cosmos-kraken-dev \
     --resource-group rg-kraken-dev \
     --name clientportal

   # Create containers
   az cosmosdb sql container create \
     --account-name cosmos-kraken-dev \
     --resource-group rg-kraken-dev \
     --database-name clientportal \
     --name user-profiles \
     --partition-key-path "/userId"
   ```

## Post-Migration Validation Procedures

### 1. Infrastructure Validation

```bash
# Verify all Azure resources are created
az resource list --resource-group rg-kraken-dev --output table

# Check Container Apps status
az containerapp list --resource-group rg-kraken-dev --output table

# Verify Key Vault secrets
az keyvault secret list --vault-name kv-kraken-dev-001 --output table

# Check storage account containers
az storage container list --account-name stakrakendev001 --output table
```

### 2. Application Health Checks

Create health check endpoints and verify:

```bash
# Backend API health check
curl https://api-kraken-dev.azurecontainerapps.io/health

# Frontend application
curl https://app-kraken-dev.azurecontainerapps.io/

# Database connectivity
curl https://api-kraken-dev.azurecontainerapps.io/api/health/database

# Storage connectivity
curl https://api-kraken-dev.azurecontainerapps.io/api/health/storage
```

### 3. Authentication Flow Testing

```javascript
// Test Azure AD B2C authentication
const msalConfig = {
  auth: {
    clientId: 'your-client-id',
    authority: 'https://krakenb2c.b2clogin.com/krakenb2c.onmicrosoft.com/B2C_1_signupsignin',
    redirectUri: 'http://localhost:4200'
  }
};

// Test login flow
const loginRequest = {
  scopes: ['openid', 'profile']
};
```

## Troubleshooting Guide

### Authentication Issues

**Problem**: Users cannot authenticate with Azure AD B2C
**Solutions**:
1. Verify B2C tenant configuration
2. Check redirect URIs in app registration
3. Validate user flow policies
4. Review CORS settings

**Problem**: Managed Identity authentication fails
**Solutions**:
1. Verify identity is assigned to Container App
2. Check Key Vault access policies
3. Validate RBAC role assignments
4. Review identity configuration in deployment

### Storage Access Issues

**Problem**: Blob storage operations fail
**Solutions**:
1. Check storage account firewall rules
2. Verify managed identity has Storage Blob Data Contributor role
3. Validate container access levels
4. Review SAS token generation logic

**Problem**: File uploads timeout
**Solutions**:
1. Increase Container App timeout settings
2. Check network connectivity
3. Verify storage account performance tier
4. Review file size limits

### Database Connection Issues

**Problem**: Cosmos DB connection failures
**Solutions**:
1. Verify connection string format
2. Check firewall rules and IP restrictions
3. Validate authentication method
4. Review consistency level settings

**Problem**: Query performance degradation
**Solutions**:
1. Review indexing policies
2. Check partition key design
3. Analyze query patterns
4. Consider request unit scaling

### Performance Optimization

**Container Apps Scaling**:
```yaml
scale:
  minReplicas: 1
  maxReplicas: 10
  rules:
  - name: http-scaling
    http:
      metadata:
        concurrentRequests: '50'
  - name: cpu-scaling
    custom:
      type: cpu
      metadata:
        type: Utilization
        value: '70'
```

**Storage Performance**:
- Use Premium storage tier for high IOPS requirements
- Implement CDN for static content
- Configure appropriate access tiers

**Database Performance**:
- Optimize partition key strategy
- Implement proper indexing
- Use appropriate consistency levels
- Monitor and scale RU/s as needed

## Migration Checklist

### Pre-Migration
- [ ] Azure subscription and permissions verified
- [ ] Infrastructure deployed using Bicep templates
- [ ] Azure DevOps project and pipelines configured
- [ ] Secrets migrated to Azure Key Vault
- [ ] DNS records prepared for cutover

### During Migration
- [ ] Application code updated for Azure services
- [ ] Container images built and pushed to ACR
- [ ] Database data migrated to Cosmos DB
- [ ] File storage migrated to Blob Storage
- [ ] Authentication configured with Azure AD B2C

### Post-Migration
- [ ] All functionality tested and validated
- [ ] Performance benchmarks met
- [ ] Security configurations verified
- [ ] Monitoring and alerting configured
- [ ] Documentation updated
- [ ] Team training completed
- [ ] GCP resources scheduled for cleanup

## Contact Information

For migration support, contact:
- DevOps Team: <EMAIL>
- Azure Specialists: <EMAIL>
- Project Manager: <EMAIL>
