import { Injectable, OnD<PERSON>roy } from '@angular/core';
import { MsalBroadcastService, MsalService } from '@azure/msal-angular';
import {
    AccountInfo,
    AuthenticationResult,
    EventMessage,
    EventType,
    InteractionStatus
} from '@azure/msal-browser';
import { Observable, ReplaySubject, Subject, Subscription } from 'rxjs';
import { filter, takeUntil } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { IndexedDbService } from '../../aimaas/services/indexed-db.service';
import { debug } from '../../core/operators';

@Injectable({
    providedIn: 'root'
})
export class AuthService implements OnDestroy {
    get isLoggedIn(): boolean {
        return !!this.user;
    }

    get loginFailure$(): Observable<any> {
        return this._loginFailure$.asObservable().pipe(debug('loginFailure$'));
    }

    get loginSuccess$(): Observable<any> {
        return this._loginSuccess$.asObservable().pipe(debug('loginSuccess$'));
    }
    get finishedInteraction$(): Observable<void> {
        return this._interactionFinished$
            .asObservable()
            .pipe(debug('Msal Finished Interaction'));
    }

    get acquireTokenSuccess$(): Observable<AuthenticationResult> {
        return this._acquireTokenSuccess$
            .asObservable()
            .pipe(debug('acquireTokenSuccess$'));
    }

    get acquireTokenFailure$(): Observable<any> {
        return this._acquireTokenFailure$
            .asObservable()
            .pipe(debug('acquireTokenFailure$'));
    }

    get user(): AccountInfo {
        const user = this._msal.instance.getActiveAccount();
        return user;
    }
    private readonly _destroying$ = new Subject<void>();
    private readonly _interactionFinished$ = new ReplaySubject<void>();
    private readonly _loginFailure$ = new ReplaySubject<any>();
    private readonly _loginSuccess$ = new ReplaySubject<any>();
    private readonly _acquireTokenSuccess$ =
        new ReplaySubject<AuthenticationResult>();
    private readonly _acquireTokenFailure$ = new ReplaySubject<any>();
    private readonly _subscriptions: Subscription[] = [];

    constructor(
        private readonly _msal: MsalService,
        private readonly _broadcasts: MsalBroadcastService,
        private indexedDbService: IndexedDbService
    ) {
        this._subscriptions.push(
            this._broadcasts.msalSubject$
                .pipe(
                    filter(
                        (msg: EventMessage) =>
                            msg.eventType === EventType.LOGIN_FAILURE
                    )
                )
                .subscribe((result: EventMessage) => {
                    const payload = result.payload as AuthenticationResult;
                    this.loginFailureCallback(payload);
                }),
            this._broadcasts.msalSubject$
                .pipe(
                    filter(
                        (msg: EventMessage) =>
                            msg.eventType === EventType.LOGIN_SUCCESS
                    )
                )
                .subscribe((result: EventMessage) => {
                    const payload = result.payload as AuthenticationResult;
                    this.loginSuccessCallback(payload);
                }),
            this._broadcasts.msalSubject$
                .pipe(
                    filter(
                        (msg: EventMessage) =>
                            msg.eventType === EventType.ACQUIRE_TOKEN_SUCCESS
                    )
                )
                .subscribe((result: EventMessage) => {
                    const payload = result.payload as AuthenticationResult;
                    this.handleTokenSuccess(payload);
                }),
            this._broadcasts.msalSubject$
                .pipe(
                    filter(
                        (msg: EventMessage) =>
                            msg.eventType === EventType.ACQUIRE_TOKEN_FAILURE
                    )
                )
                .subscribe((result: EventMessage) => {
                    const payload = result.payload as AuthenticationResult;
                    this.handleTokenFailure(payload);
                })
        );
        this._broadcasts.inProgress$
            .pipe(
                filter(
                    (status: InteractionStatus) =>
                        status === InteractionStatus.None
                ),
                takeUntil(this._destroying$)
            )
            .subscribe(() => {
                this._interactionFinished$.next();
            });
    }
    login() {
        this._msal.loginRedirect();
    }

    logout() {
        localStorage.clear();
        sessionStorage.clear();
        this.indexedDbService.clear();
        this._msal.logout();
    }

    forgotPassword() {
        this._msal.loginRedirect({
            scopes: ['openid', 'profile'],
            authority: environment.msal.forgotPasswordAuthority
        });
    }

    unsubscribe() {
        this._subscriptions?.forEach((subscription) =>
            subscription?.unsubscribe()
        );
    }

    checkAndSetActiveAccount() {
        /**
         * If no active account set but there are accounts signed in, sets first account to active account
         * To use active account set here, subscribe to inProgress$ first in your component
         * Note: Basic usage demonstrated. Your app may require more complicated account selection logic
         */
        const activeAccount = this._msal.instance.getActiveAccount();

        if (!activeAccount && this._msal.instance.getAllAccounts().length > 0) {
            const accounts = this._msal.instance.getAllAccounts();
            this._msal.instance.setActiveAccount(accounts[0]);
        }
    }

    ngOnDestroy(): void {
        this._destroying$.next(null);
        this._destroying$.complete();
    }

    private loginSuccessCallback = (payload: AuthenticationResult) => {
        this._msal.instance.setActiveAccount(payload.account);
        this._loginSuccess$.next(payload);
    };

    private loginFailureCallback = (payload: AuthenticationResult) => {
        this._loginFailure$.next(payload);
    };

    private handleTokenSuccess = (payload: AuthenticationResult) => {
        this._msal.instance.setActiveAccount(payload.account);
        this._acquireTokenSuccess$.next(payload);
    };

    private handleTokenFailure = (payload: AuthenticationResult) => {
        this._acquireTokenFailure$.next(payload);
        this._msal.loginRedirect();
    };
}
