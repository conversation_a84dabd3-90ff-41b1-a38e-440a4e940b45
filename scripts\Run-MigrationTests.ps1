# Migration Testing Script
# Runs comprehensive tests to validate the GCP to Azure migration

param(
    [Parameter(Mandatory=$false)]
    [string]$Environment = "dev",
    
    [Parameter(Mandatory=$false)]
    [switch]$RunUnitTests,
    
    [Parameter(Mandatory=$false)]
    [switch]$RunIntegrationTests,
    
    [Parameter(Mandatory=$false)]
    [switch]$RunValidation,
    
    [Parameter(Mandatory=$false)]
    [switch]$All
)

$ErrorActionPreference = "Stop"

Write-Host "🚀 Starting Migration Testing Suite" -ForegroundColor Green
Write-Host "Environment: $Environment" -ForegroundColor Cyan

if ($All) {
    $RunUnitTests = $true
    $RunIntegrationTests = $true
    $RunValidation = $true
}

# Function to run tests and capture results
function Invoke-TestSuite {
    param(
        [string]$TestName,
        [scriptblock]$TestScript
    )
    
    Write-Host "`n📋 Running $TestName..." -ForegroundColor Yellow
    
    try {
        $result = & $TestScript
        Write-Host "✅ $TestName completed successfully" -ForegroundColor Green
        return $true
    } catch {
        Write-Host "❌ $TestName failed: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

$testResults = @{}

# 1. Unit Tests
if ($RunUnitTests) {
    $testResults["UnitTests"] = Invoke-TestSuite "Unit Tests" {
        Write-Host "Running .NET unit tests..."
        
        # Backend API tests
        Set-Location "api/OrderTracking.API"
        dotnet test --configuration Release --logger "console;verbosity=detailed"
        
        # Frontend tests
        Set-Location "../../wheres-my-order"
        npm test -- --watch=false --browsers=ChromeHeadless
        
        Set-Location ".."
    }
}

# 2. Integration Tests
if ($RunIntegrationTests) {
    $testResults["IntegrationTests"] = Invoke-TestSuite "Integration Tests" {
        Write-Host "Running Azure integration tests..."
        
        # Set environment variables for testing
        $env:AZURE_CLIENT_ID = "test-client-id"
        $env:ASPNETCORE_ENVIRONMENT = "Testing"
        
        # Run Azure-specific integration tests
        Set-Location "tests"
        dotnet test AzureIntegrationTests.cs --configuration Release --logger "console;verbosity=detailed"
        Set-Location ".."
    }
}

# 3. Infrastructure Validation
if ($RunValidation) {
    $testResults["Validation"] = Invoke-TestSuite "Infrastructure Validation" {
        Write-Host "Running infrastructure validation..."
        
        # Run the validation script
        & "scripts/Validate-AzureMigration.ps1" -Environment $Environment -ResourceGroupName "rg-kraken-$Environment"
    }
}

# 4. Functional Tests
$testResults["FunctionalTests"] = Invoke-TestSuite "Functional Tests" {
    Write-Host "Running functional tests..."
    
    # Test authentication flow
    Write-Host "Testing authentication..."
    $authTest = Test-AuthenticationFlow -Environment $Environment
    
    # Test file upload/download
    Write-Host "Testing file operations..."
    $fileTest = Test-FileOperations -Environment $Environment
    
    # Test database operations
    Write-Host "Testing database operations..."
    $dbTest = Test-DatabaseOperations -Environment $Environment
    
    return $authTest -and $fileTest -and $dbTest
}

# Helper functions for functional tests
function Test-AuthenticationFlow {
    param([string]$Environment)
    
    try {
        # Test Azure AD B2C configuration
        $b2cConfig = Get-Content "wheres-my-order/src/environments/environment.ts" | Select-String "azureAd"
        if ($b2cConfig) {
            Write-Host "✅ Azure AD B2C configuration found"
            return $true
        } else {
            Write-Host "❌ Azure AD B2C configuration missing"
            return $false
        }
    } catch {
        Write-Host "❌ Authentication test failed: $($_.Exception.Message)"
        return $false
    }
}

function Test-FileOperations {
    param([string]$Environment)
    
    try {
        # Test blob storage configuration
        $blobConfig = Get-Content "api/OrderTracking.API/OrderTracking.API/appsettings.json" | ConvertFrom-Json
        if ($blobConfig.BlobStorage.APMStorageAccountName) {
            Write-Host "✅ Azure Blob Storage configuration found"
            return $true
        } else {
            Write-Host "❌ Azure Blob Storage configuration missing"
            return $false
        }
    } catch {
        Write-Host "❌ File operations test failed: $($_.Exception.Message)"
        return $false
    }
}

function Test-DatabaseOperations {
    param([string]$Environment)
    
    try {
        # Test Cosmos DB configuration
        $dbConfig = Get-Content "api/OrderTracking.API/OrderTracking.API/appsettings.json" | ConvertFrom-Json
        if ($dbConfig.Connections.DatabaseName -like "*cosmos*") {
            Write-Host "✅ Azure Cosmos DB configuration found"
            return $true
        } else {
            Write-Host "❌ Azure Cosmos DB configuration missing"
            return $false
        }
    } catch {
        Write-Host "❌ Database operations test failed: $($_.Exception.Message)"
        return $false
    }
}

# 5. Performance Tests
$testResults["PerformanceTests"] = Invoke-TestSuite "Performance Tests" {
    Write-Host "Running performance tests..."
    
    # Test API response times
    $apiUrl = "https://api-kraken-$Environment.azurecontainerapps.io"
    
    try {
        $stopwatch = [System.Diagnostics.Stopwatch]::StartNew()
        $response = Invoke-WebRequest -Uri "$apiUrl/health" -Method GET -TimeoutSec 30
        $stopwatch.Stop()
        
        $responseTime = $stopwatch.ElapsedMilliseconds
        Write-Host "API Response Time: $responseTime ms"
        
        if ($responseTime -lt 5000) {
            Write-Host "✅ API performance acceptable"
            return $true
        } else {
            Write-Host "⚠️ API response time high: $responseTime ms"
            return $false
        }
    } catch {
        Write-Host "❌ Performance test failed: $($_.Exception.Message)"
        return $false
    }
}

# 6. Security Tests
$testResults["SecurityTests"] = Invoke-TestSuite "Security Tests" {
    Write-Host "Running security tests..."
    
    try {
        # Test HTTPS enforcement
        $apiUrl = "http://api-kraken-$Environment.azurecontainerapps.io"
        
        try {
            $response = Invoke-WebRequest -Uri "$apiUrl/health" -Method GET -TimeoutSec 10
            Write-Host "⚠️ HTTP endpoint accessible (should redirect to HTTPS)"
        } catch {
            Write-Host "✅ HTTP endpoint properly secured"
        }
        
        # Test Key Vault access
        $kvName = "kv-kraken-$Environment-001"
        $secrets = az keyvault secret list --vault-name $kvName --query "length(@)" -o tsv 2>$null
        
        if ($secrets -gt 0) {
            Write-Host "✅ Key Vault accessible with proper authentication"
            return $true
        } else {
            Write-Host "❌ Key Vault access test failed"
            return $false
        }
    } catch {
        Write-Host "❌ Security test failed: $($_.Exception.Message)"
        return $false
    }
}

# Generate Test Report
Write-Host "`n📊 Test Results Summary" -ForegroundColor Cyan
Write-Host "========================" -ForegroundColor Cyan

$passedTests = 0
$totalTests = $testResults.Count

foreach ($test in $testResults.GetEnumerator()) {
    $status = if ($test.Value) { "✅ PASSED" } else { "❌ FAILED" }
    $color = if ($test.Value) { "Green" } else { "Red" }
    
    Write-Host "$($test.Key): $status" -ForegroundColor $color
    
    if ($test.Value) { $passedTests++ }
}

Write-Host "`nOverall Results: $passedTests/$totalTests tests passed" -ForegroundColor Cyan

# Save detailed report
$timestamp = Get-Date -Format "yyyy-MM-dd_HH-mm-ss"
$reportPath = "migration-test-report-$Environment-$timestamp.json"

$report = @{
    Timestamp = Get-Date
    Environment = $Environment
    TestResults = $testResults
    Summary = @{
        TotalTests = $totalTests
        PassedTests = $passedTests
        FailedTests = ($totalTests - $passedTests)
        SuccessRate = [math]::Round(($passedTests / $totalTests) * 100, 2)
    }
}

$report | ConvertTo-Json -Depth 3 | Out-File -FilePath $reportPath
Write-Host "`nDetailed test report saved to: $reportPath" -ForegroundColor Yellow

# Exit with appropriate code
if ($passedTests -eq $totalTests) {
    Write-Host "`n🎉 All tests passed! Migration validation successful." -ForegroundColor Green
    exit 0
} else {
    Write-Host "`n⚠️ Some tests failed. Please review the results and fix issues before proceeding." -ForegroundColor Yellow
    exit 1
}
