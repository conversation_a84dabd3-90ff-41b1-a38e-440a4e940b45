import { Component, Input, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { DxChartComponent } from 'devextreme-angular/ui/chart';
import { DxPieChartComponent } from 'devextreme-angular/ui/pie-chart';
import {
    AggregateAssetAndInspectionWithCategory,
    AggregateAssetType,
    Asset,
    AssetInspection
} from '../../models';

@Component({
    selector: 'app-equipment-dashboard',
    templateUrl: './equipment-dashboard.component.html',
    styleUrls: ['./equipment-dashboard.component.scss']
})
export class EquipmentDashboardComponent {
    @ViewChild('assetTypesByAreaIdChart')
    assetTypesByAreaIdChart: DxChartComponent;
    @ViewChild('assetAndInspectionPie')
    assetAndInspectionPie: DxPieChartComponent;

    @Input() set inspections(value: AssetInspection[]) {
        this._inspections = value;
        this.updateDashboard();
    }

    @Input() set assets(value: Asset[]) {
        this._assets = value;
        this.assetsKPI = value;
        this.updateDashboard();
    }

    totalAssets = 0;
    assetInspectionCount = 0;
    assetInspectionObjIds: any[] = [];
    filteredInspections: AssetInspection[] = [];
    currentAssetInspectionCount = 0;
    currentAssetInspectionObjIds: any[] = [];
    overdueAssetInspectionCount = 0;
    overdueAssetInspectionObjIds: any[] = [];
    noInspectionAssetCount = 0;
    noInspectionAssetObjIds: any[] = [];
    resolveOverlappingTypes = ['shift', 'hide', 'none'];
    aggregateAssetAndInspectionWithCategory: AggregateAssetAndInspectionWithCategory[] =
        [];
    assetTypesByAreaId: AggregateAssetType[] = [];
    objcats: string[] = [];
    isChecked: boolean = true;
    breadCrumbValue: string = '';
    private _inspections: any[];
    private _assets: Asset[];
    private assetsKPI: Asset[];

    constructor(private readonly _router: Router) {}

    renderCharts() {
        this.assetTypesByAreaIdChart?.instance?.render();
        this.assetAndInspectionPie?.instance?.render();
    }
    onValueChanged(e: any) {
        if (this.isChecked == true) {
            this._assets = this.assetsKPI;
            this.filteredInspections = this._inspections.filter(
                (inspection) => inspection.scheduletype != null
            );
        } else {
            this.filteredInspections = this._inspections.filter(
                (inspection) =>
                    inspection.assetstatus != 'Removed from Unit' &&
                    inspection.assetstatus != 'Out of Service' &&
                    inspection.scheduletype != null
            );
        }
        const assetIds = this.filteredInspections.map(
            (inspection) => inspection.assetid
        );
        this._assets = this.assetsKPI.filter((asset) =>
            assetIds.includes(String(asset.id))
        );
        this.updateDashboard();
    }
    equipmentDonutChartPointClicked($event) {
        const category = $event.target.data.category;
        let assetObjIds: string[] = [];

        switch (category) {
            case 'Assets with No Inspection':
                assetObjIds = this.noInspectionAssetObjIds;
                this.breadCrumbValue = 'Assets with No Inspection';
                break;
            case 'Assets with Current Inspection':
                assetObjIds = this.currentAssetInspectionObjIds;
                this.breadCrumbValue = 'Assets with Current Inspection';
                break;
            case 'Assets with Overdue Inspection':
                assetObjIds = this.overdueAssetInspectionObjIds;
                this.breadCrumbValue = 'Assets with Overdue Inspection';
                break;
            default:
                throw new Error(`Unknown category: ${category}`);
        }

        this._router.navigate(['/aimaas/drilldown'], {
            state: {
                data: {
                    assetObjIds
                },
                breadCrumbLabel: this.breadCrumbValue
            }
        });
    }

    customizeLabel(arg) {
        return arg.percentText;
    }

    equipmentPointClicked($event) {
        const areaId = $event.target.data.areaname;
        const assettype = $event.target.series.name;
        this.drillDown([
            ['areaname', '=', areaId === 'null' ? null : areaId],
            'and',
            ['assettype', '=', assettype === 'null' ? null : assettype]
        ]);
    }

    customizeArgumentAxisLabel = (e) => {
        return e.valueText;
    };

    private drillDown(currentFilter: any[]) {
        this._router.navigate(['/aimaas/drilldown'], {
            state: {
                data: { currentFilter },
                breadCrumbLabel: 'Equipment by Area and Type'
            }
        });
    }

    private updateDashboard() {
        this.resetChartData();
        if (this.isChecked == true) {
            this._assets = this.assetsKPI;
            this.filteredInspections = this._inspections?.filter(
                (inspection) => inspection.scheduletype != null
            );
        } else {
            this.filteredInspections = this._inspections?.filter(
                (inspection) =>
                    inspection.assetstatus != 'Removed from Unit' &&
                    inspection.assetstatus != 'Out of Service' &&
                    inspection.scheduletype != null
            );
        }
        const assetIds = this.filteredInspections?.map(
            (inspection) => inspection.assetid
        );
        this._assets = this.assetsKPI?.filter((asset) =>
            assetIds?.includes(String(asset.id))
        );
        this.setupAssetBarChart(this.assetsKPI);
        const count = 0;
        this._assets?.forEach((asset) => {
            this.assetInspectionCount++;
            this.assetInspectionObjIds.push(String(asset.assetid));
            const inspectionsOfAsset =
                this._inspections?.filter(
                    (inspection) =>
                        inspection.assetid === asset.id &&
                        inspection.scheduletype != null
                ) ?? [];
            if (inspectionsOfAsset.length != 0) {
                const currentdate = new Date();
                const isCurrentInspection = inspectionsOfAsset?.some(
                    (inspection) =>
                        inspection.nextinspectiondue != null &&
                        currentdate < new Date(inspection.nextinspectiondue)
                );
                const isOverdue = inspectionsOfAsset?.some(
                    (inspection) =>
                        inspection.nextinspectiondue != null &&
                        currentdate > new Date(inspection.nextinspectiondue)
                );
                const isNoScheduled = inspectionsOfAsset?.some(
                    (inspection) => inspection.nextinspectiondue == null
                );

                if (isOverdue) {
                    this.overdueAssetInspectionCount++;
                    this.overdueAssetInspectionObjIds.push(
                        String(asset.assetid)
                    );
                } else {
                    if (isCurrentInspection) {
                        this.currentAssetInspectionCount++;
                        this.currentAssetInspectionObjIds.push(
                            String(asset.assetid)
                        );
                    } else if (isNoScheduled) {
                        this.noInspectionAssetCount++;
                        this.noInspectionAssetObjIds.push(
                            String(asset.assetid)
                        );
                    }
                }
            }
        });

        this.aggregateAssetAndInspectionWithCategory.push({
            category: 'Assets with Current Inspection',
            count: this.currentAssetInspectionCount
        });

        this.aggregateAssetAndInspectionWithCategory.push({
            category: 'Assets with Overdue Inspection',
            count: this.overdueAssetInspectionCount
        });

        this.aggregateAssetAndInspectionWithCategory.push({
            category: 'Assets with No Inspection',
            count: this.noInspectionAssetCount
        });
    }

    private resetChartData() {
        this.assetTypesByAreaId = [];
        this.aggregateAssetAndInspectionWithCategory = [];
        this.totalAssets = 0;
        this.assetInspectionCount = 0;
        this.assetInspectionObjIds = [];
        this.noInspectionAssetCount = 0;
        this.overdueAssetInspectionCount = 0;
        this.currentAssetInspectionCount = 0;
        this.currentAssetInspectionObjIds = [];
        this.overdueAssetInspectionObjIds = [];
        this.noInspectionAssetObjIds = [];
    }

    private setupAssetBarChart(assets: any[]) {
        this.totalAssets = assets.length;
        for (const asset of assets) {
            if (
                asset.sclass !=
                'it.imc.persistence.po.plants.place.LineNumberPlaceStrategy'
            ) {
                const currentAreaId = asset.areaname;
                let aggregateAssetType: AggregateAssetType =
                    this.assetTypesByAreaId.find(
                        (assetTypeByAreaId) =>
                            assetTypeByAreaId.areaname === currentAreaId
                    );
                if (
                    aggregateAssetType === null ||
                    aggregateAssetType === undefined
                ) {
                    aggregateAssetType = { areaname: currentAreaId };
                    this.assetTypesByAreaId.push(aggregateAssetType);
                }

                const objcat = asset.assettype;
                if (objcat === null || objcat === undefined || objcat === '') {
                    continue;
                }

                if (
                    aggregateAssetType[objcat] === null ||
                    aggregateAssetType[objcat] === undefined
                ) {
                    aggregateAssetType[objcat] = 1;
                } else {
                    aggregateAssetType[objcat]++;
                }

                if (!this.objcats.includes(objcat)) {
                    this.objcats.push(objcat);
                }
            }
            this.assetTypesByAreaId.sort((a, b) =>
                a.areaname.localeCompare(b.areaname)
            );
        }
    }
}
