import { Component } from '@angular/core';
import CustomStore from 'devextreme/data/custom_store';
import { ToolbarPreparingEvent } from 'devextreme/ui/data_grid';
import { DataGridService, UsersService } from '../../shared/services';
import { ChangeInfo, CRDAuditEntry } from '../models';
import { CrdService } from '../services/crd.service';

@Component({
    selector: 'app-audit-page',
    templateUrl: './audit-page.component.html',
    styleUrls: ['./audit-page.component.scss']
})
export class AuditPageComponent {
    history = new CustomStore({
        key: 'eventId',
        load: (loadOptions) => this._crd.getAllCRDAuditEntries().toPromise()
    });
    showPopup = false;
    currentUser$ = this._users.currentProfile$;

    currentDiff: any;
    currentChangeInfo: ChangeInfo;

    constructor(
        private readonly _crd: CrdService,
        private readonly _grid: DataGridService,
        private readonly _users: UsersService
    ) {}

    onToolbarPreparing(e: ToolbarPreparingEvent) {
        e.toolbarOptions.items.unshift({
            widget: 'dxButton',
            options: {
                icon: 'fa fa-undo',
                hint: 'Restore Grid Defaults',
                onClick: () => this._grid.resetGridState(e.component)
            },
            location: 'after'
        });
    }

    openDetails = (e: {
        [key: string]: any;
        row: { [key: string]: any; data: CRDAuditEntry };
    }) => {
        this.showPopup = true;
        this.currentDiff = e.row.data.diff;
        this.currentChangeInfo = e.row.data.changeInfo;
    };
}
