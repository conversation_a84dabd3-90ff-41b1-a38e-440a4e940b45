import { Location } from '@angular/common';
import {
    AfterViewInit,
    Component,
    OnDestroy,
    OnInit,
    ViewChild
} from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { DxDataGridComponent } from 'devextreme-angular/ui/data-grid';
import ArrayStore from 'devextreme/data/array_store';
import DataSource from 'devextreme/data/data_source';
import { exportDataGrid } from 'devextreme/excel_exporter';
import dxDataGrid from 'devextreme/ui/data_grid';
import { Workbook } from 'exceljs';
import { saveAs } from 'file-saver';
import { combineLatest, Subscription } from 'rxjs';
import { filter, finalize } from 'rxjs/operators';
import { UserProfile } from '../../../profile/models/user-profile';
import { Breadcrumb } from '../../../shared/components';
import { DataGridService, UsersService } from '../../../shared/services';
import {
    AlarmCalc,
    AnomaliesRecommendations,
    Asset,
    AssetAttachment,
    AssetComponent,
    AssetInspection,
    AssetManagementSite,
    InspectionAttachment,
    RowExpandingHandler
} from '../../models';
import { SiteLabelPipe } from '../../pipes';
import { CredoSoftService } from '../../services';
import { IndexedDbService } from '../../services/indexed-db.service';
import { UtilsService } from '../../services/utils.service';
@Component({
    selector: 'app-fixed-equipment-inspection-drilldown',
    templateUrl: './fixed-equipment-inspection-drilldown.component.html',
    styleUrls: ['./fixed-equipment-inspection-drilldown.component.scss']
})
export class FixedEquipmentInspectionDrilldownComponent
    implements OnInit, AfterViewInit, OnDestroy {
    isLoading: boolean;
    selectedAssetID: any;
    lastDateOfSelectedAsset: any;
    popupVisible = false;
    inspectionDataSource: DataSource;
    assets: Asset[] = [];
    components: any[] = [];
    alarmCalcs: AlarmCalc[] = [];
    inspectionComponentMap: object = {};
    currentFilter: any = [];
    userId: string;
    generalInspectionDetails: AssetInspection[];
    visibleInspectionAnamolies: AnomaliesRecommendations[];
    inspectionAnamolies: AnomaliesRecommendations[] = [];
    corrosionAnalysis: any;
    inspectionAttachments: InspectionAttachment[] = [];
    assetAttachments: AssetAttachment[] = [];
    visibleInspectionAttachments: InspectionAttachment[];
    visibleAssetAttachments: AssetAttachment[];
    tabs: { title: string; template: string }[] = [];
    currentComponentCalcs: AlarmCalc;
    selectedAsset: Asset;
    availableSites: AssetManagementSite[];
    selectedSite: AssetManagementSite;
    submissionPopupVisible: boolean = false;
    initialAnomaly: AnomaliesRecommendations = null;
    submissionPopupTitle: string = 'Anomaly Update';
    breaCrumbLabel: string = 'Inspections List';
    selectedOperationId: any;
    isHideButton: boolean = false;
    currentUser: UserProfile;
    allInspections: AssetInspection[] = [];
    canShowInspectionList: boolean = true;
    assetDetailsPopupVisible: boolean = false;
    private routerSubscription: Subscription;
    currentAssetId: string;
    currentAssetDetails: Asset;
    inspectionSchedule: any[];
    assetComponentMap: AssetComponent[];
    equipmentPopupTitle: string = ' ';
    inspectionPopupTitle: string = ' ';
    saveState = (state) => {
        this._users.currentProfile$.subscribe((user: any) => {
            this.userId = user.id;
        });
        this.addUserPreferences(
            this.userId,
            'inspection',
            JSON.stringify(state)
        );
    };
    loadState = async () => {
        try {
            const response = await this.getUserPreference();
            if (response && response.inspection) {
                const equipmentValue = JSON.parse(response.inspection);
                equipmentValue.filterValue = this.currentFilter;
                return equipmentValue;
            } else {
                console.error('No equipment preference found in response.');
                return null;
            }
        } catch (error) {
            console.error('Error loading preference:', error);
            return null;
        }
    };
    addUserPreferences = (
        useri: string,
        storageKey: string,
        values: string
    ) => {
        if (this.credoService) {
            this.credoService
                .addPreference(useri, storageKey, values)
                .subscribe(
                    (response) => {
                        console.log('Preference added successfully:', response);
                    },
                    (error) => {
                        //    console.error('Error adding preference:', error);
                    }
                );
        } else {
            console.error('CredoService is undefined!');
        }
    };
    @ViewChild(DxDataGridComponent)
    grid: DxDataGridComponent;
    isDataLoaded: boolean = false;
    selectedLocationId: number;
    costCentreOptions: { id: string; name: string }[];
    districtvalues: { id: string; name: string }[];
    locationOptions: { id: string; name: string }[];
    districtToClientMap: Map<string, any[]>;
    clientToDistrictMap: Map<string, any[]>;
    districtToSiteMap: Map<string, any[]>;
    filteredDistrictOptions: { id: string; name: string }[];
    filteredClientOptions: { id: string; name: string }[];
    selectedCostCentres: any;
    selectedCompany: any;
    clientcostcentre: any;
    clientLocationData: any;
    isFromOverview: boolean = false;
    hideBreadcrumbs: boolean = false;

    constructor(
        private readonly credoService: CredoSoftService,
        private readonly _sitePipe: SiteLabelPipe,
        private readonly _users: UsersService,
        private readonly _grid: DataGridService,
        private route: ActivatedRoute,
        private router: Router,
        private location: Location,
        private indexedDbService: IndexedDbService,
        private utilsService: UtilsService
    ) { }

    crumbs: Breadcrumb[];

    ngAfterViewInit(): void {
        this.grid.instance.beginCustomLoading('');
    }
    getUserPreference(): Promise<any> {
        this._users.currentProfile$.subscribe((user: any) => {
            this.userId = user.id;
        });
        return new Promise((resolve, reject) => {
            this.credoService
                .getPreference(this.userId, 'inspection')
                .subscribe(
                    (response) => {
                        if (response && response.inspection) {
                            resolve(response);
                        } else {
                            reject(
                                'No equipment preference found in response.'
                            );
                        }
                    },
                    (error) => {
                        reject('Error fetching preference: ' + error);
                    }
                );
        });
    }
    addUserPreference(useri: string, storageKey: string, values: string): void {
        if (this.credoService) {
            this.credoService
                .addPreference(useri, storageKey, values)
                .subscribe(
                    (response) => {
                        // console.log('Preference added successfully:', response);
                    },
                    (error) => {
                        // console.error('Error adding preference:', error);
                    }
                );
        } else {
            console.error('CredoService is undefined!');
        }
    }
    onCellPrepared(event) {
        if (event.rowType == 'data' || this.isDataLoaded) {
            this.isLoading = false;
        } else {
            this.isLoading = true;
        }
    }
    async ngOnInit(): Promise<void> {
        if (window.location.hash.includes('fromOverview')) {
            const url = window.location.href;
            const siteLocationId = decodeURIComponent(
                url.split('fromOverview=')[1]
            ).split('locationId=')[1];
            try {
                this.breaCrumbLabel = decodeURIComponent(
                    url.split('fromOverview=')[1]
                ).split('?')[0];
                const userProfile$ = this._users.currentProfile$;
                this.hideBreadcrumbs = true;
                userProfile$.subscribe((user) => {
                    this.currentUser = user;
                });
                const userId = JSON.parse(JSON.stringify(this.currentUser?.id));
                const anomalies = await this.indexedDbService.getItem<any[]>(
                    `anomalies_${userId}`
                );
                const assets = await this.indexedDbService.getItem<any[]>(
                    `assets_${userId}`
                );
                const inspections = await this.indexedDbService.getItem<any[]>(
                    `inspections_${userId}`
                );
                const sites = await this.indexedDbService.getItem<any[]>(
                    `assetManagementSites_${userId}`
                );
                const clientLocationData = await this.indexedDbService.getItem<
                    any[]
                >(`clientLocationData_${userId}`);
                this.assets = assets || [];
                this.allInspections = inspections || [];
                this.availableSites = sites || [];
                this.clientLocationData = clientLocationData || [];
                this.inspectionAnamolies = anomalies || [];
                this.clientcostcentre = clientLocationData || [];
                this.clientLocationData = clientLocationData;
                this.processLocations(this.clientcostcentre);
                const dataSource = new DataSource({
                    store: new ArrayStore({
                        data: inspections || []
                    })
                });
                this.inspectionDataSource = dataSource;

                const roles = this.currentUser.roles.map((role) =>
                    role.toLowerCase()
                );

                if (roles) {
                    if (roles.includes('aimaas:demo')) {
                        this.availableSites = this.availableSites.filter(
                            (site) =>
                                site.locationid == Number('635140707384299520')
                        );
                    }
                }

                this.selectedSite = this.availableSites.find(
                    (site) =>
                        site.locationid ==
                        Number(
                            localStorage.getItem(
                                `selectedSite${siteLocationId}`
                            )
                        )
                );
                this.inspectionDataSource.filter(
                    (anomaly) =>
                        anomaly.locationid === this.selectedSite.locationid
                );
                this.inspectionDataSource.load();
                const storedFilter =
                    JSON.parse(sessionStorage.getItem('currentFilter')) || [];
                storedFilter.forEach((item) => {
                    if (
                        Array.isArray(item) &&
                        item.length === 3 &&
                        !isNaN(Date.parse(item[2]))
                    ) {
                        item[2] = new Date(item[2]);
                    }
                });
                this.currentFilter = storedFilter;
                this.isFromOverview = this.checkIfFromOverview();
                this.isHideButton = true;
            } catch (error) {
                console.error('Error loading data source:', error);
            }

            this.isLoading = false;
            this.isDataLoaded = true;
            const storedSiteId = localStorage.getItem(
                `selectedSite${siteLocationId}`
            );

            if (storedSiteId) {
                this.selectedSite = this.availableSites?.find(
                    (site) => site.locationid == Number(storedSiteId)
                );
            }
        } else {
            combineLatest([
                this.credoService.getAllInspectionsAsDataSource(),
                this.credoService.assets$,
                this.credoService.inspections$,
                this.credoService.assetManagementSites$,
                this.credoService.anomalies$,
                this.credoService.clientLocationData$,
                this._users.currentProfile$
            ])
                .pipe(finalize(() => this.grid.instance.endCustomLoading()))
                .subscribe(
                    ([
                        ds,
                        assets,
                        inspections,
                        sites,
                        anomalies,
                        clientLocationData,
                        currentUser
                    ]: [
                            DataSource,
                            Asset[],
                            AssetInspection[],
                            AssetManagementSite[],
                            AnomaliesRecommendations[],
                            any,
                            UserProfile
                        ]) => {
                        this.inspectionDataSource = ds;
                        this.inspectionDataSource.load();
                        this.assets = assets;
                        this.allInspections = inspections;
                        this.currentUser = currentUser;
                        this.clientcostcentre = clientLocationData;
                        this.clientLocationData = clientLocationData;
                        if (history.state?.data?.currentFilter) {
                            this.currentFilter =
                                history.state.data.currentFilter;
                        } else if (
                            window.location.hash.includes('fromOverview')
                        ) {
                            const storedFilter = JSON.parse(
                                sessionStorage.getItem('currentFilter')
                            );
                            // Convert date string back to Date object
                            storedFilter.forEach((item) => {
                                if (
                                    Array.isArray(item) &&
                                    item.length === 3 &&
                                    !isNaN(Date.parse(item[2]))
                                ) {
                                    item[2] = new Date(item[2]);
                                }
                            });
                            this.currentFilter = storedFilter;
                        }

                        this.availableSites = sites;
                        this.userId = currentUser.email;
                        this.inspectionAnamolies = anomalies;
                        const roles = currentUser.roles.map((role) =>
                            role.toLowerCase()
                        );
                        if (roles) {
                            if (roles.includes('aimaas:demo')) {
                                this.availableSites =
                                    this.availableSites.filter(
                                        (site) =>
                                            site.locationid ==
                                            Number('635140707384299520')
                                    );
                                this.clientcostcentre =
                                    clientLocationData?.filter(
                                        (site) =>
                                            site?.locationid ==
                                            Number('635140707384299520')
                                    );
                            } else if (
                                !roles.includes('app:admin') &&
                                !roles.includes('aimaas:admin') &&
                                !roles.includes('aimaas:all') &&
                                !roles.includes('aimaas:district') &&
                                !roles.includes('aimaas:client') &&
                                currentUser.assetManagementSiteIds
                            ) {
                                this.availableSites =
                                    this.availableSites.filter((site) =>
                                        currentUser.assetManagementSiteIds.includes(
                                            site.locationid
                                        )
                                    );
                                this.clientcostcentre =
                                    clientLocationData.filter((site) =>
                                        currentUser.assetManagementSiteIds.includes(
                                            site?.locationid
                                        )
                                    );
                            }
                        }
                        this.processLocations(this.clientcostcentre);
                        if (!(history?.state?.data?.assetObjIds) && !(history?.state?.source === 'overview')) {
                            let savedCostCentre = localStorage.getItem('selectedDistrict');
                            this.selectedCostCentres = savedCostCentre ?? null;
                            // Always get districts for the selected cost centre
                            let clientsForDistrict = this.utilsService.getUniqueBy(this.districtToClientMap.get(savedCostCentre) || [], 'id');
                            this.districtToClientMap.get(savedCostCentre ?? null) || [];
                            if (
                                !clientsForDistrict.length &&
                                this.districtvalues.length
                            ) {
                                // Assign all districts if none for cost centre
                                clientsForDistrict = this.utilsService.getUniqueBy(this.districtvalues, 'id');
                            }
                            this.filteredClientOptions = [{ id: '', name: '' }, ...clientsForDistrict.sort((a, b) => a.name.localeCompare(b.name))];

                            // District
                            let savedClient = localStorage.getItem('selectedClient');
                            this.selectedCompany = savedClient ?? null;

                            // Sites
                            let filteredLocations =
                                this.districtToSiteMap.get(savedClient ?? null) || [];
                            if (filteredLocations.length === 0) {
                                // Assign all sites
                                filteredLocations = Array.from(
                                    this.districtToSiteMap.values()
                                ).flat();
                            }
                            this.availableSites = filteredLocations.map((site) => ({
                                locationid: site.locationid,
                                locationname: site.locationname,
                                clientid: site.clientid ? Number(site.clientid) : 0,
                                clientname: site.clientname || ''
                            }));

                            // Site
                            let savedSite = localStorage.getItem('selectedSite');
                            let selectedSiteObj = this.availableSites.find(
                                (site) => site.locationid == Number(savedSite)
                            );
                            if (!selectedSiteObj && this.availableSites.length > 0) {
                                selectedSiteObj = this.availableSites[0];
                                localStorage.setItem(
                                    'selectedSite',
                                    String(selectedSiteObj.locationid)
                                );
                            }
                            this.selectedSite = selectedSiteObj;
                        } else this.selectedSite = this.availableSites?.find(
                            (site) => site.locationid == Number(localStorage.getItem('selectedSite'))
                        );
                        if (history.state?.data?.currentFilter) {
                            this.currentFilter =
                                history.state.data.currentFilter;
                        } else if (
                            window.location.hash.includes('fromOverview')
                        ) {
                            const storedFilterString =
                                sessionStorage.getItem('currentFilter');
                            if (storedFilterString) {
                                const storedFilter =
                                    JSON.parse(storedFilterString);
                                storedFilter.forEach((item) => {
                                    if (
                                        Array.isArray(item) &&
                                        item.length === 3 &&
                                        !isNaN(Date.parse(item[2]))
                                    ) {
                                        item[2] = new Date(item[2]);
                                    }
                                });
                                this.currentFilter = storedFilter;
                            }
                        }

                        // 8. Set other flags
                        this.isLoading = false;
                        this.isDataLoaded = true;

                        // 9. Setup Router Event Subscriptions
                        this.routerSubscription = this.router.events
                            .pipe(
                                filter(
                                    (event) => event instanceof NavigationEnd
                                ),
                                filter(() =>
                                    this.router.url.includes(
                                        'aimaas/inspection-drilldown'
                                    )
                                )
                            )
                            .subscribe(() => {
                                this.handleQueryParams();
                            });

                        // 10. Special check for Overview Source
                        if (
                            window.location.hash.includes('fromOverview') ||
                            history?.state?.source === 'overview'
                        ) {
                            this.isHideButton = true;
                        }
                        this.isFromOverview = this.checkIfFromOverview();
                        this.updateBreadcrumbs();
                    }
                );
        }

        // Ensure dropdowns are populated from localStorage for edit/view roles
        const roles = this.currentUser?.roles?.map((role) => role.toLowerCase()) || [];
        if (roles.includes('edit') || roles.includes('view')) {
            const storedCostCentre = localStorage.getItem('selectedDistrict');
            const savedClient = localStorage.getItem('selectedClient');
            const storedSiteId = localStorage.getItem('selectedSite');

            if (storedCostCentre) {
                this.selectedCostCentres = storedCostCentre;
            }
            if (savedClient) {
                this.selectedCompany = savedClient;
            }
            if (storedSiteId) {
                this.selectedSite = this.availableSites?.find(
                    (site) => site.locationid == Number(storedSiteId)
                );
            }
        }
    }

    convertHtmlToText(html: string): string {
        if (!html || html === 'null') {
            return '';
        }
        const doc = new DOMParser().parseFromString(html, 'text/html');
        return doc.documentElement.textContent ?? '';
    }

    ngOnDestroy(): void {
        if (this.routerSubscription) {
            this.routerSubscription.unsubscribe();
        }
    }
    checkIfFromOverview(): boolean {
        const hash = window.location.hash;
        const queryString = hash.split('?')[1] || '';
        const urlParams = new URLSearchParams(queryString);
        return (
            urlParams.has('fromOverview') ||
            history.state?.source === 'overview'
        );
    }

    handleQueryParams() {
        this.route.queryParams.subscribe((params) => {
            const operationId = params['op'];
            const scheduleId = params['sched'];
            // Early exit if no operationId or scheduleId
            if (!operationId && !scheduleId) {
                this.canShowInspectionList = true;
                return;
            }
            const storedAssetIds = sessionStorage.getItem('assetObjIds');
            const assetIds = storedAssetIds ? JSON.parse(storedAssetIds) : [];

            // Filter inspections based on the query params
            let inspectionDetails = this.allInspections?.filter(
                (insp) =>
                    (operationId
                        ? insp.planoperationid == operationId
                        : true) &&
                    (scheduleId ? insp.scheduleid == scheduleId : true) &&
                    (assetIds.length > 0
                        ? assetIds.includes(String(insp.assetid))
                        : true) // ✅ Fix asset ID filtering
            );
            // If no inspections found, do not show the list
            if (!inspectionDetails?.length) {
                this.canShowInspectionList = false;
                return;
            }

            // Check if the inspection's location matches the selected site's location
            const inspectionLocationId = inspectionDetails[0].locationid;
            if (String(this.selectedSite.locationid) !== inspectionLocationId) {
                const filteredAssetSite = this.availableSites.find(
                    (site) => site.locationid == Number(inspectionLocationId)
                );

                // If a matching site is found, change the site and update the inspection
                if (filteredAssetSite) {
                    this.changeSite({ selectedItem: filteredAssetSite });
                    this.inspectionSelectionChanged({
                        data: inspectionDetails[0]
                    });
                    this.canShowInspectionList = true;
                } else {
                    this.canShowInspectionList = false;
                }
            } else {
                // If the inspection's location matches the selected site, update the inspection directly
                this.inspectionSelectionChanged({ data: inspectionDetails[0] });
                this.canShowInspectionList = true;
            }
        });
    }

    processLocations(data: any[]) {
        if (!data || !Array.isArray(data) || data.length === 0) {
            return;
        }

        const costCentreMap = new Map<string, string>();
        const clientMap = new Map<string, string>();
        const locationMap = new Map<string, string>(); // Add this for locations
        const districtToSiteMap = new Map<string, Set<any>>(); // New map for district -> locations
        const filteredClientMap = new Map<string, string>();
        const CostCentresToDistrictMap = new Map<string, Set<any>>();
        const DistrictsToCostCentreMap = new Map<string, Set<any>>();

        // Process the input data
        data.forEach((item) => {
            // Process cost centre information
            if (item?.costcenterid && item?.costcentername) {
                costCentreMap.set(
                    item.costcenterid,
                    item.costcentername.trim()
                );
            }

            // Process client information
            if (item?.clientid && item?.clientname) {
                clientMap.set(item.clientid, item.clientname.trim());
            }

            // Process location information
            if (item?.locationid && item?.locationname) {
                locationMap.set(item.locationid, item.locationname.trim());
            }

            // Cost Centre → Districts (Ensure uniqueness)
            if (item?.costcenterid && item?.clientid && item?.clientname) {
                if (!CostCentresToDistrictMap.has(item.costcenterid)) {
                    CostCentresToDistrictMap.set(item.costcenterid, new Set());
                }

                // Use a Map for uniqueness based on ID
                let uniqueDistricts = CostCentresToDistrictMap.get(
                    item.costcenterid
                );
                if (
                    !Array.from(uniqueDistricts).some(
                        (d) => d.id === item.clientid
                    )
                ) {
                    uniqueDistricts.add({
                        id: item.clientid,
                        name: item.clientname.trim()
                    });
                }
            }

            // District → Cost Centres
            if (item?.clientid && item?.costcenterid && item?.costcentername) {
                if (!DistrictsToCostCentreMap.has(item.clientid)) {
                    DistrictsToCostCentreMap.set(item.clientid, new Set());
                }
                DistrictsToCostCentreMap.get(item.clientid)?.add({
                    id: item.costcenterid,
                    name: item.costcentername.trim()
                });
            }
            if (item?.clientid && item?.locationid && item?.locationname) {
                if (!districtToSiteMap.has(item.clientid)) {
                    districtToSiteMap.set(item.clientid, new Set());
                }

                let uniqueLocations = districtToSiteMap.get(item.clientid);
                uniqueLocations.add({
                    locationid: item.locationid, // ✅ Ensure 'locationid' is used
                    locationname: item.locationname.trim()
                });
            }
        });

        // Transform Map data into sorted arrays for the dropdown options
        this.costCentreOptions = Array.from(costCentreMap.entries())
            .map(([id, name]) => ({ id, name }))
            .sort((a, b) => a.name.localeCompare(b.name));

        this.districtvalues = Array.from(clientMap.entries())
            .map(([id, name]) => ({ id, name }))
            .sort((a, b) => a.name.localeCompare(b.name));

        this.locationOptions = Array.from(locationMap.entries())
            .map(([id, name]) => ({ id, name }))
            .sort((a, b) => a.name.localeCompare(b.name));

        if (!this.districtToClientMap) {
            this.districtToClientMap = new Map<string, any[]>();
        }

        CostCentresToDistrictMap.forEach((districtSet, costCentreId) => {
            this.districtToClientMap.set(
                costCentreId,
                Array.from(districtSet)
            );
        });

        this.districtToSiteMap = new Map<string, any[]>();
        districtToSiteMap.forEach((locationSet, districtId) => {
            this.districtToSiteMap.set(districtId, Array.from(locationSet));
        });

        if (!this.clientToDistrictMap) {
            this.clientToDistrictMap = new Map<string, any[]>();
        }

        DistrictsToCostCentreMap.forEach((costCentreId, districtSet) => {
            this.clientToDistrictMap.set(
                districtSet,
                Array.from(costCentreId)
            );
        });

        // Convert Set to Array for mappings

        this.filteredDistrictOptions = [{ id: '', name: '' }, ...this.utilsService.getUniqueBy(this.costCentreOptions.sort((a, b) => a.name.localeCompare(b.name)), 'id')];
        this.filteredClientOptions = [{ id: '', name: '' }, ...this.utilsService.getUniqueBy(this.districtvalues.sort((a, b) => a.name.localeCompare(b.name)), 'id')];
        const savedCostCentreId = localStorage.getItem('selectedDistrict') || '';
        const savedClientId = localStorage.getItem('selectedClient') || '';

        // Filter clientOptions based on savedCostCentreId (District)
        let clientOptions: any[] = [];
        if (savedCostCentreId && this.districtToClientMap.has(savedCostCentreId)) {
            clientOptions = this.utilsService.getUniqueBy(this.districtToClientMap.get(savedCostCentreId).sort((a, b) => a.name.localeCompare(b.name)) || [], 'id');
        } else {
            clientOptions = this.utilsService.getUniqueBy(this.districtvalues.sort((a, b) => a.name.localeCompare(b.name)), 'id');
        }
        this.filteredClientOptions = [
            { id: '', name: '' },
            ...clientOptions.sort((a, b) => a.name.localeCompare(b.name))
        ];

        // Filter districtOptions based on savedClientId (Client)
        let districtOptions: any[] = [];
        if (savedClientId && this.clientToDistrictMap.has(savedClientId)) {
            districtOptions = this.utilsService.getUniqueBy(this.clientToDistrictMap.get(savedClientId) || [], 'id');
        } else {
            districtOptions = this.utilsService.getUniqueBy(this.filteredDistrictOptions.filter(opt => opt.id !== '') // remove the empty option
                .sort((a, b) => a.name.localeCompare(b.name)),
                'id'
            );
        }
        this.filteredDistrictOptions = [
            { id: '', name: '' },
            ...this.utilsService.getUniqueBy(districtOptions.sort((a, b) => a.name.localeCompare(b.name)), 'id')
        ];
    }

    onDistrictChange(event: any) {
        const selectedDistrict = event.value;
        this.selectedCostCentres = selectedDistrict;
        localStorage.setItem('selectedDistrict', selectedDistrict);

        if (selectedDistrict) {
            const uniqueClients = this.utilsService.getUniqueBy(
                Array.from(new Set(this.districtToClientMap.get(selectedDistrict) || [])),
                'id'
            );
            this.filteredClientOptions = [{ id: '', name: '' }, ...uniqueClients.sort((a, b) => a.name.localeCompare(b.name))];

            setTimeout(() => {
                if (!this.selectedCompany) {
                    this.selectedCompany = this.filteredClientOptions[0]?.id || null;
                }
            });
        } else {
            const allClients = this.utilsService.getUniqueBy(
                Array.from(this.districtToClientMap.values()).flat(),
                'id'
            );
            this.filteredClientOptions = [{ id: '', name: '' }, ...allClients.sort((a, b) => a.name.localeCompare(b.name))];
        }

        localStorage.setItem('selectedClient', this.selectedCompany);

        this.filterSites();
    }

    onClientChange(event: any) {
        const selectedClient = event.value;
        this.selectedCompany = selectedClient;
        localStorage.setItem('selectedClient', selectedClient);

        if (selectedClient) {
            const districts = this.utilsService.getUniqueBy(
                Array.from(new Set(this.clientToDistrictMap.get(selectedClient) || [])),
                'id'
            );
            this.filteredDistrictOptions = [{ id: '', name: '' }, ...districts.sort((a, b) => a.name.localeCompare(b.name))];
        } else {
            const allDistricts = this.utilsService.getUniqueBy(
                Array.from(this.clientToDistrictMap.values()).flat(),
                'id'
            );
            this.filteredDistrictOptions = [{ id: '', name: '' }, ...allDistricts.sort((a, b) => a.name.localeCompare(b.name))];
        }

        this.filterSites();
    }

    filterSites() {
        const isValid = (val: any) =>
            val !== null && val !== undefined && val !== '' && val !== 'null' && val !== 'undefined';

        const district = isValid(this.selectedCostCentres);
        const client = isValid(this.selectedCompany);

        if (district && client) {
            this.updateAvailableSites(this.getFilteredSitesByBoth(this.selectedCostCentres, this.selectedCompany));
        } else if (district) {
            this.updateAvailableSites(this.getFilteredSitesByDistrict(this.selectedCostCentres));
        } else if (client) {
            this.updateAvailableSites(this.getFilteredSitesByClient(this.selectedCompany));
        } else {
            this.updateAvailableSites(Array.from(this.districtToSiteMap.values()).flat());
        }
    }

    updateAvailableSites(filteredLocations: any[]) {
        this.availableSites = filteredLocations;
        this.selectedSite = this.availableSites[0] || null;
        if (this.selectedSite) {
            localStorage.setItem('selectedSite', String(this.selectedSite.locationid));
        }
    }

    getFilteredSitesByDistrict(districtId: string): any[] {
        const clientIds = (this.districtToClientMap.get(districtId) || []).map(client => client.id);
        return clientIds.flatMap(id => this.districtToSiteMap.get(id) || []);
    }

    getFilteredSitesByClient(clientId: string): any[] {
        return this.districtToSiteMap.get(clientId) || [];
    }

    getFilteredSitesByBoth(districtId: string, clientId: string): any[] {
        const allSites = this.districtToSiteMap.get(clientId) || [];
        const validLocationIds = this.clientLocationData
            .filter(item => item.costcenterid === districtId && item.clientid == clientId)
            .map(item => item.locationid);
        return allSites.filter(loc => validLocationIds.includes(loc.locationid));
    }

    assetSelectionChanged(data) {
        const inspection = data?.row?.data;
        if (!window.location.hash.includes('fromOverview')) {
            this.location.replaceState(
                `/aimaas/drilldown?assetid=${inspection?.assetid}`
            );
        }
        const asset = this.assets?.find(
            (asset) => asset.id == inspection?.assetid
        );
        this.assetComponentMap = [];
        var comp = this.assetComponentMap;
        this.currentAssetId = String(asset?.id);
        this.credoService.getAllComponents(asset.id).subscribe((data) => {
            this.assetComponentMap = data;
            comp = this.assetComponentMap.sort((a, b) => {
                const clientCompare = a.componentname.localeCompare(
                    b.componentname
                );
                if (clientCompare !== 0) {
                    return clientCompare;
                }
            });
        });
        this.currentAssetDetails = asset;
        this.inspectionSchedule = this.getInspectionsForAsset(String(asset.id));
        this.equipmentPopupTitle = `EQUIPMENT - ${asset.assetid}`;
        this.assetDetailsPopupVisible = true;
    }
    getInspectionsForAsset(id: string): AssetInspection[] {
        if (!this.allInspections) {
            return [];
        }

        return this.allInspections?.filter((item) => item.assetid === id);
    }
    restoreAssetsDefaultsClicked = async (e) => {
        const result = await this._grid.resetGridState(this.grid);
        if (result) {
            this.addUserPreferences(
                this.userId,
                'inspection',
                JSON.stringify('')
            );
        }
    };
    CompleteHistoryButtonClicked(event) {
        if (event) {
            this.currentFilter = [['assetidname', '=', event]];
            this.assetDetailsPopupVisible = false;
        }
    }
    clientSubmissionTitleValueChange(e: any) {
        this.submissionPopupTitle = e;
    }
    clientDataFormSubmitted(e: any) {
        this.submissionPopupVisible = false;
    }
    clientSubmitDataOnclick(e: string) {
        if (e === 'frombuttonclick') {
            this.initialAnomaly = null;
        }
        this.submissionPopupVisible = !this.submissionPopupVisible;
    }
    updateBreadcrumbs() {
        let sourceLabel = 'KPI Dashboards'; // Default label
        let sourceRoute = '/aimaas/dashboards';

        // ✅ Read source from history.state first
        let source = history.state?.source;

        // ✅ If history.state is empty, fallback to URL query parameters
        if (!source) {
            const hash = window.location.hash;
            const queryString = hash.split('?')[1] || '';
            const urlParams = new URLSearchParams(queryString);
            if (urlParams.has('fromOverview')) {
                sourceLabel = 'Overview Dashboard';
                sourceRoute = '/aimaas/overview-dashboard'; // ✅ Set Overview Dashboard when navigating from overview
            }
        }
        // ✅ Set Overview Dashboard when navigating from overview
        if (source === 'overview') {
            sourceLabel = 'Overview Dashboard';
            sourceRoute = '/aimaas/overview-dashboard';
        }

        // ✅ Ensure breadcrumb label is set correctly
        this.breaCrumbLabel =
            history.state?.breadCrumbLabel ?? 'Inspections List';

        // ✅ Handle breadcrumb for Inspection (when navigating to Inspection)
        if (window.location.hash.includes('fromOverview')) {
            const breadcrumbLabel = sessionStorage.getItem('breadcrumbLabel');
            if (breadcrumbLabel) {
                this.breaCrumbLabel = breadcrumbLabel;
            }
        }

        // ✅ Prevent duplicate breadcrumbs
        this.crumbs = [
            { label: sourceLabel, route: sourceRoute },
            {
                label: this.breaCrumbLabel,
                route: '/aimaas/inspection-drilldown'
            }
        ];
    }

    onContentReady(event) {
        this.grid?.instance?.endCustomLoading();
    }

    getComponentsForInspection(key: any): any {
        return (this.inspectionComponentMap[key.objid] = this
            .inspectionComponentMap[key.objid] || {
            store: new ArrayStore({
                data: this.components.filter(
                    (component) => component.objparent === key.objid
                )
            })
        });
    }

    inspectionSelectionChanged(e) {
        this.popupVisible = true;
        const inspection = e.data;
        if (
            inspection?.scheduleid !== null &&
            inspection?.planoperationid !== null &&
            !window.location.hash.includes('fromOverview')
        ) {
            this.location.replaceState(
                `/aimaas/inspection-drilldown?op=${inspection.planoperationid}&sched=${inspection.scheduleid}`
            );
        } else if (
            inspection?.planoperationid !== null &&
            !window.location.hash.includes('fromOverview')
        ) {
            this.location.replaceState(
                `/aimaas/inspection-drilldown?op=${inspection.planoperationid}`
            );
        } else if (
            inspection?.operationid !== null &&
            !window.location.hash.includes('fromOverview')
        ) {
            this.location.replaceState(
                `/aimaas/inspection-drilldown?sched=${inspection.scheduleid}`
            );
        }
        this.inspectionPopupTitle =
            inspection.operationtype !== null
                ? `INSPECTION - ${inspection?.assetidname} - ${inspection?.operationtype
                } - ${inspection?.date.replace(/-/g, '/')} `
                : `SCHEDULE - ${inspection?.assetidname} - ${inspection?.scheduletype
                } - ${inspection?.nextinspectiondue
                    ? inspection.nextinspectiondue.replace(/-/g, '/')
                    : ''
                }`;
        this.generalInspectionDetails = inspection;
        this.selectedAssetID = inspection.assetid;
        this.lastDateOfSelectedAsset = inspection.lastdate;
        this.selectedOperationId = inspection.planoperationid;
        this.visibleInspectionAttachments = this.inspectionAttachments.filter(
            (attachment) =>
                attachment.inspectionid === inspection.planoperationid
        );
        this.visibleAssetAttachments = this.assetAttachments.filter(
            (attachment) => attachment.CLIENTID === inspection.objid
        );

        this.visibleInspectionAnamolies = this.inspectionAnamolies.filter(
            (anomaly) => anomaly.operationid == inspection.planoperationid
        );
        this.selectedAsset = this.assets.find(
            (asset) => asset.id === inspection.assetid
        );

        this.tabs = [
            {
                title: 'Asset Details',
                template: 'asset-details'
            },
            {
                title: 'Inspection Attachments',
                template: 'inspection-attachments'
            },
            {
                title: 'Asset Attachments',
                template: 'asset-attachments'
            }
        ];
    }
    closePopup() {
        this.popupVisible = false;
        this.equipmentPopupTitle = ' ';
        this.inspectionPopupTitle = ' ';
        if (!window.location.hash.includes('fromOverview')) {
            this.location.replaceState('/aimaas/inspection-drilldown');
        }
    }
    componentSelectionChanged(e) {
        const component = e.selectedRowsData[0];
        this.currentComponentCalcs = this.alarmCalcs.find(
            (calc) => calc.objid === component.objid
        );
        this.tabs = [
            {
                title: 'Asset Details',
                template: 'asset-details'
            },
            {
                title: 'Calculations',
                template: 'calculations'
            }
        ];
    }

    getCurrentCompnentCalcs() { }

    onRowExpanding(e: {
        cancel: boolean;
        component: dxDataGrid;
        element: HTMLElement;
        key: any;
        model: any;
    }) {
        RowExpandingHandler.handle(e);
    }

    customDisplayExpr = (site: any): string => {
        if (site) return this._sitePipe.transform(site);
    };
    changeSite(selectedSite) {
        let _selectedSite = selectedSite.selectedItem;
        if (_selectedSite == null) {
            setTimeout(() => {
                this.selectedSite = this.availableSites[0];
                localStorage.setItem(
                    'selectedSite',
                    String(this.selectedSite.locationid)
                );
                localStorage.setItem(
                    'selectedClient',
                    String(this.selectedCompany)
                );
                localStorage.setItem(
                    'selectedDistrict',
                    String(this.selectedCostCentres)
                );
            }, 1);
        } else {
            this.selectedSite = selectedSite.selectedItem;
            localStorage.setItem(
                'selectedSite',
                String(this.selectedSite.locationid)
            );
            localStorage.setItem(
                'selectedClient',
                String(this.selectedCompany)
            );
            localStorage.setItem(
                'selectedDistrict',
                String(this.selectedCostCentres)
            );
        }
        this.inspectionDataSource.filter(
            (inspection) =>
                inspection.locationid === this.selectedSite.locationid
        );
        this.inspectionDataSource.load();
    }

    async onExporting(event) {
        const workbook = new Workbook();
        const worksheet = workbook.addWorksheet('Inspections');
        await exportDataGrid({
            component: event.component,
            worksheet
        });
        const buffer: BlobPart = await workbook.xlsx.writeBuffer();
        saveAs(
            new Blob([buffer], { type: 'application/octet-stream' }),
            'Inspections.xlsx'
        );
    }
    onCloseTab(): void {
        window.close();
    }
}
