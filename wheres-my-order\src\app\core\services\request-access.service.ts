import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../../../environments/environment';
import { AccessRequest } from '../../models';

const modules = [
    "Where's My Order",
    'Equipment Demand Request',
    'Asset Integrity Management as a Service (AIMaaS)',
    'Work Management - Pipeline',
    'Bolting Calculator',
    'Remote Asset Monitoring',
    'Asset Performance Management (APM)'
] as const;

export type RequestAccessModules = typeof modules;
export type RequestAccessModule = RequestAccessModules[number];

const wmoSpecialAccesses = [
    'None',
    'Manufacturing User',
    'Engineering User',
    'District Manager'
] as const;

export type WMOSpecialAccesses = typeof wmoSpecialAccesses;
export type WMOSpecialAccess = WMOSpecialAccesses[number];

@Injectable({
    providedIn: 'root'
})
export class RequestAccessService {
    constructor(private readonly _http: HttpClient) {}

    request(accessRequest: AccessRequest) {
        return this._http.post(
            `${environment.api.url}/RequestAccess`,
            accessRequest
        );
    }
}
