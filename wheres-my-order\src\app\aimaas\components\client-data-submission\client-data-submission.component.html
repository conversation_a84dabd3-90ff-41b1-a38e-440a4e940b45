<dx-scroll-view [height]=600
                id="top-container">

    <div class="container"
         id="main-container">
        <div class="client-submission-selectionbox">
            <dx-select-box #selectionBoxClientSubmission
                           style="width: 150px;"
                           [items]="clientSubmissionDataOptions"
                           [value]="clientSubmissionDataOptions[0]"
                           [stylingMode]="'filled'"
                           [readOnly]="initialAnomaly"
                           (onValueChanged)="selectionBoxClientSubmissionValueChange($event)">
            </dx-select-box>
        </div>

        <div class="content-block">


            <div class="anomaly-container"
                 *ngIf="selectionBoxClientSubmission.value === clientSubmissionDataOptions[0]">
                <div class="left-anomaly">
                    <div class="field-group">
                        <label for="anomaly-priority">Priority</label>
                        <dx-text-box id="anomaly-priority"
                                     [(value)]="selectedAnomaly.anomalypriority"
                                     [readOnly]="true">
                        </dx-text-box>
                    </div>
                    <div class="field-group">
                        <label for="anomaly-type">Anomaly type</label>
                        <dx-text-box [(value)]="selectedAnomaly.anomalytype"
                                     id="anomaly-type"
                                     [readOnly]="true">
                        </dx-text-box>
                    </div>
                    <div class="field-group">
                        <label for="anomaly-anomalydescription">Anomaly
                            Description</label>
                        <dx-text-area (height)="100"
                                      [readOnly]="true"
                                      id="anomaly-anomalydescription"
                                      [value]="convertHtmlToText(selectedAnomaly.anomalydescription)">
                        </dx-text-area>
                    </div>
                    <div class="field-group"
                         *ngIf="!initialAnomaly">
                        <label for="anomaly-selectedAsset">Asset Id<span
                                  class="required">*</span></label>
                        <dx-select-box *ngIf="availableAssets?.length > 0"
                                       #assetSelectionBox
                                       [(value)]="selectedAsset"
                                       id="anomaly-assetname"
                                       style="width:400px; margin:2px"
                                       [items]="availableAnomalyAssets"
                                       [showClearButton]="false"
                                       [searchEnabled]="true"
                                       displayExpr="assetid"
                                       stylingMode="filled"
                                       (onValueChanged)="onAssetChanged1($event)">
                        </dx-select-box>
                    </div>
                    <div class="field-group"
                         *ngIf="initialAnomaly">
                        <label for="anomaly-selectedAsset">Asset Id</label>
                        <dx-text-box [(value)]="selectedAsset.assetid"
                                     id="anomaly-selectedAsset"
                                     *ngIf="selectedAnomaly"
                                     [readOnly]="true">
                        </dx-text-box>
                    </div>
                </div>

                <div class="right-anomaly">
                    <div class="field-group">
                        <label for="anomaly-detectiondate">Detection
                            Date</label>
                        <dx-text-box [value]="selectedAnomaly.detectiondate"
                                     [readOnly]="true"
                                     id="anomaly-detectiondate"
                                     *ngIf="selectedAnomaly">
                        </dx-text-box>
                    </div>
                    <div class="field-group">
                        <label for="anomaly-resolutionstate">Approval
                            State</label>
                        <dx-text-box [(value)]="selectedAnomaly.resolutionstate"
                                     [readOnly]="true"
                                     id="anomaly-resolutionstate"
                                     *ngIf="selectedAnomaly">
                        </dx-text-box>
                    </div>
                    <div class="field-group">
                        <label for="anomaly-proposedrecommendation">Proposed
                            Recommendation</label>
                        <dx-text-area (height)="100"
                                      [readOnly]="true"
                                      id="anomaly-proposedrecommendation"
                                      [value]="convertHtmlToText(selectedAnomaly.proposedrecommemendation)">
                        </dx-text-area>
                    </div>
                    <div class="field-group"
                         *ngIf="!initialAnomaly">
                        <label for="anomaly-number">Anomaly Number <span
                                  class="required">*</span></label>
                        <dx-select-box #selectBox
                                       id="anomaly-number"
                                       [dataSource]="checkAsset"
                                       displayExpr="anomaly"
                                       [(value)]="selectedAnomaly"
                                       [searchEnabled]="true"
                                       (onOpened)="onItemClick($event)"
                                       (onValueChanged)="onAnomalyChanged($event)">
                        </dx-select-box>
                    </div>
                    <div class="field-group"
                         *ngIf="initialAnomaly">
                        <label for="anomaly-number-field">Anomaly Number</label>
                        <dx-text-box [(value)]="selectedAnomaly.anomaly"
                                     id="anomaly-number-field"
                                     [readOnly]="true"
                                     *ngIf="selectedAnomaly">
                        </dx-text-box>
                    </div>
                </div>
            </div>
            <div class="inner-content">

                <div class="comment-maincontainer">

                    <div class="field-group"
                         *ngIf="selectionBoxClientSubmission.value === clientSubmissionDataOptions[0]">
                        <label for="client-closeddate">Date Closed Per
                            Client <span class="required">*</span></label>

                        <dx-date-box type="date"
                                     id="client-closeddate"
                                     [(value)]="clientcloseddate">
                        </dx-date-box>
                    </div>


                    <div class="field-group"
                         *ngIf="selectionBoxClientSubmission.value === clientSubmissionDataOptions[2]">
                        <label for="service-type">Type of Service
                            Requested <span class="required">
                                *</span></label>
                        <dx-select-box #selectionServiceTypeBox
                                       id="service-type"
                                       style="width: 200px; margin: 2px;"
                                       [items]="serviceTypeDataOptions"
                                       [value]="serviceTypeDataOptions[0]"
                                       [stylingMode]="'filled'"
                                       [showClearButton]="false">
                        </dx-select-box>
                    </div>
                    <div class="field-group"
                         *ngIf="selectionBoxClientSubmission.value !== clientSubmissionDataOptions[0]">
                        <label for="client-submission-select">For Asset ( Select
                            ID
                            ) <span class="required"
                                  *ngIf="selectionBoxClientSubmission.value == clientSubmissionDataOptions[1]">*</span></label>


                        <dx-tag-box *ngIf="availableAssets?.length > 0"
                                    #assetSelectionBox
                                    id="client-submission-select"
                                    [dataSource]="availableAssets"
                                    stylingMode="filled"
                                    [searchEnabled]="true"
                                    displayExpr="assetid">
                        </dx-tag-box>
                    </div>

                    <div class="field-group">

                        <label style="padding-bottom: 140px;"
                               for="client-comment">{{commentBoxLabel}} <span
                                  class="required"
                                  *ngIf="isCommentsMandatory">*</span>
                            <span class="required"
                                  *ngIf="selectedDocuments.length>0 && selectionBoxClientSubmission.value == clientSubmissionDataOptions[0]">*</span></label>
                        <dx-html-editor id="client-comment"
                                        [(value)]="comment"
                                        height="200px"
                                        width="400px">
                            <dxo-toolbar>
                                <dxi-item name="bold"></dxi-item>
                                <dxi-item name="italic"></dxi-item>
                                <dxi-item name="underline"></dxi-item>
                                <dxi-item name="orderedList"></dxi-item>
                                <dxi-item name="bulletList"></dxi-item>
                                <dxi-item name="blockquote"></dxi-item>
                                <dxi-item name="alignLeft"></dxi-item>
                                <dxi-item name="alignCenter"></dxi-item>
                                <dxi-item name="alignRight"></dxi-item>
                                <dxi-item name="link"></dxi-item>
                            </dxo-toolbar>
                        </dx-html-editor>
                    </div>

                </div>
                <div class="documents-container">
                    <div class="file-uploader">

                        <dx-file-uploader #fileUploader
                                          [multiple]="true"
                                          accept=""
                                          [showFileList]="true"
                                          uploadMode="useForm"
                                          selectButtonText="Select Document(s)"
                                          (onValueChanged)="onFileSelect($event)">

                        </dx-file-uploader>
                    </div>

                    <dx-scroll-view [height]=230
                                    style="margin-top: 4px;">
                        <div>
                            <p
                               style="text-align: center; border: 1px solid #9E9E9E; border-bottom: none;margin-bottom: 0;">
                                <b> Documents to submit </b><span
                                      *ngIf="selectionBoxClientSubmission.value == clientSubmissionDataOptions[1]"
                                      class="required">*</span>
                            </p>
                        </div>
                        <dx-data-grid [dataSource]="selectedDocuments"
                                      keyExpr="filename"
                                      style="height: 180px; border: 1px solid #9E9E9E; "
                                      [showColumnLines]="true"
                                      [showRowLines]="true"
                                      [showBorders]="true"
                                      [wordWrapEnabled]="true">
                            <dxo-editing mode="row"
                                         [allowUpdating]="false"
                                         [allowDeleting]="true"></dxo-editing>
                            <dxi-column dataField="filename"
                                        caption="File Name"></dxi-column>
                            <dxi-column dataField="filetype"
                                        caption="Type"
                                        [calculateCellValue]="formatFileType"></dxi-column>
                            <dxi-column caption="Remove"
                                        type="buttons">
                                <dxi-button name="delete"
                                            type="danger"
                                            (onClick)="removeFile($event)"></dxi-button>
                            </dxi-column>
                        </dx-data-grid>

                    </dx-scroll-view>
                </div>
            </div>
        </div>
    </div>
    <div class="submit-button"
         style="text-align: right;">

        <dx-button text="SUBMIT"
           type="success"
           style="float: right;margin: 13px;"
           (onClick)="submitForm()"
           [disabled]="loadingVisible || !allowedToSubmit"></dx-button>
    </div>
    <dx-load-panel #loadPanel
                   shadingColor="rgba(0,0,0,0.4)"
                   [position]="{ of: '#top-container' }"
                   [(visible)]="loadingVisible"
                   [showIndicator]="true"
                   [showPane]="true"
                   [shading]="true"
                   [hideOnOutsideClick]="false">
    </dx-load-panel>
</dx-scroll-view>