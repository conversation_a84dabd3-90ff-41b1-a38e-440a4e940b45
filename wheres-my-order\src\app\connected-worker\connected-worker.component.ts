import { HttpClient } from '@angular/common/http';
import { Component, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { DxSelectBoxComponent } from 'devextreme-angular';
import { models } from 'powerbi-client';
import { BehaviorSubject } from 'rxjs';
import { environment } from '../../environments/environment';
import { userHasSomesRoles } from '../core/operators';
import { ConfigResponse } from '../fsm/models';
import { UsersService } from '../shared/services';

@Component({
  selector: 'app-connected-worker',
  templateUrl: './connected-worker.component.html',
  styleUrls: ['./connected-worker.component.scss']
})
export class ConnectedWorkerComponent implements OnInit {

  @ViewChild(DxSelectBoxComponent) dashboardSelectBox: DxSelectBoxComponent;

  // Util variables
  // Test comment

  isLoading: boolean = false
  pageTitle: string = ""
  invalidRoute: boolean = false
  $selectedDashboard: string = "" 
  $client: string = ""
  validClients = ["chevron", "gpc"]
  dashboardDataSource = []

  // Setup initial config and observer

  private _initialConfig = {
    type: 'report',
    id: undefined,
    embedUrl: undefined,
    accessToken: undefined,
    tokenType: models.TokenType.Embed
  };

  private _config = new BehaviorSubject<models.IReportEmbedConfiguration>(
    this._initialConfig
  );

  readonly config$ = this._config.asObservable();

  // Construct and initialize

  constructor(private readonly _http: HttpClient, 
    private readonly _activatedRoute: ActivatedRoute,
    private readonly _users: UsersService) { 
    this._activatedRoute.paramMap.subscribe((params)=>{
      this.invalidRoute = false
      this.$client = params.get("client")
      if(!this.validClients.includes(this.$client)) {
        this.invalidRoute = true
        return
      }
      this.pageTitle = "Connected Worker | " + this.$client.toUpperCase()
      this.$selectedDashboard = "" 
      this.setupDashboards(this.$client);
     });
  }

  ngOnInit(): void {
    
   }

  // Events and triggers

  onDashboardChanged($event) {
    this.$selectedDashboard = $event.value
    this.isLoading = true
    this.updateDashboard(this.$client, this.$selectedDashboard)
  }

  updateDashboard(client, dashboardId) {

    if (client === "" || dashboardId === "") { return }

    this._http
    .get(`${environment.api.url}/PowerBI/GetConnectedWorker/${client}/${dashboardId}`)
    .subscribe((response: ConfigResponse) => {
        const newConfig = {
            ...this._config.value,
            id: response.EmbedReport[0].ReportId,
            embedUrl: response.EmbedReport[0].EmbedUrl,
            accessToken: response.EmbedToken.token
        };
        this._config.next(newConfig)
        this.isLoading = false
    },
    error => {
      console.log(error)
      this.isLoading = false
    });
  }

  // Setup datasources based on permissions for dashboards

  setupDashboards(clientId) {
    switch(clientId) {
      case "chevron":
        this.setupChevronDashboards();
        break;
      case "gpc":
        this.setupGPCDashboards();
        break;
      default:
        break;
    }
  }

  setupChevronDashboards() {

    this.dashboardDataSource = []

    this._users.currentProfile$.pipe(userHasSomesRoles([
      'app:admin',
      'connectedworker:admin',
      'connectedworker:chevronall',
      'connectedworker:chevronactivitytracker'
    ])).subscribe(hasPermission => {
      if (hasPermission) {
        this.dashboardDataSource.push (
          {
            Id: "activitytracker",
            Name: "Activity Tracker"
          }
        )
        this.dashboardSelectBox.value = "activitytracker"
        
      }
    })

    this._users.currentProfile$.pipe(userHasSomesRoles([
      'app:admin',
      'connectedworker:admin',
      'connectedworker:chevronall',
      'connectedworker:chevroncesosi'
    ])).subscribe(hasPermission => {
      if (hasPermission) {
        this.dashboardDataSource.push (
          {
            Id: "cesosi",
            Name: "CES OSI Metrics"
          }
        )
        this.dashboardSelectBox.value = "cesosi"
      }
    })
  }

  setupGPCDashboards(){
    this.dashboardDataSource = []

    this._users.currentProfile$.pipe(userHasSomesRoles([
      'app:admin',
      'connectedworker:admin',
      'connectedworker:gpcall',
      'connectedworker:gpcsupplementsections'
    ])).subscribe(hasPermission => {
      if (hasPermission) {
        this.dashboardDataSource.push (
          {
            Id: "supplementsections",
            Name: "Supplement Sections"
          }
        )
        this.dashboardSelectBox.value = "supplementsections"
      }
    })
  }
}
