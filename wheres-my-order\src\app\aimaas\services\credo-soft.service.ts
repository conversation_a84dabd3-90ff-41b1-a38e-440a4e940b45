import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import ArrayStore from 'devextreme/data/array_store';
import DataSource from 'devextreme/data/data_source';
import { Observable, ReplaySubject } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { debug } from '../../core/operators';
import { UserProfile } from '../../profile/models';
import { UsersService } from '../../shared/services';
import {
    AlarmCalc,
    AnomaliesRecommendations,
    Asset,
    AssetAttachment,
    AssetComponent,
    AssetInspection,
    AssetManagementSite,
    AssetSubmissions,
    ChamberData,
    CorrosionAnalysis,
    InspectionAttachment
} from '../models';
import { ClientCostCenter } from '../models/client-costcenter';
import { GeneralAnalysis } from '../models/general-analysis';
import { SystemManagementCategories } from '../models/system-management-categories';
import { IndexedDbService } from './indexed-db.service';

@Injectable({
    providedIn: 'root'
})
export class CredoSoftService {
    private _assets = new ReplaySubject<Asset[]>();
    private _corrosionAnalysis = new ReplaySubject<CorrosionAnalysis[]>();
    private _inspections = new ReplaySubject<AssetInspection[]>();
    private _components = new ReplaySubject<AssetComponent[]>();
    private _generalAnalysis = new ReplaySubject<GeneralAnalysis[]>();
    private _assetManagementSites = new ReplaySubject<AssetManagementSite[]>();
    private _assetAttachments = new ReplaySubject<AssetAttachment[]>();
    private _inspectionAttachments = new ReplaySubject<
        InspectionAttachment[]
    >();
    private _anomalies = new ReplaySubject<AnomaliesRecommendations[]>();
    private _submissions = new ReplaySubject<AssetSubmissions[]>();
    private _clientLocationData = new ReplaySubject<ClientCostCenter[]>();
    private indexedDbService = new IndexedDbService();
    currentUser: UserProfile;
    get anomalies$(): Observable<AnomaliesRecommendations[]> {
        return this._anomalies.asObservable();
    }
    get assets$(): Observable<Asset[]> {
        return this._assets.asObservable();
    }

    get inspections$(): Observable<AssetInspection[]> {
        return this._inspections.asObservable();
    }

    get components$(): Observable<AssetComponent[]> {
        return this._components.asObservable();
    }

    get assetManagementSites$(): Observable<AssetManagementSite[]> {
        return this._assetManagementSites.asObservable();
    }

    get clientLocationData$(): Observable<ClientCostCenter[]> {
        return this._clientLocationData.asObservable();
    }

    get assetAttachments$(): Observable<AssetAttachment[]> {
        return this._assetAttachments.asObservable();
    }

    get inspectionAttachments$(): Observable<InspectionAttachment[]> {
        return this._inspectionAttachments.asObservable();
    }

    get assetSubmissions$(): Observable<AssetSubmissions[]> {
        return this._submissions.asObservable();
    }

    constructor(private readonly _http: HttpClient, private readonly _users: UsersService) {
        const isFromOverview = window.location.hash.includes('fromOverview');
        const userProfile$ = this._users.currentProfile$;
        userProfile$.subscribe((user) => {
            this.currentUser = user;
        });
        const userId = this.currentUser?.id;
        if (!isFromOverview) {
            // Not from Overview: Always fetch fresh data
            this.fetchAndCacheAllData();
        } else {
            // From Overview: Try to use cached data
            Promise.all([
                this.indexedDbService.getItem<any[]>(`assets_${userId}`),
                this.indexedDbService.getItem<any[]>(`inspections_${userId}`),
                this.indexedDbService.getItem<any[]>(`anomalies_${userId}`),
                this.indexedDbService.getItem<any[]>(`assetManagementSites_${userId}`),
                this.indexedDbService.getItem<any[]>(`clientLocationData_${userId}`)
            ]).then(([assetsDBData, anomaliesDBData, inspectionsDBData, assetManagementSitesDBData, clientLocationDBData ]) => {
                if (assetsDBData && anomaliesDBData && inspectionsDBData && assetManagementSitesDBData && clientLocationDBData) {
                    // Push cached data into ReplaySubjects
                    this._assets.next(assetsDBData);
                    this._anomalies.next(anomaliesDBData);
                    this._inspections.next(inspectionsDBData);
                    this._assetManagementSites.next(assetManagementSitesDBData);
    
                    // Similarly, load clientLocationData if needed
                } else {
                    // If any cache missing, fallback to fresh API calls
                    this.fetchAndCacheAllData();
                }
            });
        }
    }

    private fetchAndCacheAllData() {
        const userId = this.currentUser?.id;
        this.getAssets().subscribe(data => {
            this._assets.next(data);
            this.indexedDbService.setItem(`assets_${userId}`, data);
        });
    
        this.getInspections().subscribe(data => {
            this._inspections.next(data);
            this.indexedDbService.setItem(`inspections_${userId}`, data);
        });
    
        this.getAllAssetManagementSites().subscribe(data => {
            this._assetManagementSites.next(data);
            this.indexedDbService.setItem(`assetManagementSites_${userId}`, data);
        });
    
        this.getAnomalies().subscribe(data => {
            this._anomalies.next(data);
            this.indexedDbService.setItem(`anomalies_${userId}`, data);
        });
    
        this.getClientLocationData().subscribe(data => {
            this._clientLocationData.next(data);
            this.indexedDbService.setItem(`clientLocationData_${userId}`, data);
        });
    }

    getSAS() {
        return this._http.get<string>(`${environment.api.url}/CredoSoft/SAS`, {
            responseType: 'text' as 'json'
        });
    }

    addPreference(
        userId: string,
        storageKey: string,
        value: string
    ): Observable<any> {
        return this._http
            .post(`${environment.api.url}/Antea/addPreferenceField`, {
                userId,
                storageKey,
                value
            })
            .pipe(
                catchError((error) => {
                    throw error; // Rethrow or handle as needed
                })
            );
    }

    public getPreference(userId: string, storageKey: string): Observable<any> {
        return this._http.get(
            `${environment.api.url}/Antea/getPreferenceField`,
            { params: { userId, storageKey } }
        );
    }

    getAllInspectionsAsDataSource(): Observable<DataSource> {
        return this.inspections$.pipe(
            map((Inspections) => {
                return new DataSource({
                    store: new ArrayStore({
                        data: Inspections
                    })
                });
            })
        );
    }

    getAllAssetsAsDataSource(): Observable<DataSource> {
        return this.assets$.pipe(
            map((assets) => {
                return new DataSource({
                    store: new ArrayStore({
                        data: assets
                    })
                });
            })
        );
    }
    getAllAnomaliesAsDataSource(): Observable<DataSource> {
        return this.anomalies$.pipe(
            map((anomalies) => {
                return new DataSource({
                    store: new ArrayStore({
                        data: anomalies
                    })
                });
            })
        );
    }

    getComponents(assetObjId: number): Observable<AssetComponent[]> {
        return this._http
            .get<AssetComponent[]>(
                `${environment.api.url}/Antea/AssetComponents/${assetObjId}`
            )
            .pipe(
                map(
                    (components) =>
                        components.map((c) => new AssetComponent(c)),
                    debug('Components')
                )
            );
    }

    getChamberData(assetObjId: string): Observable<ChamberData[]> {
        return this._http
            .get<ChamberData[]>(
                `${environment.api.url}/Antea/ChamberData/?assetId=${assetObjId}`
            )
            .pipe(
                map(
                    (chamber) => chamber.map((c) => new ChamberData(c)),
                    debug('ChamberData')
                )
            );
    }

    getAlarmCalc(componentObjId: number): Observable<AlarmCalc> {
        return this._http
            .get<AlarmCalc>(
                `${environment.api.url}/CredoSoft/AlarmCalcs/${componentObjId}`
            )
            .pipe(
                map((calc) => new AlarmCalc(calc)),
                debug('Alarm Calc')
            );
    }

    getAllSubmissionsAsDataSource(): Observable<DataSource> {
        return this.anomalies$.pipe(
            map((submissions) => {
                return new DataSource({
                    store: new ArrayStore({
                        data: submissions
                    })
                });
            })
        );
    }

    private getAllAssetManagementSites(): Observable<AssetManagementSite[]> {
        return this._http
            .get<AssetManagementSite[]>(
                `${environment.api.url}/Antea/AssetManagementSites`
            )
            .pipe(
                map((sites) =>
                    sites && sites.length > 0
                        ? sites
                            .filter((s) => s.locationname !== null)
                            .sort((a, b) =>
                                a.locationname.localeCompare(b.locationname)
                            )
                            .map((s) => new AssetManagementSite(s))
                        : []
                ),
                debug('asset management sites')
            );
    }

    public getAssets(): Observable<Asset[]> {
        return this._http
            .get<any[]>(`${environment.api.url}/Antea/Assests`)
            .pipe(
                map((assets) =>
                    assets && assets.length > 0
                        ? assets
                            .filter((a) => a.assetid !== null)
                            .sort((a, b) =>
                                a.assetid.localeCompare(b.assetid)
                            )
                            .map((a) => new Asset(a))
                        : []
                ),
                debug('assets')
            );
    }
    public getAnomalies(): Observable<AnomaliesRecommendations[]> {
        return this._http
            .get<any[]>(`${environment.api.url}/Antea/Anomalies`)
            .pipe(
                map((anomalies) =>
                    anomalies && anomalies.length > 0
                        ? anomalies
                            .filter((a) => a.anomaly !== null)
                            .sort((a, b) =>
                                a.anomaly.localeCompare(b.anomaly)
                            )
                            .map((a) => new AnomaliesRecommendations(a))
                        : []
                ),
                debug('assets')
            );
    }

    public getInspections(): Observable<AssetInspection[]> {
        return this._http
            .get<AssetInspection[]>(`${environment.api.url}/Antea/Inspections`)
            .pipe(
                map((inspections) =>
                    inspections && inspections.length > 0
                        ? inspections.map((inspection) => {
                            if (inspection.assetmanagementcategory) {
                                try {
                                    const categoryArray = JSON.parse(
                                        inspection.assetmanagementcategory
                                    );
                                    const descriptions = categoryArray.map(
                                        (item: any) => item.DESCRIPTION
                                    );
                                    inspection.assetmanagementcategory =
                                        descriptions.join(', ');
                                } catch (error) {
                                    console.error(
                                        'Error parsing assetmanagementcategory',
                                        error
                                    );
                                    inspection.assetmanagementcategory = null;
                                }
                            }
                            return new AssetInspection(inspection);
                        })
                        : []
                ),
                debug('inspections')
            );
    }

    getAllComponents(assetId): Observable<AssetComponent[]> {
        return this._http
            .get<any[]>(
                `${environment.api.url}/Antea/AssetComponents?assetId=${assetId}`
            )
            .pipe(
                map((components) =>
                    components && components.length > 0
                        ? components.map((c) => new AssetComponent(c))
                        : []
                ),
                debug('components')
            );
    }
    getAllGeneralAnalysis(assetId): Observable<GeneralAnalysis[]> {
        return this._http
            .get<any[]>(
                `${environment.api.url}/Antea/GeneralAnalysis?assetId=${assetId}`
            )
            .pipe(
                map((general) =>
                    general && general.length > 0
                        ? general.map((c) => new GeneralAnalysis(c))
                        : []
                ),
                debug('General analysis')
            );
    }
    getAllCorrosionAnalysis(operationId): Observable<CorrosionAnalysis[]> {
        return (
            this._http
                .get<any[]>(
                    `${environment.api.url}/Antea/CorrosionAnalysis?operationId=${operationId}`
                )
                .pipe(
                    map((general) =>
                        general && general.length > 0
                            ? general.map((c) => new CorrosionAnalysis(c))
                            : []
                    ),
                    debug('Corrosion analysis')
                )
        );
    }
    getClientLocationData(): Observable<any> {
        return this._http.get<any>(`${environment.api.url}/Antea/ClientLocationData`).pipe(
            catchError((error: any) => {
                console.error('Error fetching client location data:', error);
                return throwError('Failed to fetch client location data');
            })
        );
    }

    public getAllAssetAttachments(assetid): Observable<AssetAttachment[]> {
        return this._http
            .get<AssetAttachment[]>(
                `${environment.api.url}/Antea/AssetAttachments?assetid=${assetid}`
            )
            .pipe(
                map((attachments) =>
                    attachments && attachments.length > 0
                        ? attachments.map((a) => new AssetAttachment(a))
                        : []
                ),
                debug('asset attachments')
            );
    }

    public getAllInspectionAttachments(
        operationid
    ): Observable<InspectionAttachment[]> {
        return this._http
            .get<InspectionAttachment[]>(
                `${environment.api.url}/Antea/InspectionsAttachments?operationid=${operationid}`
            )
            .pipe(
                map((attachments) =>
                    attachments && attachments.length > 0
                        ? attachments.map((a) => new InspectionAttachment(a))
                        : []
                ),
                debug('inspection attachments')
            );
    }

    public getSubmissions(assetId): Observable<AssetSubmissions[]> {
        return this._http
            .get<AssetSubmissions[]>(
                `${environment.api.url}/Antea/Submissions?assetid=${assetId}`
            )
            .pipe(
                map((submissions) =>
                    submissions && submissions.length > 0
                        ? submissions.map((a) => new AssetSubmissions(a))
                        : []
                ),
                debug('submissions')
            );
    }
    public uploadSubmissions(data: any): Observable<any> {
        return this._http.post(
            `${environment.api.url}/Antea/Submissions`,
            data
        );
    }
    public deleteSubmission(id: string): Observable<any> {
        return this._http
            .delete<any>(`${environment.api.url}/Antea/Submissions?id=${id}`)
            .pipe(
                map((response) => {
                    return response;
                }),
                catchError((error) => {
                    console.error('Error deleting submission:', error);
                    throw error;
                })
            );
    }

    public downloadAttachment(location: any): Observable<any> {
        let url = this.buildQuery(`${environment.api.url}/Antea/DownloadFile`, {
            filePath: location
        });
        return this._http.get(url);
    }
    public getSystemManagementCategories(): Observable<
        SystemManagementCategories[]
    > {
        return this._http
            .get<SystemManagementCategories[]>(
                `${environment.api.url}/Antea/SystemManagementCategories`
            )
            .pipe(
                map((systemcategories) =>
                    systemcategories.map((a) => ({
                        ...a,
                        isChecked: false
                    }))
                ),
                debug('SystemManagementCategories')
            );
    }
    buildQuery(baseURL, params) {
        const query = Object.keys(params)
            .map(
                (key) =>
                    encodeURIComponent(key) +
                    '=' +
                    encodeURIComponent(params[key])
            )
            .join('&');
        return `${baseURL}?${query}`;
    }
}
function throwError(arg0: string): any {
    throw new Error('Function not implemented.');
}
