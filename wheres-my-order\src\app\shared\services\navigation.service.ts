import { Injectable } from '@angular/core';
import cloneDeep from 'clone-deep';
import { Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';
import {
    administrationMenu,
    aimaasMenu,
    aiToolsMenu,
    apmClientManagement,
    apmMenu,
    apmToolsMenu,
    assetManagementMenu,
    authHistoryMenu,
    connectedWorkerMenu,
    CPDxTreeViewItem,
    emptyWorkmanagementMenu,
    fieldServiceManagementMenu,
    fullCalculatorsMenu,
    fullWorkManagementMenu,
    homeMenu,
    leakReportingMenu,
    remoteMonitoringMenu,
    requestAccessMenu,
    userAuditMenu
} from '../../app-navigation';
import { userHasSomesRoles } from '../../core/operators';
import { UserProfile } from '../../profile/models';
import { AuthService } from './auth.service';
import { UsersService } from './users.service';

@Injectable({
    providedIn: 'root'
})
export class NavigationService {
    constructor(
        private readonly _authService: AuthService,
        private readonly _usersService: UsersService
    ) {}

    getMenuItems(): Observable<CPDxTreeViewItem[]> {
        if (!this._authService.isLoggedIn) return of([homeMenu]);
        return this._usersService.currentProfile$.pipe(
            map((user) => {
                const currentNavigation = [homeMenu];
                if (!user.active) {
                    return currentNavigation;
                }

                if (user.roles.some((a) => a.toLowerCase() === 'app:admin')) {
                    this.getAppAdminFeatures(currentNavigation);
                } else {
                    if (
                        user.roles.some(
                            (a) => a.toLowerCase() === 'aitools:esg'
                        )
                    ) {
                        currentNavigation.push(aiToolsMenu);
                    }
                    // Descope module
                    // if (this.anyStartingWith(user.roles, 'wmo')) {
                    //     this.getWMOFeatures(currentNavigation, user.roles);
                    // }

                    if (user.isTeamEmployee) {
                         currentNavigation.push(fullCalculatorsMenu);
                    } 
                    //else if (
                    //    user.roles.some((role) =>
                    //        role.toLowerCase().trim().startsWith('joints')
                    //     )
                    //) {
                    //    currentNavigation.push(boltedFlangeCalculatorMenu);
                    // }
                     

                    this.getWorkManagementFeatures(currentNavigation, user);
                    // Descope module
                    // if (this.anyStartingWith(user.roles, 'edr')) {
                    //     this.getEDRFeatures(currentNavigation);
                    // }

                    if (
                        this.anyStartingWith(user.roles, 'demo') ||
                        this.anyStartingWith(user.roles, 'aimaas') ||
                        this.anyStartingWith(user.roles, 'remotemonitoring')
                    ) {
                        this.getAssetManagementFeatures(
                            currentNavigation,
                            user.roles
                        );
                    }

                    if (
                        user.roles.some((role) =>
                            role.toLowerCase().endsWith('admin')
                        )
                    ) {
                        this.getAdminFeatures(currentNavigation);
                    }
                }

                // Always show Request Access at the bottom
                currentNavigation.push(requestAccessMenu);

                return currentNavigation;
            })
        );
    }

    private getWorkManagementFeatures(
        navigation: CPDxTreeViewItem[],
        user: UserProfile
    ) {
        const workManagementMenu = cloneDeep(emptyWorkmanagementMenu);

        const isAppAdmin = user.roles.some(
            (role) => role.toLowerCase().trim() === 'app:admin'
        );
        const hasFSM = user.roles.some((role) =>
            role.toLowerCase().trim().startsWith('fsm')
        );
        const hasPIA = user.roles.some((role) =>
            role.toLowerCase().trim().startsWith('pia')
        );

        const hasConnectedWorker = user.roles.some((role) =>
            role.toLowerCase().trim().startsWith('connectedworker')
        );

        const hasMOS = user.roles.some((role) =>
            role.toLowerCase().trim().startsWith('mos')
        );
        const hasCrdAdmin = user.roles.some(
            (role) => role.toLowerCase().trim() === 'crd:admin'
        );

        const hasAPMRole = user.roles.some((role) => {
            const cleanRole = role.toLowerCase().trim();
            return (
                cleanRole === 'apm:admin' ||
                cleanRole === 'apm:edit' ||
                cleanRole === 'apm:qaqc' ||
                cleanRole === 'apm:view'
            );
        });

        const hasAPMAdminRole = user.roles.some(
            (role) => role.toLowerCase().trim() === 'apm:admin'
        );

        const hasAPMToolsRole = user.roles.some(
            (role) => role.toLowerCase().trim() === 'apm:tools'
        );

        // Temporary
        // if (user.isTeamEmployee) {
        if (user.roles.some((r) => r.toLowerCase().trim() === 'crd:admin')) {
            // if (hasCrdAdmin) {
            // Descope module
            // workManagementMenu.items.push(chemicalReferenceDictionaryAdminMenu);
            // } else
            //     workManagementMenu.items.push(chemicalReferenceDictionaryMenu);
        }

        if (hasFSM) {
            workManagementMenu.items.push(fieldServiceManagementMenu);
        }
        // Descope module
        // if (hasPIA) {
        //     if (isAppAdmin) {
        //         workManagementMenu.items.push(fullPipelineIntegrityMenu);
        //     } else {
        //         if (
        //             user.roles.some(
        //                 (role) => role.toLowerCase() === 'pia:qa'
        //             ) &&
        //             !user.roles.some(
        //                 (role) =>
        //                     role.toLowerCase() === 'pia:client' ||
        //                     role.toLowerCase() === 'pia:view' ||
        //                     role.toLowerCase() === 'pia:technician'
        //             )
        //         ) {
        //             blankPipelineIntegrityMenu.items.push(
        //                 pipelineDigsiteDetailsOnly
        //             );
        //         }
        //         if (
        //             user.roles.some(
        //                 (role) =>
        //                     role.toLowerCase() === 'pia:view' ||
        //                     role.toLowerCase() === 'pia:client' ||
        //                     role.toLowerCase() === 'pia:technician'
        //             )
        //         ) {
        //             blankPipelineIntegrityMenu.items.push(
        //                 pipelineDigsiteStatusOnly
        //             );
        //             blankPipelineIntegrityMenu.items.push(
        //                 pipelineDigsiteDetailsOnly
        //             );
        //         }

        //         if (
        //             user.roles.some(
        //                 (role) =>
        //                     role.toLowerCase() === 'pia:admin' ||
        //                     role.toLowerCase() === 'pia:scheduler'
        //             )
        //         ) {
        //             blankPipelineIntegrityMenu.items.push(
        //                 pipelineIntegritySetupOnly
        //             );
        //         }

        //         if (
        //             user.roles.some((role) => role.toLowerCase() === 'pia:view')
        //         ) {
        //             blankPipelineIntegrityMenu.items.push(
        //                 pipelineIntegrityMetricsOnly
        //             );
        //         }

        //         workManagementMenu.items.push(blankPipelineIntegrityMenu);
        //     }
        // }

        if (hasMOS) {
            // Descope module
            // workManagementMenu.items.push(mechanicalDataCollectionMenu);
        }

        if (hasConnectedWorker) {
            this._usersService.currentProfile$
                .pipe(
                    userHasSomesRoles([
                        'connectedworker:chevronAll',
                        'connectedworker:chevronactivitytracker',
                        'connectedworker:chevroncesosi'
                    ])
                )
                .subscribe((hasAccess) => {
                    if (hasAccess) {
                        connectedWorkerMenu.items.push({
                            text: 'Chevron',
                            path: '/connected-worker/chevron'
                        });
                    }
                });

            this._usersService.currentProfile$
                .pipe(
                    userHasSomesRoles([
                        'connectedworker:gpcall',
                        'connectedworker:gpcsupplementsections'
                    ])
                )
                .subscribe((hasAccess) => {
                    if (hasAccess) {
                        connectedWorkerMenu.items.push({
                            text: 'GPC',
                            path: '/connected-worker/gpc'
                        });
                    }
                });

            this._usersService.currentProfile$
                .pipe(userHasSomesRoles(['connectedworker:admin']))
                .subscribe((hasAccess) => {
                    if (hasAccess) {
                        connectedWorkerMenu.items = [];
                        connectedWorkerMenu.items.push(
                            {
                                text: 'Chevron',
                                path: '/connected-worker/chevron'
                            },
                            {
                                text: 'GPC',
                                path: '/connected-worker/gpc'
                            }
                        );
                    }
                });

            if (connectedWorkerMenu.items.length > 0) {
                workManagementMenu.items.push(connectedWorkerMenu);
            }
        }

        if (isAppAdmin || hasAPMRole) {
            const clone = cloneDeep(apmMenu);
            clone.items.push(leakReportingMenu);
            if (hasAPMToolsRole) {
                clone.items.push(apmToolsMenu);
            }
            if (isAppAdmin || hasAPMAdminRole) {
                clone.items.push(apmClientManagement);
                workManagementMenu.items.push(clone);
            } else {
                workManagementMenu.items.push(clone);
            }
        }

        if (workManagementMenu.items.length > 0) {
            navigation.push(workManagementMenu);
        }
    }

    private getAppAdminFeatures(navigation: CPDxTreeViewItem[]) {
        // de-scoped modules
        // const wmoMenuCopy = { ...wmoMenu, items: [...wmoMenu.items] };
        // wmoMenuCopy.items.push({ ...wmoAdministrationMenu });
        const adminMenu = {
            ...administrationMenu,
            items: [...administrationMenu.items]
        };
        adminMenu.items.push(authHistoryMenu);
        adminMenu.items.push(userAuditMenu);
        const assetMenu = {
            ...assetManagementMenu,
            items: [...assetManagementMenu.items]
        };
        //Descope module
        assetMenu.items.unshift(aimaasMenu);
        assetMenu.items.unshift(remoteMonitoringMenu);
        const fsmMenu = {
            ...fieldServiceManagementMenu,
            items: [...fieldServiceManagementMenu.items]
        };
        navigation.push(
            aiToolsMenu,
            // wmoMenuCopy, // Descope module
            fullCalculatorsMenu,
            fullWorkManagementMenu,
            // equipmentCenterMenu, // Descope module
            assetMenu,
            adminMenu
        );
    }

    // Descope module
    // private getWMOFeatures(navigation: any[], roles: string[]) {
    //     if (roles.some((role) => role.toLowerCase().trim() === 'app:admin')) {
    //         wmoMenu.items.push(wmoAdministrationMenu);
    //     }
    //     navigation.push(wmoMenu);
    // }

    // Descope module
    // private getEDRFeatures(navigation: any[]) {
    //     navigation.push(equipmentCenterMenu);
    // }

    private getAssetManagementFeatures(navigation: any[], roles: string[]) {
        const assetMenu = {
            ...assetManagementMenu,
            items: [...assetManagementMenu.items]
        };
        if (!this.anyStartingWith(roles, 'demo')) {
            assetMenu.items = [];
        }

        if (this.anyStartingWith(roles, 'aimaas')) {
            assetMenu.items.unshift(aimaasMenu);
        }
        if (this.anyStartingWith(roles, 'remotemonitoring')) {
            assetMenu.items.unshift(remoteMonitoringMenu);
        }
        navigation.push(assetMenu);
    }

    private getAdminFeatures(navigation: any[]) {
        navigation.push(administrationMenu);
    }

    private anyStartingWith(roles: string[], prefix: string) {
        return roles.some((a) => a.toLowerCase().startsWith(prefix));
    }
}
