{"AzureAdB2C": {"Instance": "https://teamincb2c.b2clogin.com/", "ClientId": "___AZURE_AD_B2C_CLIENT_ID___", "Domain": "teamincb2c.onmicrosoft.com", "SignUpSignInPolicyId": "B2C_1_signupsignin"}, "AzureAd": {"AuthenticationMode": "ServicePrincipal", "AuthorityUri": "https://login.microsoftonline.com/organizations/", "ClientId": "___AZURE_AD_CLIENT_ID___", "TenantId": "___AZURE_AD_TENANT_ID___", "Scope": ["https://analysis.windows.net/powerbi/api/.default"], "ClientSecret": "___AZURE_AD_CLIENT_SECRET___"}, "BlobStorage": {"BlobEndpoint": "https://___STORAGE_ACCOUNT_NAME___.blob.core.windows.net/", "AccountName": "___STORAGE_ACCOUNT_NAME___", "APMContainer": "apm-prod", "APMReportingContainer": "apm-prod-reporting", "APMStorageAccountName": "___STORAGE_ACCOUNT_NAME___", "APMBlobContainerName": "apm-prod", "APMWOStorageAccountName": "___STORAGE_ACCOUNT_NAME___", "APMWOBlobContainerName": "apm-workorders-prod", "KeyVaultName": "___KEY_VAULT_NAME___", "AnteaAttachmentsBlobContainer": "antea-attachments-prod", "AnteaSubmissionsBlobContainer": "antea-submissions-prod"}, "Connections": {"Database": "ClientPortalProd", "UserProfiles": "user-profiles", "Roles": "roles", "EquipmentRequests": "equipment-requests", "FlangeCalculations": "flange-calculations", "Notifications": "notifications", "ReleaseNotes": "release-notes", "AuthHistory": "auth-history", "Endpoint": "___COSMOS_DB_ENDPOINT___", "AuthKey": "___COSMOS_DB_PRIMARY_KEY___", "KeyVaultName": "___KEY_VAULT_NAME___", "ResourceGroupName": "___RESOURCE_GROUP_NAME___", "SubscriptionId": "___AZURE_SUBSCRIPTION_ID___", "DatabaseName": "cosmos-clientportalapi-prod-001"}, "APM": {"Environment": "production", "CmsSettings": {"CMSRootObjectName": "Inspection", "CMSBaseInspectionsContainerName": "cms-base-inspections", "CMSClientInspectionsContainerName": "cms-client-inspections", "CMSClientInspectionsSitesSubcontainerName": "sites", "CMSClientInspectionsSitesInspectionsSubcontainerName": "inspections", "CMSClientInspectionsAllSitesSubcontainerName": "all-sites-inspections", "CMSDistrictInspectionsContainerName": "cms-district-inspections", "CMSDistrictInspectionsSitesSubcontainerName": "sites", "CMSDistrictInspectionsSitesInspectionsSubcontainerName": "inspections", "CMSDistrictInspectionsAllSitesSubcontainerName": "all-sites-inspections", "CMSBaseInspectionsFirestoreCollectionName": "cms-base-inspections", "CMSClientInspectionsFirestoreCollectionName": "cms-client-inspections", "CMSClientInspectionsSitesFirestoreSubcollectionName": "sites", "CMSClientInspectionsSitesInspectionsFirestoreSubcollectionName": "inspections", "CMSClientInspectionsAllSitesFirestoreSubcollectionName": "all-sites-inspections", "CMSDistrictInspectionsFirestoreCollectionName": "cms-district-inspections", "CMSDistrictInspectionsSitesFirestoreSubcollectionName": "sites", "CMSDistrictInspectionsSitesInspectionsFirestoreSubcollectionName": "inspections", "CMSDistrictInspectionsAllSitesFirestoreSubcollectionName": "all-sites-inspections"}}, "PowerBI": {"WorkspaceId": "___POWERBI_WORKSPACE_ID___", "ReportId": "___POWERBI_REPORT_ID___"}, "Azure:SignalR:ConnectionString": "___SIGNALR_CONNECTION_STRING___", "ApplicationInsights": {"ConnectionString": "___APPLICATION_INSIGHTS_CONNECTION_STRING___"}, "Logging": {"ApplicationInsights": {"LogLevel": {"Default": "Information", "Microsoft": "Warning"}}, "LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "DeploymentEnvironment": "Production", "AllowedHosts": "*"}