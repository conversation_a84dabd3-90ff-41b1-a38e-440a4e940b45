import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Router } from '@angular/router';
import { AssetInspection } from '../../models';

@Component({
    selector: 'app-inspection-schedule',
    templateUrl: './inspection-schedule.component.html',
    styleUrls: ['./inspection-schedule.component.scss']
})
export class InspectionScheduleComponent implements OnInit {
    @Input() assetinspection: AssetInspection[];
    selectedRowData: any;
    completeHistoryButtonDisable: boolean = false;
    @Output() CompleteHistoryButtonClicked = new EventEmitter<any>();
    constructor(private _router: Router) {}

    ngOnInit(): void {
        this.assetinspection = this.assetinspection.filter(
            (inspection) => inspection.scheduletype !== null
        );
        this.completeHistoryButtonDisable = this.assetinspection?.length <= 0;
    }
    AllDatesClicked() {
        this._router.navigate(['/aimaas/inspection-drilldown'], {
            state: {
                data: {
                    currentFilter: [
                        [
                            'assetidname',
                            '=',
                            this.assetinspection[0].assetidname
                        ],
                        'and',
                        ['inspectionstatus', '=', 'Past Inspection']
                    ]
                }
            }
        });
        this.CompleteHistoryButtonClicked.emit(
            this.assetinspection[0].assetidname
        );
    }
    cellTemplate = (cellElement, cellInfo) => {
        const options = { timeZone: 'America/Chicago' };

        const dateStr = cellInfo.value
            ? cellInfo.value.toLocaleDateString('en-US', options)
            : '';
        const link = document.createElement('a');
        link.href = '#';
        link.innerText = dateStr;
        link.onclick = (event) => {
            event.preventDefault();
            this.onLinkClick(cellInfo.data);
        };
        cellElement.appendChild(link);
    };

    onLinkClick(rowData: any) {
        this._router.navigate(['/aimaas/inspection-drilldown'], {
            state: {
                data: {
                    currentFilter: [
                        ['assetidname', '=', rowData.assetidname],
                        'and',
                        ['inspectiontype', '=', rowData.inspectiontype],
                        'and',
                        [
                            'inspectiondue',
                            '=',
                            new Date(String(rowData.nextdate).split(' ')[0])
                        ],
                        'and',
                        ['inspectionstatus', '=', 'Past Inspection']
                    ]
                }
            }
        });
    }

    onSelectionChanged(event: any) {
        this.selectedRowData = event.selectedRowsData[0];
    }
    formatNotes(data) {
        if (data.nextduedatenotes == null) {
            return ' ';
        }
        const doc = new DOMParser().parseFromString(
            data.nextduedatenotes,
            'text/html'
        );
        return doc.documentElement.textContent ?? ' ';
    }
}
