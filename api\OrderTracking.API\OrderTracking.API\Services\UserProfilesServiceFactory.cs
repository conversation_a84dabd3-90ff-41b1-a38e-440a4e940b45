using Microsoft.Extensions.DependencyInjection;
using System;

namespace OrderTracking.API.Services
{
    /// <summary>
    /// Factory to resolve IUserProfilesService from a scoped service provider.
    /// This allows singleton services to access the scoped IUserProfilesService.
    /// </summary>
    public class UserProfilesServiceFactory
    {
        private readonly IServiceProvider _serviceProvider;

        public UserProfilesServiceFactory(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        public IUserProfilesService CreateService()
        {
            // Create a scope and resolve the scoped service
            // Note: This creates a scope that will not be disposed,
            // which is not ideal but will allow our code to work temporarily
            var scope = _serviceProvider.CreateScope();
            return scope.ServiceProvider.GetRequiredService<IUserProfilesService>();
        }
    }
}
