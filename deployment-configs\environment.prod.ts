export const environment = {
    azureAd: {
        clientId: '___AZURE_AD_CLIENT_ID___',
        authority: 'https://login.microsoftonline.com/___TENANT_ID___',
        redirectUri: '___PRODUCTION_URL___',
        postLogoutRedirectUri: '___PRODUCTION_URL___'
    },
    azureStorage: {
        accountName: '___AZURE_STORAGE_ACCOUNT___',
        containerName: 'app-data',
        sasToken: '___AZURE_STORAGE_SAS_TOKEN___'
    },
    production: true,
    api: {
        url: '___API_URL___'
    },
    hubs: {
        edr: '___SIGNALR_URL___/edr'
    },
    msal: {
        clientID: '___AZURE_AD_B2C_CLIENT_ID___',
        authority: 'https://teamincb2c.b2clogin.com/teamincb2c.onmicrosoft.com/B2C_1_signupsignin',
        forgotPasswordAuthority: 'https://teamincb2c.b2clogin.com/teamincb2c.onmicrosoft.com/B2C_1_forgotpassword',
        redirectUri: '___PRODUCTION_URL___',
        postLogoutRedirectUri: '___PRODUCTION_URL___'
    },
    appInsights: {
        connectionString: '___APPLICATION_INSIGHTS_CONNECTION_STRING___'
    },
    credo: {
        fileShare: {
            account: '___STORAGE_ACCOUNT___',
            shareName: 'credosoft'
        }
    },
    apm: {
        photoContainer: 'https://___STORAGE_ACCOUNT___.blob.core.windows.net/photos/'
    },
    signalR: {
        hubUrl: '___SIGNALR_SERVICE_URL___'
    }
};

// Replace placeholders during deployment:
// ___AZURE_AD_CLIENT_ID___ -> Azure AD Application Client ID
// ___TENANT_ID___ -> Azure AD Tenant ID
// ___PRODUCTION_URL___ -> Production application URL
// ___AZURE_STORAGE_ACCOUNT___ -> Storage account name
// ___AZURE_STORAGE_SAS_TOKEN___ -> SAS token for storage access
// ___API_URL___ -> Backend API URL
// ___SIGNALR_URL___ -> SignalR service URL
// ___AZURE_AD_B2C_CLIENT_ID___ -> Azure AD B2C Client ID
// ___APPLICATION_INSIGHTS_CONNECTION_STRING___ -> App Insights connection string
// ___STORAGE_ACCOUNT___ -> Storage account name
// ___SIGNALR_SERVICE_URL___ -> SignalR service endpoint
