<form class="dx-card content-block responsive-paddings"
      *ngIf="currentUser$ | async as currentUser"
      (submit)="requestAccess(btn)">

    <h2>Request Access</h2>
    <p>
        Please use the options below to request access to one or more modules.
    </p>

    <h3>Modules:</h3>
    <dx-list #modulesList
             [items]="modules"
             [selectionMode]="'all'"
             [(selectedItemKeys)]="selectedModules"
             [showSelectionControls]="true"></dx-list>

    <div *ngIf="modulesList.selectedItems.includes('Where\'s My Order')">
        <h3>Where's My Order Special Access:</h3>
        <dx-select-box [items]="wmoSpecialAccessItems"
                       [(value)]="selectedWMOSpecialAccess"
                       [stylingMode]="'filled'"></dx-select-box>
        <small><em>(optional)</em></small>
    </div>

    <div *ngIf="currentUser.isTeamEmployee; else nonTeamEmployeeContent">
        <div
             *ngIf="modulesList.selectedItems.includes('Asset Integrity Management as a Service (AIMaaS)')">
            <h3>Client and Site Names:</h3>
            <dx-text-box [(value)]="managementSites"
                         [stylingMode]="'filled'">
                <dx-validator>
                    <dxi-validation-rule type="required"
                                         message="Client and Site Names are required">
                    </dxi-validation-rule>
                </dx-validator>
            </dx-text-box>
        </div>

        <h3>Company(s):</h3>
        <dx-tag-box [items]="clients$ | async"
                    [(value)]="selectedCompanies"
                    [searchEnabled]="true"
                    [stylingMode]="'filled'">
        </dx-tag-box>
        <small><em>Specify the company(s) you work for.</em></small>

        <h3>District(s):</h3>
        <dx-tag-box [items]="districts$ | async"
                    [valueExpr]="'label'"
                    [displayExpr]="'label'"
                    [(value)]="selectedDistricts"
                    [searchEnabled]="true"
                    [stylingMode]="'filled'"></dx-tag-box>
        <small><em>Specify the district(s) you work for.</em></small>
    </div>

    <ng-template #nonTeamEmployeeContent>
        <h3>Company Name and Site:</h3>
        <dx-text-box [(value)]="companyNameAndSite"
                     [stylingMode]="'filled'">
            <dx-validator>
                <dxi-validation-rule type="required"
                                     message="Company Name and Site are required">
                </dxi-validation-rule>
            </dx-validator>
        </dx-text-box>

        <h3>TEAM Contact: <small>A good contact within TEAM to verify your
                access request</small></h3>
        <dx-text-box [(value)]="teamContact"
                     [stylingMode]="'filled'">
            <dx-validator>
                <dxi-validation-rule type="required"
                                     message="TEAM Contact is required">
                </dxi-validation-rule>
            </dx-validator>
        </dx-text-box>

        <dx-validation-summary id="summary"></dx-validation-summary>
    </ng-template>

    <div class="button">
        <dx-button #btn
                   type="default"
                   text="Request Access"
                   [disabled]="sendingRequest"
                   [useSubmitBehavior]="true"></dx-button>
    </div>
</form>