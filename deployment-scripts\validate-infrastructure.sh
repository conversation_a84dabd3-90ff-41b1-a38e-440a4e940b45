#!/bin/bash

# Azure Infrastructure Validation Script
# Usage: ./validate-infrastructure.sh <environment>

set -e

ENVIRONMENT=${1:-dev}
RESOURCE_GROUP="rg-kraken-${ENVIRONMENT}-001"

echo "🔍 Validating Azure infrastructure for ${ENVIRONMENT} environment..."

# Check resource group exists
echo "📋 Checking resource group..."
if az group show --name $RESOURCE_GROUP &>/dev/null; then
    echo "✅ Resource group exists: $RESOURCE_GROUP"
else
    echo "❌ Resource group not found: $RESOURCE_GROUP"
    exit 1
fi

# Check Cosmos DB
echo "🗄️ Checking Cosmos DB..."
COSMOS_NAME="opt-${ENVIRONMENT}-cosmosdb-001"
if az cosmosdb show --name $COSMOS_NAME --resource-group $RESOURCE_GROUP &>/dev/null; then
    echo "✅ Cosmos DB exists: $COSMOS_NAME"
    
    # Check database and containers
    DATABASE_NAME="cosmos-clientportalapi-${ENVIRONMENT}-001"
    if az cosmosdb sql database show --account-name $COSMOS_NAME --resource-group $RESOURCE_GROUP --name $DATABASE_NAME &>/dev/null; then
        echo "✅ Database exists: $DATABASE_NAME"
        
        # Check key containers
        CONTAINERS=("user-profiles" "roles" "equipment-requests" "notifications")
        for container in "${CONTAINERS[@]}"; do
            if az cosmosdb sql container show --account-name $COSMOS_NAME --resource-group $RESOURCE_GROUP --database-name $DATABASE_NAME --name $container &>/dev/null; then
                echo "✅ Container exists: $container"
            else
                echo "❌ Container missing: $container"
            fi
        done
    else
        echo "❌ Database not found: $DATABASE_NAME"
    fi
else
    echo "❌ Cosmos DB not found: $COSMOS_NAME"
    exit 1
fi

# Check Storage Account
echo "💾 Checking Storage Account..."
STORAGE_NAME="stakraken${ENVIRONMENT}001"
if az storage account show --name $STORAGE_NAME --resource-group $RESOURCE_GROUP &>/dev/null; then
    echo "✅ Storage Account exists: $STORAGE_NAME"
    
    # Check blob containers
    CONTAINERS=("apm-${ENVIRONMENT}" "apm-workorders-${ENVIRONMENT}" "antea-attachments-${ENVIRONMENT}")
    for container in "${CONTAINERS[@]}"; do
        if az storage container show --name $container --account-name $STORAGE_NAME &>/dev/null; then
            echo "✅ Blob container exists: $container"
        else
            echo "❌ Blob container missing: $container"
        fi
    done
else
    echo "❌ Storage Account not found: $STORAGE_NAME"
    exit 1
fi

# Check Key Vault
echo "🔐 Checking Key Vault..."
KEY_VAULT_NAME="kv-kraken-${ENVIRONMENT}-001"
if az keyvault show --name $KEY_VAULT_NAME --resource-group $RESOURCE_GROUP &>/dev/null; then
    echo "✅ Key Vault exists: $KEY_VAULT_NAME"
    
    # Check required secrets
    SECRETS=("CosmosDbPrimaryKey" "ApplicationInsightsConnectionString")
    for secret in "${SECRETS[@]}"; do
        if az keyvault secret show --vault-name $KEY_VAULT_NAME --name $secret &>/dev/null; then
            echo "✅ Secret exists: $secret"
        else
            echo "⚠️ Secret missing: $secret"
        fi
    done
else
    echo "❌ Key Vault not found: $KEY_VAULT_NAME"
    exit 1
fi

# Check Application Insights
echo "📊 Checking Application Insights..."
APP_INSIGHTS_NAME="ai-kraken-${ENVIRONMENT}"
if az monitor app-insights component show --app $APP_INSIGHTS_NAME --resource-group $RESOURCE_GROUP &>/dev/null; then
    echo "✅ Application Insights exists: $APP_INSIGHTS_NAME"
else
    echo "❌ Application Insights not found: $APP_INSIGHTS_NAME"
    exit 1
fi

# Check SignalR Service
echo "📡 Checking SignalR Service..."
SIGNALR_NAME="signalr-kraken-${ENVIRONMENT}-001"
if az signalr show --name $SIGNALR_NAME --resource-group $RESOURCE_GROUP &>/dev/null; then
    echo "✅ SignalR Service exists: $SIGNALR_NAME"
else
    echo "❌ SignalR Service not found: $SIGNALR_NAME"
    exit 1
fi

# Check Container Registry
echo "📦 Checking Container Registry..."
REGISTRY_NAME="krakenacr"
if az acr show --name $REGISTRY_NAME &>/dev/null; then
    echo "✅ Container Registry exists: $REGISTRY_NAME"
else
    echo "❌ Container Registry not found: $REGISTRY_NAME"
    exit 1
fi

echo ""
echo "🎉 Infrastructure validation completed!"
echo "✅ All required Azure services are properly configured for ${ENVIRONMENT} environment"
