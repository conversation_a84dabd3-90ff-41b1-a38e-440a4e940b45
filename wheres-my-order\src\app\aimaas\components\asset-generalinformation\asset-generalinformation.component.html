<section class="scrollsection"
         *ngIf="asset && chamberData">

    <p style="font-size: 16px;
    font-weight: 500;">ASSET INFO</p>
    <div style="display:flex ;flex-direction: row;">
        <div class="image"
             style="width: 40%; align-items: center;">

            <div *ngIf="imageUrl">
                <img [src]="imageUrl"
                     alt="Asset Image"
                     style="max-width: 100%; height: auto;" />
            </div>

            <div *ngIf="!imageUrl"
                 style="padding-left: 120px;">
                <p>No asset photo available</p>
            </div>
            <dx-load-panel shadingColor="rgba(0,0,0,0.4)"
                           [(visible)]="imageloadingVisible"
                           [showIndicator]="true"
                           [position]="{ of: '#top-container' }"
                           [showPane]="true"
                           [shading]="false"
                           [hideOnOutsideClick]="false">
            </dx-load-panel>
        </div>
        <div class="fields"
             style="width: 60%;">
            <div class="left-fields">
                <div class="field">
                    <label><b>Asset ID </b></label>
                    <span>{{asset?.assetid}}</span>
                </div>
                <div class="field">
                    <label><b>Description </b></label>
                    <span>{{asset?.description}}</span>
                </div>
                <div class="field">
                    <label><b>Service </b></label>
                    <span>{{asset?.service}}</span>
                </div>
                <div class="field">
                    <label><b>Equipment Category </b></label>
                    <span>{{asset?.equipcategory}}</span>
                </div>
                <div class="field">
                    <label><b>Item Type </b></label>
                    <span>{{asset?.assettype}}</span>
                </div>
                <div class="field">
                    <label><b>Orientation </b></label>
                    <span>{{asset?.orientation}}</span>
                </div>
                <div class="field">
                    <label><b>Construction Code </b></label>
                    <span>{{asset?.constructioncode}}</span>

                </div>
                <div class="field">
                    <label><b>Inspection Code </b></label>
                    <span>{{asset?.inspectioncode}}</span>

                </div>
            </div>
            <div class="right-fields">
                <div class="field">
                    <label><b>Manufacturer </b></label>
                    <span>{{asset?.manufacturer}}</span>
                </div>
                <div class="field">
                    <label><b>Construction Year </b></label>
                    <span>{{constructionYearValue(asset?.constructionyear)}}</span>

                </div>
                <div class="field">
                    <label><b>Dimensions </b></label>
                    <span>{{convertHtmlToText(asset?.dimensions)}}</span>

                </div>
                <div class="field">
                    <label><b>Serial Number </b></label>
                    <span>{{asset?.serialnumber}}</span>

                </div>
                <div class="field">
                    <label><b>National Board </b></label>
                    <span>{{convertHtmlToText(asset?.nationalboard)}}</span>

                </div>
                <div class="field">
                    <label><b>Local Juridictional </b></label>
                    <span>{{convertHtmlToText(asset?.localjuridictional)}}</span>

                </div>
                <div class="field">
                    <label><b>CMMS Number</b></label>
                    <span>{{convertHtmlToText(asset?.cmms)}}</span>

                </div>
                <div class="field">
                    <label><b>P&ID </b></label>
                    <span>{{convertHtmlToText(asset?.pid)}}</span>

                </div>
                <div class="field">
                    <label><b>Digital Twin Link </b></label>

                    <span [innerHTML]="asset?.link"
                          target="_blank"></span>

                </div>
            </div>
        </div>
    </div>
    <p style="font-size: 16px;
    font-weight: 500;">CHAMBER DATA</p>
    <div class="fields"
         *ngFor="let chamber of chamberData">
        <div>
            <div class="field">
                <label><b>Name</b></label>
                <span>{{ chamber.name }}</span>
            </div>
            <div class="field">
                <label><b>Design Pressure</b></label>
                <span>{{ removeNullValues(chamber.designpressure)
                    }}</span>
            </div>
            <div class="field">
                <label><b>Full Vacuum</b></label>
                <span>{{ chamber.fullvaccum }}</span>
            </div>
            <div class="field">
                <label><b>Min Temp</b></label>
                <span>{{ removeNullValues (chamber.mintemp )}}</span>
            </div>
            <div class="field">
                <label><b>Design Temp</b></label>
                <span>{{ removeNullValues(chamber.designtemp) }}</span>
            </div>
            <div class="field">
                <label><b>Fluid</b></label>
                <span>{{ chamber.fluid }}</span>
            </div>

            <!-- <hr> -->
            <br>
            <br>
        </div>
    </div>

</section>
<section *ngIf="chamberData?.length == 0"
         style="padding-left: 500px;">
    <p> No Data</p>
</section>
<dx-load-panel #loadPanel
               shadingColor="rgba(0,0,0,0.4)"
               [position]="{ of: '#top-container' }"
               [(visible)]="loadingVisible"
               [showIndicator]="true"
               [showPane]="true"
               [shading]="false"
               [hideOnOutsideClick]="false">
</dx-load-panel>