import moment from 'moment';
import { AssetCategoryAPICode } from '..';
import { isNullOrUndefined } from '../../../shared/helpers';
import {
    InspectionStatusBreakdown,
    ProjectVm,
    StatusesByBin,
    TaskVM,
    WorkTypePercentages
} from '../view-models';
import {
    CosmosAsset,
    CosmosAttribute,
    CosmosProject,
    CosmosTask,
    CosmosWorkOrder,
    ListChangeLog,
    ProjectActivityItem,
    // Legacy Firebase types for backward compatibility
    FirebaseAsset,
    FirebaseAttribute,
    FirebaseProject,
    FirebaseTask,
    FirebaseWorkOrder
} from './models';

export function getCurrentEntries(listChangeLog: ListChangeLog) {
    const sorted = Object.values(listChangeLog).sort((a, b) => a.T - b.T);
    let allEntries = Array.from(new Set(sorted.map((x) => x.V)));
    const allCopy = [...allEntries];
    for (const entry of allCopy) {
        const lastMatch = sorted
            .filter((x) => x.V === entry)
            .reduce(
                (prev, curr) =>
                    prev === null || curr.T >= prev.T ? curr : prev,
                null
            );
        if (lastMatch.A === 'Removed')
            allEntries = allEntries.filter((x) => x !== entry);
    }

    allEntries = allEntries.filter((x) => !isNullOrUndefined(x));

    return allEntries;
}

export function getCurrentValue<T>(attribute: CosmosAttribute<T>): T {
    if (!attribute || !attribute.ValueChangeLog) return attribute?.Value;

    const sorted = Object.values(attribute.ValueChangeLog).sort(
        (a, b) => a.T - b.T
    );
    const latest = sorted[sorted.length - 1];
    return latest ? latest.V : attribute.Value;
}

// Legacy Firebase helper functions (for backward compatibility)
export function getCurrentValueFirebase<T>(attribute: FirebaseAttribute<T>): T {
    return getCurrentValue(attribute as CosmosAttribute<T>);
}

// Additional helper functions for Azure Cosmos DB migration
export function convertFirebaseToCosmosAttribute<T>(firebaseAttr: FirebaseAttribute<T>): CosmosAttribute<T> {
    return firebaseAttr as CosmosAttribute<T>;
}

export function convertCosmosToFirebaseAttribute<T>(cosmosAttr: CosmosAttribute<T>): FirebaseAttribute<T> {
    return cosmosAttr as FirebaseAttribute<T>;
}

// Migrated from Firebase helpers for Azure compatibility
export function calculateAttributeValue<T>(attribute: CosmosAttribute<T> | FirebaseAttribute<T>) {
    return attribute
        ? attribute.Value ??
              (attribute.ValueChangeLog &&
              Object.keys(attribute.ValueChangeLog).length > 0
                  ? Object.values(attribute.ValueChangeLog).reduce(
                        (prev, curr) =>
                            prev.T > curr.T
                                ? prev
                                : curr
                    ).V
                  : undefined)
        : undefined;
}

// Simplified Aggregation namespace for Azure compatibility
export namespace Aggregation {
    export function getActivitySummary(
        projectIds: string[],
        projects: any[],
        tasks: any[]
    ): any {
        // Simplified implementation for Azure migration
        return {
            projects: projects.filter(p => projectIds.includes(p.id)),
            tasks: tasks.filter(t => projectIds.includes(t.ProjectId))
        };
    }

    export function getAssetCategoriesSummary(assets: any[], projectIds: string[]) {
        // Simplified implementation for Azure migration
        return assets.filter(a => projectIds.some(pid => a.projectIds?.includes(pid)));
    }

    export function getStatusesByMonth(projectIds: string[], tasks: any[], assets: any[]) {
        // Fixed signature to match expected parameters
        return {};
    }

    export function getStatusesByWeek(projectIds: string[], tasks: any[], assets: any[]) {
        // Fixed signature to match expected parameters
        return {};
    }

    export function getWorkTypePercentages(projectActivities: any[], taskActivities: any[]) {
        // Simplified implementation for Azure migration
        return {};
    }

    export function getSummaryInfo(projects: any[], projectIds: string[], tasks: any[], workOrders: any[]) {
        // Simplified implementation for Azure migration
        return {
            totalProjects: projects.filter(p => projectIds.includes(p.id)).length,
            totalTasks: tasks.filter(t => projectIds.includes(t.ProjectId)).length,
            totalWorkOrders: workOrders.filter(w => projectIds.includes(w.ProjectId)).length
        };
    }

    export function getInspectionStatuses(
        projectIds: string[],
        tasks: any[],
        assets: any[],
        workOrders: any[]
    ) {
        // Simplified implementation for Azure migration
        return {
            completed: 0,
            inProgress: 0,
            notStarted: 0
        };
    }

    export function getTimelineTasksAndProjects(
        projectIds: string[],
        tasks: any[],
        projects: any[],
        workOrders: any[]
    ) {
        // Simplified implementation for Azure migration
        return {
            tasks: tasks.filter(t => projectIds.includes(t.ProjectId)),
            projects: projects.filter(p => projectIds.includes(p.id))
        };
    }

    export function getAssetsByAreaAndType(projectIds: string[], assets: any[]) {
        // Simplified implementation for Azure migration
        return assets.filter(a => projectIds.some(pid => a.projectIds?.includes(pid)));
    }

    export function createInspectionsWithoutDueDates(
        projectIds: string[],
        tasks: any[],
        assets: any[]
    ) {
        // Simplified implementation for Azure migration
        return tasks.filter(t =>
            projectIds.includes(t.ProjectId) &&
            (!t.dueDate || t.dueDate === null)
        );
    }
}
