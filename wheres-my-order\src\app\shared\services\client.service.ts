import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { combineLatest, Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { UserProfile } from '../../profile/models';
import { UsersService } from './users.service';

export type Client = {
    clientid: string;
    clientname: string;
};

export namespace Client {
    export function displayExpr(client: Client): string {
        return client ? `${client.clientid} - ${client.clientname}` : '';
    }

    export function valueExpr(client: Client): string | null {
        return client ? client.clientname : null;
    }
}

@Injectable({
    providedIn: 'root'
})
export class ClientService {
    constructor(
        private readonly _http: HttpClient,
        private readonly _users: UsersService
    ) {}

    getClientNames(filterByPermissions = true): Observable<string[]> {
        return this.getClients(filterByPermissions).pipe(
            map((clients) => clients.map((client) => client.clientname))
        );
    }

    getClients(filterByPermissions = true): Observable<Client[]> {
        return combineLatest([
            // Go get our districts from the assets folder
            this._http.get<Client[]>('assets/data/clients.json'),
            // Grab the current user profile that is signed in
            this._users.currentProfile$
        ]).pipe(
            // Map districts and current user to the districts that the current user has access to.
            map(([clients, currentUser]) =>
                filterByPermissions
                    ? this.filterClientsForUser(clients, currentUser)
                    : clients
            )
        );
    }

    private filterClientsForUser(
        clients: Client[],
        currentUser: UserProfile
    ): { clientid: string; clientname: string }[] {
        return clients.filter(
            (client) =>
                currentUser.roles
                    .map((r) => r.toLowerCase().trim())
                    .some((r) => r === 'app:admin' || r === 'wmo:assigner') ||
                currentUser.customerAccounts.includes(client.clientname)
        );
    }
}
