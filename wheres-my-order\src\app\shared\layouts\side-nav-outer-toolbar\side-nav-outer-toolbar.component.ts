import {
    AfterViewInit,
    Component,
    ElementRef,
    OnInit,
    Renderer2,
    ViewChild
} from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { DxScrollViewComponent } from 'devextreme-angular';
import { Observable } from 'rxjs';
import { filter, map, switchMap, tap } from 'rxjs/operators';
import { CPDxTreeViewItem } from '../../../app-navigation';
import { debug } from '../../../core/operators';
import { OnlineOfflineNotificationService } from '../../../core/services';
import { AuthService, NavigationService, ScreenService } from '../../services';
import { ScrollService } from '../../services/scroll.service';

@Component({
    selector: 'app-side-nav-outer-toolbar',
    templateUrl: './side-nav-outer-toolbar.component.html',
    styleUrls: ['./side-nav-outer-toolbar.component.scss']
})
export class SideNavOuterToolbarComponent implements OnInit, AfterViewInit {
    @ViewChild('sideNavMenu', { read: ElementRef }) set adjustResponseModal(
        content: ElementRef
    ) {
        this.collapseSidebar(content);
    }
    onlineOfflineStatus$: Observable<boolean>;

    menuItems: CPDxTreeViewItem[];
    selectedRoute$: Observable<string>;

    menuOpened: boolean;
    temporaryMenuOpened = false;

    title: string;

    menuMode = 'shrink';
    menuRevealMode = 'expand';
    minMenuSize = 0;
    shaderEnabled = false;
    closeOnOutsideClick = false;
    @ViewChild('scrollview') scrollView: DxScrollViewComponent;

    ngAfterViewInit() {
        this._scroll.scrollViewRef = this.scrollView;
    }

    constructor(
        private _screen: ScreenService,
        protected renderer: Renderer2,
        private _router: Router,
        private _navigation: NavigationService,
        private _auth: AuthService,
        private readonly _onlineOfflineService: OnlineOfflineNotificationService,
        private _scroll: ScrollService
    ) {
        this.onlineOfflineStatus$ = this._onlineOfflineService.isOnline$;
    }

    ngOnInit() {
        this.menuItems = [
            {
                text: 'Loading...',
                icon: 'fa fa-circle-o-notch fa-spin',
                path: '/'
            }
        ];

        this.selectedRoute$ = this._router.events.pipe(
            filter<NavigationEnd>((event) => event instanceof NavigationEnd),
            debug('NAVIGATION'),
            map((event) => event.urlAfterRedirects)
        );

        this._auth.finishedInteraction$
            .pipe(
                switchMap((res) => {
                    return this._navigation.getMenuItems().pipe(
                        tap((navigation) => {
                            this.menuItems = navigation;
                        })
                    );
                })
            )
            .subscribe();

        this.menuOpened = this._screen.sizes['screen-large'];

        this._screen.changed.subscribe(() => this.updateDrawer());

        this.updateDrawer();
    }

    updateDrawer() {
        const isXSmall = this._screen.sizes['screen-x-small'];
        const isLarge = this._screen.sizes['screen-large'];

        this.menuMode = isLarge ? 'shrink' : 'overlap';
        this.menuRevealMode = isXSmall ? 'slide' : 'expand';
        this.minMenuSize = isXSmall ? 0 : 60;
        this.shaderEnabled = !isLarge;
        this.closeOnOutsideClick = !isLarge;
    }

    get hideMenuAfterNavigation() {
        return this.menuMode === 'overlap' || this.temporaryMenuOpened;
    }

    get showMenuAfterClick() {
        return !this.menuOpened;
    }

    navigationChanged(event) {
        const items = event.itemData.items;
        if (items && items.some((_) => true)) {
            this.temporaryMenuOpened = true;
            this.menuOpened = true;
        } else {
            const path = event.itemData.path;
            const pointerEvent = event.event;
            this.menuOpened = true;
            if (path) {
                if (event.node.selected) {
                    pointerEvent.preventDefault();
                } else {
                    this._router.navigate([path]);
                }

                if (this.hideMenuAfterNavigation) {
                    this.temporaryMenuOpened = false;
                    this.menuOpened = false;
                    pointerEvent.stopPropagation();
                }
            } else {
                pointerEvent.preventDefault();
            }
        }
    }

    navigationClick() {
        if (this.showMenuAfterClick) {
            this.temporaryMenuOpened = true;
            this.menuOpened = true;
        }
    }

    // Collapses sidebar menu when clicking is not on an option.
    private collapseSidebar(content: ElementRef): void {
        if (content) {
            this.renderer.listen(content.nativeElement, 'click', (evt) => {
                // if clicks is not on an option but on sidebar
                if (
                    evt.target ===
                    content.nativeElement.querySelector(
                        '.dx-scrollable-content'
                    )
                ) {
                    this.menuOpened = !this.menuOpened;
                }
            });
        }
    }
}
