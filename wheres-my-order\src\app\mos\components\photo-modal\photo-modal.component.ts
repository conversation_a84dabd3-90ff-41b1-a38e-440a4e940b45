import {
    Component,
    EventEmitter,
    Input,
    Output,
    SimpleChanges,
    ViewChild
} from '@angular/core';
import { DxValidationGroupComponent } from 'devextreme-angular';
import { WebcamImage, WebcamInitError, WebcamUtil } from 'ngx-webcam';
import { Observable, Subject } from 'rxjs';
import { PhotoModal } from '../../models/photo-modal';

@Component({
    selector: 'app-photo-modal',
    templateUrl: './photo-modal.component.html',
    styleUrls: ['./photo-modal.component.scss']
})
export class PhotoModalComponent {
    tracks: MediaStreamTrack[];
    canClose: boolean;
    constructor() {}

    showCamera: boolean;
    attachment_photo: PhotoModal = new PhotoModal();
    @Input() isBeingEdited: boolean;
    @Input() editCounter: number;
    @Input() allAttachments: PhotoModal[];
    counter: number = 1;

    value: any[] = [];
    imageDescriptions: string[] = [];
    imageText: string;
    fileObject: File = null;

    @Output() counterChange = new EventEmitter<number>();

    @Input() attachments: PhotoModal[] = [];
    @Output() filesChange = new EventEmitter<PhotoModal[]>();

    output_photo: PhotoModal = new PhotoModal();
    @Output() output_photoFile = new EventEmitter<PhotoModal>();

    public showWebcam = false;
    public allowCameraSwitch = true;
    public multipleWebcamsAvailable = false;
    public deviceId: string;
    public videoOptions: MediaTrackConstraints = {
        // width: {ideal: 1024},
        // height: {ideal: 576}
    };
    public errors: WebcamInitError[] = [];

    // latest snapshot
    public webcamImage: WebcamImage = null;

    // webcam snapshot trigger
    private trigger: Subject<void> = new Subject<void>();
    // switch to next / previous / specific webcam; true/false: forward/backwards, string: deviceId
    private nextWebcam: Subject<boolean | string> = new Subject<
        boolean | string
    >();

    showCancelButton: boolean = false;
    webcamEnabled: boolean = false;

    // eslint-disable-next-line @angular-eslint/use-lifecycle-interface
    public ngOnInit(): void {
        WebcamUtil.getAvailableVideoInputs().then(
            (mediaDevices: MediaDeviceInfo[]) => {
                this.multipleWebcamsAvailable =
                    mediaDevices && mediaDevices.length > 1;
            }
        );

        this.loadingVisible = true;
    }

    captureText: string = 'Capture';

    public triggerSnapshot(): void {
        this.trigger.next();
        this.toggleWebcam();

        if (this.captureText === 'Capture') {
            this.captureText = 'Retake';
        } else if (this.captureText === 'Retake') {
            this.showCancelButton = false;
            this.webcamImage = null;
            this.captureText = 'Capture';
            this.loadingVisible = true;
            setTimeout(() => (this.loadingVisible = false), 500);
            setTimeout(() => (this.showCancelButton = true), 2500); //temporary solution to mitigate the error of camera staying open even after it is closed.
        }
    }

    public toggleWebcam(): void {
        this.showWebcam = !this.showWebcam;

        // MediaStream.getTracks().forEach((track) => track.stop);
    }

    validationText: string;
    validationTextList: string[] = [
        'File Name is required',
        'File Name is already taken. Please type in another name.'
    ];

    public handleInitError(error: WebcamInitError): void {
        this.errors.push(error);
    }

    public showNextWebcam(directionOrDeviceId: boolean | string): void {
        // true => move forward through devices
        // false => move backwards through devices
        // string => move to device with given deviceId
        this.nextWebcam.next(directionOrDeviceId);
    }

    public handleImage(webcamImage: WebcamImage): void {
        console.info('received webcam image', webcamImage);
        this.webcamImage = webcamImage;
    }

    public cameraWasSwitched(deviceId: string): void {

        this.deviceId = deviceId;
        // let constraints = {
        //     deviceId: deviceId,
        //     width: this.videoOptions.width,
        //     height: this.videoOptions.height
        // };
        // navigator.mediaDevices
        //     .getUserMedia({ video: constraints })
        //     .then((stream) => {
        //         // this.onTransmission.emit(stream);
        //         this.tracks = stream.getVideoTracks();
        //         console.log(this.tracks);
        //     });
        // this.canClose = true;
    }

    public get triggerObservable(): Observable<void> {
        return this.trigger.asObservable();
    }

    public get nextWebcamObservable(): Observable<boolean | string> {
        return this.nextWebcam.asObservable();
    }

    public dataURLtoFile(dataurl, filename) {
        var arr = dataurl.split(','),
            mime = arr[0].match(/:(.*?);/)[1],
            bstr = atob(arr[1]),
            n = bstr.length,
            u8arr = new Uint8Array(n);
        while (n--) {
            u8arr[n] = bstr.charCodeAt(n);
        }
        return new File([u8arr], filename, { type: mime });
    }

    ifDuplicate: boolean = false;

    loadingVisible = false;

    public addImage(image: WebcamImage) {
        this.fileObject = this.dataURLtoFile(
            image.imageAsDataUrl,
            this.attachment_photo.name
        );

        this.attachment_photo.name = this.fileObject.name;
        this.attachment_photo.size = this.fileObject.size;
        this.attachment_photo.type = this.fileObject.type;
        this.attachment_photo.image = image;

        if (this.isBeingEdited) {
            this.attachments.splice(this.editCounter, 1, this.attachment_photo);
            this.isBeingEdited = false;
            this.value[this.editCounter] = this.fileObject;
        } else {
            this.attachments.push(this.attachment_photo);
            this.value.push(this.fileObject);
        }

        this.output_photo = this.attachment_photo;
        this.attachment_photo = new PhotoModal();
        this.fileObject = null;
    }

    public openCameraSelected() {
        this.fileNameGroup.instance.reset();
        this.showCamera = !this.showCamera;
        this.showWebcam = true;
        this.loadingVisible = true;
        this.captureText = 'Capture';
        this.validationText = null;
        setTimeout(() => (this.loadingVisible = false), 500);
        setTimeout(() => (this.showCancelButton = true), 2500); //temporary solution to mitigate the error of camera staying open even after it is closed.
    }

    @ViewChild('fileNameGroup')
    fileNameGroup: DxValidationGroupComponent;

    public outputAttachments(value: PhotoModal[]) {
        this.filesChange.emit(value);
    }

    // eslint-disable-next-line @angular-eslint/use-lifecycle-interface
    ngOnChanges(changes: SimpleChanges): void {
        if (changes.isBeingEdited) {
            this.isBeingEdited = changes.isBeingEdited.currentValue;
        }

        if (changes.editCounter) {
            this.editCounter = changes.editCounter.currentValue;
        }

        if (changes.allAttachments) {
            this.allAttachments = changes.allAttachments.currentValue;
        }

        if (this.isBeingEdited) {
            this.showCamera = !this.showCamera;
            this.webcamImage = this.attachments[this.editCounter].image;
            this.showWebcam = false;

            this.attachment_photo.name =
                this.attachments[this.editCounter].name;
            this.attachment_photo.description =
                this.attachments[this.editCounter].description;

            this.showCancelButton = true;
        }
    }

    // when you press cancel button
    onPopupClosed() {
        this.errors = [];

        this.attachment_photo = new PhotoModal();
        this.fileObject = null;
        this.webcamImage = null;
        this.showCancelButton = false;

        // this.tracks?.forEach((track) => track.stop());
        // this.tracks.forEach((track) => track.stop());

        if (this.isBeingEdited) {
            this.isBeingEdited = false;
            this.outputAttachments(this.attachments);
        }

        if (this.showWebcam) {
            this.toggleWebcam();
        }
    }

    // when you press OK button
    public closePopup(): void {
        this.errors = [];
        if (this.showCamera && this.fileNameGroup.instance.validate().isValid) {
            if (this.webcamImage != null) {
                this.addImage(this.webcamImage);

                if (this.ifDuplicate === false) {
                    this.toggleWebcam();
                    this.imageDescriptions.push(this.imageText);
                    this.imageText = null;
                    this.webcamImage = null;

                    this.output_photoFile.emit(this.output_photo);
                    this.outputAttachments(this.attachments);
                    this.showCancelButton = false;
                }
            }
            if (this.ifDuplicate === false) {
                this.showCamera = false;
            }
        } else if (this.showCamera == false) {
            this.showCamera = true;
            this.showCancelButton = false;
            this.toggleWebcam();
        }
    }

    checkDuplicate = (e) => {
        let duplicateName = false;

        if (this.allAttachments) {
            for (let i = 0, len = this.allAttachments.length; i < len; i++) {
                if (e.value === this.allAttachments[i].name) {
                    if (this.isBeingEdited != true) {
                        duplicateName = true;
                    } else if (this.isBeingEdited && this.showCamera !== true) {
                        if (i != this.editCounter) {
                            duplicateName = true;
                        }
                    }
                }
            }
        }

        this.ifDuplicate = duplicateName;

        return duplicateName != true;
    };
}
