import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import {
    BehaviorSubject,
    firstValueFrom,
    from,
    lastValueFrom,
    Observable
} from 'rxjs';
import { filter, shareReplay, switchMap, take, tap } from 'rxjs/operators';
import packageInfo from '../../../../package.json';
import { environment } from '../../../environments/environment';
import { debug } from '../../core/operators';
import { UserProfile } from '../../profile/models';
import { AuthService } from './auth.service';

@Injectable({
    providedIn: 'root'
})
export class UsersService {
    private _currentProfile$ = new BehaviorSubject<UserProfile>(undefined);
    private _currentProfile: UserProfile | undefined;

    constructor(
        private readonly _httpService: HttpClient,
        private readonly _auth: AuthService,
        private readonly _toasts: ToastrService,
        private readonly _router: Router
    ) {
        this._auth.finishedInteraction$.subscribe(async (_) => {
            this._auth.checkAndSetActiveAccount();
            if (this._auth?.user) {
                const user = await lastValueFrom(
                    this.get(this._auth?.user?.username)
                );
                if (!user.roles || user.roles.length <= 0) {
                    this._router.navigate(['/request-access']);
                }

                this._currentProfile = user;

                this._currentProfile$.next(this._currentProfile);
            }
        });

        this._auth.acquireTokenSuccess$
            .pipe(
                filter((result) => !result.fromCache),
                tap(() => {
                    this.updateUserLastLoginDate();
                })
            )
            .subscribe();

        this.currentProfile$
            .pipe(
                filter(
                    (user) =>
                        user.lastClientPortalVersion !== packageInfo.version
                ),
                switchMap((user) =>
                    this._toasts
                        .info(
                            `Release ${packageInfo.version} is now live. Click here to visit the About page to see what\'s new.`,
                            'New Version!',
                            { disableTimeOut: true, closeButton: true }
                        )
                        .onTap.pipe(take(1))
                ),
                tap(() => this._router.navigate(['/about']))
            )
            .subscribe();
    }

    get currentProfile$() {
        return this._currentProfile$.asObservable().pipe(filter(Boolean));
    }

    get(id: string): Observable<UserProfile> {
        return this._httpService
            .get<UserProfile>(
                `${environment.api.url}/users/${id?.toLowerCase()}/`
            )
            .pipe(debug('UsersService.get'));
    }

    delete(userProfileId: string): Observable<any> {
        return this._httpService.delete<UserProfile>(
            `${environment.api.url}/users/${userProfileId}`
        );
    }

    update(userProfile: UserProfile, originalId: string): Observable<any> {
        const httpCall = this._httpService.put<UserProfile>(
            `${environment.api.url}/users/${originalId}`,
            userProfile
        );
        const updateOperation = lastValueFrom(httpCall)
            .then(() => {
                if (originalId === this._currentProfile.id) {
                    return lastValueFrom(this.get(originalId));
                }
            })
            .then((freshUser) => {
                if (freshUser) {
                    this._currentProfile = freshUser;
                    this._currentProfile$.next(freshUser);
                    this._router.navigate([this._router.url]);
                }
            });

        return from(updateOperation);
    }

    getAll(): Observable<UserProfile[]> {
        return this._httpService
            .get<UserProfile[]>(`${environment.api.url}/users`)
            .pipe(debug('get all users'), shareReplay());
    }

    sendVerificationEmail(userId: string): Observable<any> {
        return this._httpService.put<UserProfile>(
            `${environment.api.url}/users/send-verification/${userId}`,
            null
        );
    }

    verify(userId: string, token: string): Observable<any> {
        return this._httpService.put<UserProfile>(
            `${environment.api.url}/users/${userId}/verify/${token}`,
            null
        );
    }

    async updateUserLastLoginDate() {
        const currentProfile = await firstValueFrom(this._currentProfile$);
        if (!currentProfile) return;
        currentProfile.lastLoginDate = new Date();
        await lastValueFrom(this.update(currentProfile, currentProfile.id));
        const freshUser = await lastValueFrom(this.get(currentProfile.id));
        this._currentProfile = freshUser;
        this._currentProfile$.next(this._currentProfile);
    }

    async updateUserClientVersion() {
        if (this._currentProfile) {
            this._currentProfile.lastClientPortalVersion = packageInfo.version;
            await lastValueFrom(
                this.update(this._currentProfile, this._currentProfile.id)
            );
            const freshUser = await lastValueFrom(
                this.get(this._currentProfile.id)
            );
            this._currentProfile = freshUser;
            this._currentProfile$.next(this._currentProfile);
        }
    }
}
