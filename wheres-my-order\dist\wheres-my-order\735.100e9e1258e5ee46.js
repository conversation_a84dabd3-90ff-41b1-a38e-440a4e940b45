"use strict";(self.webpackChunkwheres_my_order=self.webpackChunkwheres_my_order||[]).push([[735],{12735:(Ql,jt,L)=>{L.r(jt),L.d(jt,{AIMaaSModule:()=>Jl});var ut=L(36895),Sr=L(47936),wr=L(82995),yr=L(51679),Re=L(98350),Tr=L(47259),Ve=L(82559),Me=L(15861),$e=L(60305),ke=L(16714),Xt=L(17290),Er=L(11746),br=L(64313),Ar=L(94327),Dr=L(39841),Pr=L(11386),i=L(98274),Fr=L(51296),ht=L(47752),qt=L(63326),Ir=L(93720),Lr=L(39768),te=L(84943),pe=L(99106),Or=L(15697),Rr=L(52599),Mr=L(87074),Zr=L(94064),Hr=L(17763),Br=L(64564),Nr=L(72834),Gr=L(23103),Vr=L(7539),$r=L(14413),kr=L(80321),Ur=L(92531),Wr=L(38463),zr=L(76697),Jr=L(75344),Qr=L(25722),Kr=L(50954),Yr=L(30695),jr=L(23848);function Xr(e,a){if(1&e&&i._UZ(0,"app-breadcrumbs",49),2&e){const t=i.oxw();i.Q6J("crumbs",t.crumbs)}}function qr(e,a){if(1&e&&(i.ynx(0),i._UZ(1,"app-asset-generalinformation",51),i.BQk()),2&e){const t=i.oxw(2);i.xp6(1),i.Q6J("selectedAssetId",t.currentAssetId)("asset",t.currentAssetDetails)}}function en(e,a){if(1&e&&(i.TgZ(0,"div")(1,"dx-scroll-view",50),i.YNc(2,qr,2,2,"ng-container",17),i.qZA()()),2&e){const t=i.oxw();i.xp6(1),i.Q6J("height",550),i.xp6(1),i.Q6J("ngIf",t.assetDetailsPopupVisible)}}function tn(e,a){if(1&e&&(i.ynx(0),i._UZ(1,"app-inspection-schedule",52),i.BQk()),2&e){const t=i.oxw(2);i.xp6(1),i.Q6J("assetinspection",t.inspectionSchedule)}}function an(e,a){if(1&e&&(i.TgZ(0,"div")(1,"dx-scroll-view",50),i.YNc(2,tn,2,1,"ng-container",17),i.qZA()()),2&e){const t=i.oxw();i.xp6(1),i.Q6J("height",550),i.xp6(1),i.Q6J("ngIf",t.assetDetailsPopupVisible)}}function rn(e,a){if(1&e&&(i.TgZ(0,"div")(1,"dx-scroll-view",50),i._UZ(2,"app-asset-component-details",53),i.qZA()()),2&e){const t=i.oxw();i.xp6(1),i.Q6J("height",540),i.xp6(1),i.Q6J("assetComponent",t.assetComponentMap)}}function nn(e,a){if(1&e&&(i.ynx(0),i._UZ(1,"app-general-analysis",54),i.BQk()),2&e){const t=i.oxw(2);i.xp6(1),i.Q6J("assetId",t.currentAssetId)}}function ln(e,a){if(1&e&&(i.TgZ(0,"div")(1,"dx-scroll-view",50),i.YNc(2,nn,2,1,"ng-container",17),i.qZA()()),2&e){const t=i.oxw();i.xp6(1),i.Q6J("height",600),i.xp6(1),i.Q6J("ngIf",t.assetDetailsPopupVisible)}}function on(e,a){if(1&e&&(i.ynx(0),i._UZ(1,"app-attachments",55),i.BQk()),2&e){const t=i.oxw(2);i.xp6(1),i.Q6J("selectedAssetId",t.currentAssetId)}}function sn(e,a){if(1&e&&(i.TgZ(0,"div")(1,"dx-scroll-view",50),i.YNc(2,on,2,1,"ng-container",17),i.qZA()()),2&e){const t=i.oxw();i.xp6(1),i.Q6J("height",600),i.xp6(1),i.Q6J("ngIf",t.assetDetailsPopupVisible)}}function dn(e,a){if(1&e&&(i.ynx(0),i.TgZ(1,"dx-scroll-view",50),i._UZ(2,"app-submissions",55),i.qZA(),i.BQk()),2&e){const t=i.oxw(2);i.xp6(1),i.Q6J("height",600),i.xp6(1),i.Q6J("selectedAssetId",t.currentAssetId)}}function cn(e,a){if(1&e&&(i.TgZ(0,"div"),i.YNc(1,dn,3,2,"ng-container",17),i.qZA()),2&e){const t=i.oxw();i.xp6(1),i.Q6J("ngIf",t.assetDetailsPopupVisible)}}function un(e,a){if(1&e&&(i.TgZ(0,"div")(1,"dx-scroll-view",50),i._UZ(2,"app-general-inspection-details",56),i.qZA()()),2&e){const t=i.oxw();i.xp6(1),i.Q6J("height",600),i.xp6(1),i.Q6J("generalinspection",t.generalInspectionDetails)}}function hn(e,a){if(1&e&&(i.TgZ(0,"div")(1,"dx-scroll-view",50),i._UZ(2,"app-inspection-anomalies",57),i.qZA()()),2&e){const t=i.oxw();i.xp6(1),i.Q6J("height",600),i.xp6(1),i.Q6J("inspectionanomalies",t.visibleInspectionAnamolies)}}function vn(e,a){if(1&e&&(i.ynx(0),i._UZ(1,"app-corrosion-analysis",58),i.BQk()),2&e){const t=i.oxw(2);i.xp6(1),i.Q6J("operationId",t.selectedOperationId)}}function gn(e,a){if(1&e&&(i.TgZ(0,"div")(1,"dx-scroll-view",50),i.YNc(2,vn,2,1,"ng-container",17),i.qZA()()),2&e){const t=i.oxw();i.xp6(1),i.Q6J("height",600),i.xp6(1),i.Q6J("ngIf",t.popupVisible)}}function pn(e,a){if(1&e&&(i.ynx(0),i._UZ(1,"app-inspection-attachments",58),i.BQk()),2&e){const t=i.oxw(2);i.xp6(1),i.Q6J("operationId",t.selectedOperationId)}}function mn(e,a){if(1&e&&(i.TgZ(0,"div")(1,"dx-scroll-view",50),i.YNc(2,pn,2,1,"ng-container",17),i.qZA()()),2&e){const t=i.oxw();i.xp6(1),i.Q6J("height",600),i.xp6(1),i.Q6J("ngIf",t.popupVisible)}}function fn(e,a){if(1&e){const t=i.EpF();i.ynx(0),i.TgZ(1,"app-client-data-submission",59),i.NdJ("clientSubmissionTitleValueChange",function(n){i.CHM(t);const l=i.oxw();return i.KtG(l.clientSubmissionTitleValueChange(n))})("formSubmittedValueChange",function(n){i.CHM(t);const l=i.oxw();return i.KtG(l.clientDataFormSubmitted(n))}),i.qZA(),i.BQk()}if(2&e){const t=i.oxw();i.xp6(1),i.Q6J("initialAnomaly",t.initialAnomaly)}}function _n(e,a){if(1&e){const t=i.EpF();i.TgZ(0,"dx-button",62),i.NdJ("onClick",function(){i.CHM(t);const n=i.oxw(2);return i.KtG(n.clientSubmitDataOnclick("frombuttonclick"))}),i._uU(1," Action Center "),i.qZA()}}const Cn=function(){return["../drilldown"]},xn=function(){return["../inspection-drilldown"]};function Sn(e,a){if(1&e&&(i.TgZ(0,"div",60)(1,"dx-button",61),i._uU(2," Equipment List "),i.qZA(),i.TgZ(3,"dx-button",61),i._uU(4," Inspections List "),i.qZA(),i.YNc(5,_n,2,0,"dx-button",21),i.ALo(6,"hasRole"),i.ALo(7,"hasRole"),i.ALo(8,"hasRole"),i.ALo(9,"hasRole"),i.ALo(10,"hasRole"),i.qZA()),2&e){const t=i.oxw();i.xp6(1),i.Q6J("routerLink",i.DdM(18,Cn)),i.xp6(2),i.Q6J("routerLink",i.DdM(19,xn)),i.xp6(2),i.Q6J("ngIf",(null==t.availableSites?null:t.availableSites.length)>0&&(i.xi3(6,3,t.currentUser,"AIMaaS:Edit")||i.xi3(7,6,t.currentUser,"App:Admin")||i.xi3(8,9,t.currentUser,"AIMaaS:Admin")||i.xi3(9,12,t.currentUser,"AIMaaS:Demo")||i.xi3(10,15,t.currentUser,"AIMaaS:All")))}}function wn(e,a){if(1&e){const t=i.EpF();i.TgZ(0,"dx-button",62),i.NdJ("onClick",function(){i.CHM(t);const n=i.oxw();return i.KtG(n.onCloseTab())}),i._uU(1," Close tab "),i.qZA()}}function yn(e,a){if(1&e){const t=i.EpF();i.TgZ(0,"dx-select-box",63,64),i.NdJ("valueChange",function(n){i.CHM(t);const l=i.oxw();return i.KtG(l.selectedCostCentres=n)})("onValueChanged",function(n){i.CHM(t);const l=i.oxw();return i.KtG(l.onDistrictChange(n))}),i._UZ(2,"dxo-drop-down-options",65),i.qZA()}if(2&e){const t=i.oxw();i.Q6J("value",t.selectedCostCentres)("items",t.filteredDistrictOptions)("showSelectionControls",!1)("searchEnabled",!0)}}function Tn(e,a){if(1&e){const t=i.EpF();i.TgZ(0,"dx-select-box",66,67),i.NdJ("valueChange",function(n){i.CHM(t);const l=i.oxw();return i.KtG(l.selectedCompany=n)})("onValueChanged",function(n){i.CHM(t);const l=i.oxw();return i.KtG(l.onClientChange(n))}),i._UZ(2,"dxo-drop-down-options",68),i.qZA()}if(2&e){const t=i.oxw();i.Q6J("value",t.selectedCompany)("items",t.filteredClientOptions)("showSelectionControls",!1)("searchEnabled",!0)}}function En(e,a){if(1&e&&(i.TgZ(0,"div")(1,"div",72),i._uU(2),i.ALo(3,"siteLabel"),i.qZA()()),2&e){const t=a.$implicit;i.xp6(2),i.Oqu(i.lcZ(3,1,t))}}function bn(e,a){if(1&e){const t=i.EpF();i.TgZ(0,"dx-select-box",69,70),i.NdJ("valueChange",function(n){i.CHM(t);const l=i.oxw();return i.KtG(l.selectedSite=n)})("onSelectionChanged",function(n){i.CHM(t);const l=i.oxw();return i.KtG(l.changeSite(n))}),i.ALo(2,"siteLabel"),i._UZ(3,"dxo-drop-down-options",71),i.YNc(4,En,4,3,"div",47),i.qZA()}if(2&e){const t=i.oxw();i.Q6J("items",t.availableSites)("displayExpr",t.customDisplayExpr)("value",t.selectedSite)("hint",i.lcZ(2,7,t.selectedSite))("showClearButton",!1)("searchEnabled",!0),i.xp6(4),i.Q6J("dxTemplateOf","item")}}function An(e,a){if(1&e){const t=i.EpF();i.TgZ(0,"a",74),i.NdJ("click",function(){i.CHM(t);const n=i.oxw().$implicit,l=i.oxw();return i.KtG(l.assetSelectionChanged(n))}),i._uU(1),i.qZA()}if(2&e){const t=i.oxw().$implicit;i.xp6(1),i.Oqu(t.value)}}function Dn(e,a){if(1&e&&(i.TgZ(0,"div"),i.YNc(1,An,2,1,"a",73),i.qZA()),2&e){const t=a.$implicit;i.xp6(1),i.Q6J("ngIf",t&&t.value)}}function Pn(e,a){if(1&e){const t=i.EpF();i.TgZ(0,"a",74),i.NdJ("click",function(){i.CHM(t);const n=i.oxw().$implicit,l=i.oxw();return i.KtG(l.inspectionSelectionChanged(n))}),i._uU(1),i.qZA()}if(2&e){const t=i.oxw().$implicit;i.xp6(1),i.Oqu(t.value)}}function Fn(e,a){if(1&e&&(i.TgZ(0,"div"),i.YNc(1,Pn,2,1,"a",73),i.qZA()),2&e){const t=a.$implicit;i.xp6(1),i.Q6J("ngIf",t&&t.value)}}const In=function(){return{of:".gridload"}};function Ln(e,a){if(1&e){const t=i.EpF();i.TgZ(0,"div",75)(1,"dx-load-panel",76,77),i.NdJ("visibleChange",function(n){i.CHM(t);const l=i.oxw();return i.KtG(l.isLoading=n)}),i.qZA()()}if(2&e){const t=i.oxw();i.xp6(1),i.Q6J("position",i.DdM(6,In))("visible",t.isLoading)("showIndicator",!0)("showPane",!0)("shading",!0)("hideOnOutsideClick",!1)}}const On=function(){return[5,10,25,50]},Rn=function(e){return{icon:"fa fa-undo",hint:"Restore Grid Defaults",onClick:e}},Zn=[{path:"overview-dashboard",component:Ve.kR,data:{pageTitle:"Asset Integrity Hub"},canActivate:[Tr.OT]},{path:"dashboards",component:Ve._f,data:{pageTitle:"Asset Integrity Hub"}},{path:"",redirectTo:"",pathMatch:"full"},{path:"drilldown",component:Ve.j6,data:{pageTitle:"Assets"}},{path:"inspection-drilldown",component:Ve.N1,data:{pageTitle:"Inspections"}},{path:"anomaly-drilldown",component:(()=>{class e{constructor(t,r,n,l,h,s,d,f,v){var g=this;this.credoService=t,this._sitePipe=r,this._users=n,this._grid=l,this.location=h,this.route=s,this.router=d,this.indexedDbService=f,this.utilsService=v,this.storageKey="datagrid-state",this.popupVisible=!1,this.anomalyDataSource=new Xt.Z({store:[]}),this.currentFilter=[],this.assetAttachments=[],this.inspectionAttachments=[],this.componentAttachments=[],this.visibleInspectionAttachments=[],this.anomaliesData=[],this.submissionPopupVisible=!1,this.initialAnomaly=null,this.submissionPopupTitle="Anomaly Update",this.breaCrumbLabel="Recommendation List",this.isDataLoaded=!1,this.isHideButton=!1,this.assetDetailsPopupVisible=!1,this.equipmentPopupTitle=" ",this.inspectionPopupTitle=" ",this.assets=[],this.isFromOverview=!1,this.hideBreadcrumbs=!1,this.addUserPreferences=(x,A,_)=>{this.credoService?this.credoService.addPreference(x,A,_).subscribe(y=>{console.log("Preference added successfully:",y)},y=>{}):console.error("CredoService is undefined!")},this.saveState=x=>{this._users.currentProfile$.subscribe(A=>{this.userId=A.id}),this.addUserPreferences(this.userId,"equipment",JSON.stringify(x))},this.loadState=(0,Me.Z)(function*(){try{const x=yield g.getUserPreference();if(x&&x.equipment){const A=JSON.parse(x.equipment);return A.filterValue=g.currentFilter,A}return console.error("No anomaly preference found in response."),null}catch(x){return console.error("Error loading preference:",x),null}}),this.filteredValue=null,this.restoreAssetsDefaultsClicked=function(){var x=(0,Me.Z)(function*(A){(yield g._grid.resetGridState(g.grid))&&g.addUserPreferences(g.userId,"anomaly",JSON.stringify(""))});return function(A){return x.apply(this,arguments)}}(),this.anomalyCellTemplate=(x,A)=>{const _=A.value?A.value:"",y=document.createElement("a");y.href="#",y.innerText=_,y.onclick=V=>{V.preventDefault(),this.onAnomalyClick(A.data)},x.appendChild(y)},this.customDisplayExpr=x=>{if(x)return this._sitePipe.transform(x)}}ngAfterViewInit(){setTimeout(()=>{this.dataGrid&&this.anomalyDataSource&&this.refreshGrid()},0)}getUserPreference(){return this._users.currentProfile$.subscribe(t=>{this.userId=t.id}),new Promise((t,r)=>{this.credoService.getPreference(this.userId,"anomaly").subscribe(n=>{n&&n.equipment?t(n):r("No equipment preference found in response.")},n=>{r("Error fetching preference: "+n)})})}addUserPreference(t,r,n){this.credoService?this.credoService.addPreference(t,r,n).subscribe(l=>{},l=>{}):console.error("CredoService is undefined!")}ngOnInit(){var t=this;return(0,Me.Z)(function*(){if(window.location.hash.includes("fromOverview")){const r=window.location.href,n=decodeURIComponent(r.split("fromOverview=")[1]).split("locationId=")[1];try{t.breaCrumbLabel=decodeURIComponent(r.split("fromOverview=")[1]).split("?")[0];const h=t._users.currentProfile$;t.hideBreadcrumbs=!0,h.subscribe(y=>{t.currentUser=y});const s=JSON.parse(JSON.stringify(t.currentUser?.id)),d=yield t.indexedDbService.getItem(`anomalies_${s}`),f=yield t.indexedDbService.getItem(`assets_${s}`),v=yield t.indexedDbService.getItem(`inspections_${s}`),g=yield t.indexedDbService.getItem(`assetManagementSites_${s}`),x=yield t.indexedDbService.getItem(`clientLocationData_${s}`);t.assets=f||[],t.inspections=v||[],t.availableSites=g||[],t.clientLocationData=x||[],t.clientcostcentre=x||[],t.processLocations(t.clientcostcentre);const A=new Xt.Z({store:new ke.Z({data:d||[]})});t.anomalyDataSource=A;const _=t.currentUser.roles.map(y=>y.toLowerCase());_&&(_.includes("aimaas:demo")&&(t.availableSites=t.availableSites.filter(y=>y.locationid==Number(localStorage.getItem("selectedSite")))),history?.state?.data?.assetObjIds&&(t.currentFilter=history.state.data.assetObjIds.length<=0?["assetid","noneof",(yield t.anomalyDataSource.store().load())?.data?.map(y=>y.assetid)||[]]:["assetid","anyof",history.state.data.assetObjIds])),t.selectedSite=t.availableSites.find(y=>y.locationid==Number(localStorage.getItem(`selectedSite${n}`)))??t.availableSites[0],t.filterValue=JSON.parse(sessionStorage.getItem("currentFilter"))||[],setTimeout(()=>{t.anomalyDataSource.filter(y=>y.locationid===t.selectedSite.locationid),t.anomalyDataSource.load()},0),t.currentFilter=t.filterValue,t.isFromOverview=t.checkIfFromOverview(),t.dataGrid&&(t.dataGrid.instance.filter(t.currentFilter),yield t.dataGrid.instance.refresh())}catch(h){console.error("Error loading data source:",h)}const l=localStorage.getItem(`selectedSite${n}`);l&&(t.selectedSite=t.availableSites?.find(h=>h.locationid==Number(l))),t.isLoading=!1,t.isDataLoaded=!0}else(0,Dr.a)([t.credoService.getAllAnomaliesAsDataSource(),t.credoService.inspections$,t.credoService.assetManagementSites$,t._users.currentProfile$,t.credoService.anomalies$,t.credoService.assets$,t.credoService.clientLocationData$]).pipe().subscribe(function(){var r=(0,Me.Z)(function*([n,l,h,s,d,f,v]){t.anomalyDataSource=n,t.inspections=l,t.currentUser=s,t.anomaliesData=d,t.clientcostcentre=v,t.clientLocationData=v||[];const g=history.state;g&&g.data&&g.data.currentFilter?t.filterValue=g.data.currentFilter:window.location.hash.includes("fromOverview")&&(t.filterValue=JSON.parse(sessionStorage.getItem("currentFilter"))),t.availableSites=h,t.assets=f,t.userId=s.email;const x=s.roles.map(C=>C.toLowerCase());x&&(x.includes("aimaas:demo")?(t.availableSites=t.availableSites.filter(C=>C.locationid==Number("635140707384299520")),t.clientcostcentre=v?.filter(C=>C?.locationid==Number("635140707384299520"))):!x.includes("app:admin")&&!x.includes("aimaas:admin")&&!x.includes("aimaas:all")&&!x.includes("aimaas:district")&&!x.includes("aimaas:client")&&s.assetManagementSiteIds&&(t.availableSites=t.availableSites.filter(C=>s.assetManagementSiteIds.includes(C.locationid)),t.clientcostcentre=v.filter(C=>s.assetManagementSiteIds.includes(C?.locationid)))),t.processLocations(t.clientcostcentre);let A=localStorage.getItem("selectedDistrict");t.selectedCostCentres=A??null;let _=t.utilsService.getUniqueBy(t.districtToClientMap.get(A)||[],"id");t.districtToClientMap.get(A??null),!_.length&&t.districtvalues.length&&(_=t.utilsService.getUniqueBy(t.districtvalues,"id")),t.filteredClientOptions=[{id:"",name:""},..._.sort((C,o)=>C.name.localeCompare(o.name))];let y=localStorage.getItem("selectedClient");t.selectedCompany=y??null;let V=t.districtToSiteMap.get(y??null)||[];0===V.length&&(V=Array.from(t.districtToSiteMap.values()).flat()),t.availableSites=V.map(C=>({locationid:C.locationid,locationname:C.locationname,clientid:C.clientid?Number(C.clientid):0,clientname:C.clientname||""}));let N=localStorage.getItem("selectedSite"),U=t.availableSites.find(C=>C.locationid==Number(N));!U&&t.availableSites.length>0&&(U=t.availableSites[0],localStorage.setItem("selectedSite",String(U.locationid))),t.selectedSite=U,history?.state?.data?.assetObjIds&&(t.currentFilter=history.state.data.assetObjIds.length<=0?["assetid","noneof",(yield t.anomalyDataSource.store().load())?.data?.map(C=>C.assetid)||[]]:["assetid","anyof",history.state.data.assetObjIds]),t.isLoading=!1,t.isDataLoaded=!0});return function(n){return r.apply(this,arguments)}}());(window.location.hash.includes("fromOverview")||"overview"===history?.state?.source)&&(t.isHideButton=!0),t.isFromOverview=t.checkIfFromOverview(),setTimeout(()=>{history.state?.data?.currentFilter&&(t.currentFilter=history.state.data.currentFilter)},0),t.updateBreadcrumbs()})()}refreshGrid(){this.dataGrid&&this.anomalyDataSource&&Array.isArray(this.anomalyDataSource.items?.())&&(this.dataGrid.instance.clearFilter(),this.currentFilter&&this.dataGrid.instance.filter(this.currentFilter),this.dataGrid.instance.refresh())}checkIfFromOverview(){const r=window.location.hash.split("?")[1]||"";return new URLSearchParams(r).has("fromOverview")||"overview"===history.state?.source}processLocations(t){if(!t||!Array.isArray(t)||0===t.length)return;const r=new Map,n=new Map,l=new Map,h=new Map,s=new Map,d=new Map,f=new Map;t.forEach(_=>{if(_?.costcenterid&&_?.costcentername&&r.set(_.costcenterid,_.costcentername.trim()),_?.clientid&&_?.clientname&&n.set(_.clientid,_.clientname.trim()),_?.locationid&&_?.locationname&&l.set(_.locationid,_.locationname.trim()),_?.costcenterid&&_?.clientid&&_?.clientname){d.has(_.costcenterid)||d.set(_.costcenterid,new Set);let y=d.get(_.costcenterid);Array.from(y).some(V=>V.id===_.clientid)||y.add({id:_.clientid,name:_.clientname.trim()})}_?.clientid&&_?.costcenterid&&_?.costcentername&&(f.has(_.clientid)||f.set(_.clientid,new Set),f.get(_.clientid)?.add({id:_.costcenterid,name:_.costcentername.trim()})),_?.clientid&&_?.locationid&&_?.locationname&&(h.has(_.clientid)||h.set(_.clientid,new Set),h.get(_.clientid).add({locationid:_.locationid,locationname:_.locationname.trim()}))}),this.costCentreOptions=Array.from(r.entries()).map(([_,y])=>({id:_,name:y})).sort((_,y)=>_.name.localeCompare(y.name)),d.get(this.selectedCostCentres)?.forEach(({id:_,name:y})=>{s.set(_,y)}),this.districtvalues=Array.from(n.entries()).map(([_,y])=>({id:_,name:y})).sort((_,y)=>_.name.localeCompare(y.name)),this.locationOptions=Array.from(l.entries()).map(([_,y])=>({id:_,name:y})).sort((_,y)=>_.name.localeCompare(y.name)),this.districtToClientMap||(this.districtToClientMap=new Map),d.forEach((_,y)=>{this.districtToClientMap.set(y,Array.from(_))}),this.districtToSiteMap=new Map,h.forEach((_,y)=>{this.districtToSiteMap.set(y,Array.from(_))}),this.clientToDistrictMap||(this.clientToDistrictMap=new Map),f.forEach((_,y)=>{this.clientToDistrictMap.set(y,Array.from(_))}),this.filteredDistrictOptions=[{id:"",name:""},...this.utilsService.getUniqueBy(this.costCentreOptions.sort((_,y)=>_.name.localeCompare(y.name)),"id")],this.filteredClientOptions=[{id:"",name:""},...this.utilsService.getUniqueBy(this.districtvalues.sort((_,y)=>_.name.localeCompare(y.name)),"id")];const v=localStorage.getItem("selectedDistrict")||"",g=localStorage.getItem("selectedClient")||"";let x=[];x=v&&this.districtToClientMap.has(v)?this.utilsService.getUniqueBy(this.districtToClientMap.get(v)||[],"id"):this.utilsService.getUniqueBy(this.districtvalues,"id"),this.filteredClientOptions=[{id:"",name:""},...x.sort((_,y)=>_.name.localeCompare(y.name))];let A=[];A=g&&this.clientToDistrictMap.has(g)?this.utilsService.getUniqueBy(this.clientToDistrictMap.get(g)||[],"id"):this.utilsService.getUniqueBy(this.filteredDistrictOptions.filter(_=>""!==_.id).sort((_,y)=>_.name.localeCompare(y.name)),"id"),this.filteredDistrictOptions=[{id:"",name:""},...this.utilsService.getUniqueBy(A.sort((_,y)=>_.name.localeCompare(y.name)),"id")]}onDistrictChange(t){const r=t.value;if(this.selectedCostCentres=r,localStorage.setItem("selectedDistrict",r),r){const n=this.utilsService.getUniqueBy(Array.from(new Set(this.districtToClientMap.get(r)||[])),"id");this.filteredClientOptions=[{id:"",name:""},...n.sort((l,h)=>l.name.localeCompare(h.name))],setTimeout(()=>{this.selectedCompany||(this.selectedCompany=this.filteredClientOptions[0]?.id||null)})}else{const n=this.utilsService.getUniqueBy(Array.from(this.districtToClientMap.values()).flat(),"id");this.filteredClientOptions=[{id:"",name:""},...n.sort((l,h)=>l.name.localeCompare(h.name))]}localStorage.setItem("selectedClient",this.selectedCompany),this.filterSites()}onClientChange(t){const r=t.value;if(this.selectedCompany=r,localStorage.setItem("selectedClient",r),r){const n=this.utilsService.getUniqueBy(Array.from(new Set(this.clientToDistrictMap.get(r)||[])),"id");this.filteredDistrictOptions=[{id:"",name:""},...n.sort((l,h)=>l.name.localeCompare(h.name))]}else{const n=this.utilsService.getUniqueBy(Array.from(this.clientToDistrictMap.values()).flat(),"id");this.filteredDistrictOptions=[{id:"",name:""},...n.sort((l,h)=>l.name.localeCompare(h.name))]}this.filterSites()}filterSites(){const t=l=>null!=l&&""!==l&&"null"!==l&&"undefined"!==l,r=t(this.selectedCostCentres),n=t(this.selectedCompany);this.updateAvailableSites(r&&n?this.getFilteredSitesByBoth(this.selectedCostCentres,this.selectedCompany):r?this.getFilteredSitesByDistrict(this.selectedCostCentres):n?this.getFilteredSitesByClient(this.selectedCompany):Array.from(this.districtToSiteMap.values()).flat())}updateAvailableSites(t){this.availableSites=t,this.selectedSite=this.availableSites[0]||null,this.selectedSite&&localStorage.setItem("selectedSite",String(this.selectedSite.locationid))}getFilteredSitesByDistrict(t){return(this.districtToClientMap.get(t)||[]).map(n=>n.id).flatMap(n=>this.districtToSiteMap.get(n)||[])}getFilteredSitesByClient(t){return this.districtToSiteMap.get(t)||[]}getFilteredSitesByBoth(t,r){const n=this.districtToSiteMap.get(r)||[],l=this.clientLocationData.filter(h=>h.costcenterid===t&&h.clientid==r).map(h=>h.locationid);return n.filter(h=>l.includes(h.locationid))}updateBreadcrumbs(){let t="KPI Dashboards",r="/aimaas/dashboards",n=history.state?.source;if(!n){const h=window.location.hash.split("?")[1]||"";new URLSearchParams(h).has("fromOverview")&&(t="Overview Dashboard",r="/aimaas/overview-dashboard")}if("overview"===n&&(t="Overview Dashboard",r="/aimaas/overview-dashboard"),this.breaCrumbLabel=history.state?.breadCrumbLabel??"Recommendations List",window.location.hash.includes("fromOverview")){const l=sessionStorage.getItem("breadcrumbLabel");l&&(this.breaCrumbLabel=l)}this.crumbs=[{label:t,route:r},{label:this.breaCrumbLabel,route:"/aimaas/anomaly-drilldown"}]}clientSubmissionTitleValueChange(t){this.submissionPopupTitle=t}clientDataFormSubmitted(t){this.submissionPopupVisible=!1}clientSubmitDataOnclick(t){"frombuttonclick"===t&&(this.initialAnomaly=null),this.submissionPopupVisible=!this.submissionPopupVisible}onAnomalyClick(t){this.initialAnomaly=t,this.clientSubmitDataOnclick("fromanomaly")}onContentReady(t){this.grid?.instance?.endCustomLoading()}onCellPrepared(t){this.isLoading="data"!=t.rowType&&!this.isDataLoaded}getComponentsForAsset(t){return this.components?this.components.filter(r=>r.assetid===t):[]}getGeneralAnalysisForAsset(t){return this.generalAnalysis?this.generalAnalysis.filter(r=>r.assetid===t):[]}getInspectionsForAsset(t){return this.inspections?this.inspections?.filter(r=>r.assetid===t):[]}getSubmissionForAsset(t){return this.submissions?this.submissions?.filter(r=>r.assetId===t):[]}assetSelectionChanged(t){const r=t?.row?.data;window.location.hash.includes("fromOverview")||this.location.replaceState(`/aimaas/drilldown?assetid=${r?.assetid}`);const n=this.assets?.find(h=>h.id==r?.assetid);this.assetComponentMap=[],this.currentAssetId=String(n.id),this.credoService.getAllComponents(n.id).subscribe(h=>{this.assetComponentMap=h,this.assetComponentMap.sort((s,d)=>{const f=s.componentname.localeCompare(d.componentname);if(0!==f)return f})}),this.currentAssetDetails=n,this.inspectionSchedule=this.getInspectionsForAsset(String(n.id)),this.equipmentPopupTitle=`EQUIPMENT - ${n.assetid}`,this.assetDetailsPopupVisible=!0}closePopup(){this.popupVisible=!1,this.assetDetailsPopupVisible=!1,this.equipmentPopupTitle=" ",this.inspectionPopupTitle=" ",window.location.hash.includes("fromOverview")||this.location.replaceState("/aimaas/inspection-drilldown")}inspectionSelectionChanged(t){const r=t?.row?.data;let n;n=this.inspections?.find(l=>l?.planoperationid==r?.operationid),null!==n?.planoperationid&&(window.location.hash.includes("fromOverview")||this.location.replaceState(`/aimaas/inspection-drilldown?op=${n?.planoperationid}`),this.popupVisible=!0),this.inspectionPopupTitle=null!==n.operationtype?`INSPECTION - ${n?.assetidname} - ${n?.operationtype} - ${n?.date.replace(/-/g,"/")} `:`SCHEDULE - ${n?.assetidname} - ${n?.scheduletype} - ${n?.nextinspectiondue?n.nextinspectiondue.replace(/-/g,"/"):""}`,this.visibleInspectionAnamolies=this.anomaliesData.filter(l=>l.operationid==n.planoperationid),this.generalInspectionDetails=n,this.selectedOperationId=n.planoperationid}formatPID(t){return null==t.pid?" ":(new DOMParser).parseFromString(t.pid,"text/html").documentElement.textContent??" "}formatLocalJudictional(t){return null==t.localjuridictional?" ":(new DOMParser).parseFromString(t.localjuridictional,"text/html").documentElement.textContent??" "}onRowExpanding(t){Pr.g.handle(t)}changeSite(t){null==t.selectedItem?setTimeout(()=>{this.selectedSite=this.availableSites[0],localStorage.setItem("selectedSite",String(this.selectedSite.locationid)),localStorage.setItem("selectedClient",String(this.selectedCompany)),localStorage.setItem("selectedDistrict",String(this.selectedCostCentres))},1):(this.selectedSite=t.selectedItem,localStorage.setItem("selectedSite",String(this.selectedSite.locationid)),localStorage.setItem("selectedClient",String(this.selectedCompany)),localStorage.setItem("selectedDistrict",String(this.selectedCostCentres))),setTimeout(()=>{this.anomalyDataSource.filter(n=>n.locationid===this.selectedSite.locationid),this.anomalyDataSource.load()},0)}convertHtmlToText(t){return null==t?" ":(new DOMParser).parseFromString(t,"text/html").documentElement.textContent??" "}formatAnomalyDescription(t){return null==t.anomalydescription?" ":(new DOMParser).parseFromString(t.anomalydescription,"text/html").documentElement.textContent??" "}formatAnomalyProposedRecom(t){return null==t.proposedrecommemendation?" ":(new DOMParser).parseFromString(t.proposedrecommemendation,"text/html").documentElement.textContent??" "}onExporting(t){return(0,Me.Z)(function*(){const r=new br.Workbook,n=r.addWorksheet("Anomalies");yield(0,Er.d)({component:t.component,worksheet:n});const l=yield r.xlsx.writeBuffer();(0,Ar.saveAs)(new Blob([l],{type:"application/octet-stream"}),"Anomalies.xlsx")})()}onCloseTab(){window.close()}}return e.\u0275fac=function(t){return new(t||e)(i.Y36(Fr._),i.Y36(ht.L),i.Y36(qt.fz),i.Y36(qt.A6),i.Y36(ut.Ye),i.Y36(Re.gz),i.Y36(Re.F0),i.Y36(Ir.e),i.Y36(Lr.F))},e.\u0275cmp=i.Xpm({type:e,selectors:[["app-anomaly-drill-down"]],viewQuery:function(t,r){if(1&t&&(i.Gf($e.e,5),i.Gf($e.e,5)),2&t){let n;i.iGM(n=i.CRH())&&(r.grid=n.first),i.iGM(n=i.CRH())&&(r.dataGrid=n.first)}},decls:72,vars:69,consts:[[3,"crumbs",4,"ngIf"],[3,"visible","width","height","showTitle","title","dragEnabled","showCloseButton","visibleChange","onHiding"],[1,"tabs-demo"],[1,"widget-container"],["title","General Information"],[4,"dxTemplate"],["title","Inspection Schedule"],["title","Component Details"],["title","General Analysis"],["title","Asset Attachments"],["title","Submissions"],["title","LAST INSPECTION DETAILS"],["title","Inspection Anomalies"],["title","UT Readings"],["title","Inspection Attachments"],[3,"visible","width","height","showTitle","dragEnabled","showCloseButton","visibleChange"],["toolbar","top","location","center","locateInMenu","always",3,"text"],[4,"ngIf"],[1,"dx-card","content-block","responsive-paddings",2,"max-width","94vw"],[2,"display","flex","justify-content","space-between","align-items","center"],["style","margin-left: auto;",4,"ngIf"],["class","listpagebuttons",3,"onClick",4,"ngIf"],[1,"top-row"],["id","selectBox4","displayExpr","name","valueExpr","id","placeholder","Select District","style","width:370px; margin:2px",3,"value","items","showSelectionControls","searchEnabled","valueChange","onValueChanged",4,"ngIf"],["id","selectBox3","displayExpr","name","valueExpr","id","placeholder","Select Company","style","width:370px; margin:2px",3,"value","items","showSelectionControls","searchEnabled","valueChange","onValueChanged",4,"ngIf"],["id","selectBox","style","width:370px; margin:2px","itemTemplate","item","stylingMode","filled",3,"items","displayExpr","value","hint","showClearButton","searchEnabled","valueChange","onSelectionChanged",4,"ngIf"],["id","gridload",1,"gridload",3,"wordWrapEnabled","rowAlternationEnabled","allowColumnResizing","filterSyncEnabled","allowColumnReordering","dataSource","showColumnLines","showRowLines","showBorders","filterValue","filterValueChange","onCellPrepared","onExporting"],["dataGrid",""],[3,"enabled"],[3,"enabled","pageSize"],[3,"showPageSizeSelector","allowedPageSizes"],["name","groupPanel"],["widget","dxButton","location","after",3,"options"],["name","columnChooserButton"],["name","exportButton"],[3,"visible"],["type","custom","storageKey","equipment",3,"enabled","customLoad","customSave"],["mode","dragAndDrop","title","Column Chooser (Drag and Drop)",3,"enabled","width"],["dataField","assetname","caption","Asset Id",3,"cellTemplate"],["dataField","anomalypriority","caption","Anomaly Priority"],["dataField","anomalytype","caption","Anomaly Type"],["dataField","anomalydescription","caption","Description",3,"calculateCellValue","width"],["dataField","detectiondate","caption","Detection date","dataType","date",3,"showInColumnChooser","visible"],["dataField","proposedrecommemendation","caption","Proposed recommendation",3,"calculateCellValue","width"],["dataField","inspectionoperationinstance","caption","Inspection",3,"cellTemplate"],["dataField","resolutionstate","caption","Resolution State"],["dataField","anomaly","caption","Anomaly N.",3,"cellTemplate"],[4,"dxTemplate","dxTemplateOf"],["style","width: 100%; height: 100%;",4,"ngIf"],[3,"crumbs"],[3,"height"],[3,"selectedAssetId","asset"],[3,"assetinspection"],[3,"assetComponent"],[3,"assetId"],[3,"selectedAssetId"],[3,"generalinspection"],[3,"inspectionanomalies"],[3,"operationId"],[3,"initialAnomaly","clientSubmissionTitleValueChange","formSubmittedValueChange"],[2,"margin-left","auto"],[1,"listpagebuttons",3,"routerLink"],[1,"listpagebuttons",3,"onClick"],["id","selectBox4","displayExpr","name","valueExpr","id","placeholder","Select District",2,"width","370px","margin","2px",3,"value","items","showSelectionControls","searchEnabled","valueChange","onValueChanged"],["districtSelectionBox",""],["container","#selectBox4"],["id","selectBox3","displayExpr","name","valueExpr","id","placeholder","Select Company",2,"width","370px","margin","2px",3,"value","items","showSelectionControls","searchEnabled","valueChange","onValueChanged"],["oisClientSelectionBox",""],["container","#selectBox3"],["id","selectBox","itemTemplate","item","stylingMode","filled",2,"width","370px","margin","2px",3,"items","displayExpr","value","hint","showClearButton","searchEnabled","valueChange","onSelectionChanged"],["siteSelectionBox",""],["container","#selectBox"],[2,"display","inline-block"],["href","javascript://void",3,"click",4,"ngIf"],["href","javascript://void",3,"click"],[2,"width","100%","height","100%"],["shadingColor","rgba(0,0,0,0.4)",3,"position","visible","showIndicator","showPane","shading","hideOnOutsideClick","visibleChange"],["loadPanel",""]],template:function(t,r){1&t&&(i.YNc(0,Xr,1,1,"app-breadcrumbs",0),i.TgZ(1,"dx-popup",1),i.NdJ("visibleChange",function(l){return r.assetDetailsPopupVisible=l})("onHiding",function(){return r.closePopup()}),i.TgZ(2,"div",2)(3,"div",3)(4,"dx-tab-panel")(5,"dxi-item",4),i.YNc(6,en,3,2,"div",5),i.qZA(),i.TgZ(7,"dxi-item",6),i.YNc(8,an,3,2,"div",5),i.qZA(),i.TgZ(9,"dxi-item",7),i.YNc(10,rn,3,2,"div",5),i.qZA(),i.TgZ(11,"dxi-item",8),i.YNc(12,ln,3,2,"div",5),i.qZA(),i.TgZ(13,"dxi-item",9),i.YNc(14,sn,3,2,"div",5),i.qZA(),i.TgZ(15,"dxi-item",10),i.YNc(16,cn,2,1,"div",5),i.qZA()()()()(),i.TgZ(17,"dx-popup",1),i.NdJ("visibleChange",function(l){return r.popupVisible=l})("onHiding",function(){return r.closePopup()}),i.TgZ(18,"div",2)(19,"div",3)(20,"dx-tab-panel")(21,"dxi-item",11),i.YNc(22,un,3,2,"div",5),i.qZA(),i.TgZ(23,"dxi-item",12),i.YNc(24,hn,3,2,"div",5),i.qZA(),i.TgZ(25,"dxi-item",13),i.YNc(26,gn,3,2,"div",5),i.qZA(),i.TgZ(27,"dxi-item",14),i.YNc(28,mn,3,2,"div",5),i.qZA()()()()(),i.TgZ(29,"dx-popup",15),i.NdJ("visibleChange",function(l){return r.submissionPopupVisible=l}),i._UZ(30,"dxi-toolbar-item",16),i.YNc(31,fn,2,1,"ng-container",17),i.qZA(),i.TgZ(32,"div",18)(33,"div",19)(34,"h3"),i._uU(35),i.qZA(),i.YNc(36,Sn,11,20,"div",20),i.TgZ(37,"div"),i.YNc(38,wn,2,0,"dx-button",21),i.qZA()(),i.TgZ(39,"div",22),i.YNc(40,yn,3,4,"dx-select-box",23),i.YNc(41,Tn,3,4,"dx-select-box",24),i.qZA(),i.YNc(42,bn,5,9,"dx-select-box",25),i.TgZ(43,"div")(44,"dx-data-grid",26,27),i.NdJ("filterValueChange",function(l){return r.filterValue=l})("onCellPrepared",function(l){return r.onCellPrepared(l)})("onExporting",function(l){return r.onExporting(l)}),i._UZ(46,"dxo-load-panel",28)(47,"dxo-paging",29)(48,"dxo-pager",30),i.TgZ(49,"dxo-toolbar"),i._UZ(50,"dxi-item",31)(51,"dxi-item",32)(52,"dxi-item",33)(53,"dxi-item",34),i.qZA(),i._UZ(54,"dxo-header-filter",35)(55,"dxo-filter-panel",35)(56,"dxo-filter-row",35)(57,"dxo-export",28)(58,"dxo-state-storing",36)(59,"dxo-column-chooser",37)(60,"dxi-column",38)(61,"dxi-column",39)(62,"dxi-column",40)(63,"dxi-column",41)(64,"dxi-column",42)(65,"dxi-column",43)(66,"dxi-column",44)(67,"dxi-column",45)(68,"dxi-column",46),i.YNc(69,Dn,2,1,"div",47),i.YNc(70,Fn,2,1,"div",47),i.qZA(),i.YNc(71,Ln,3,7,"div",48),i.qZA()()),2&t&&(i.Q6J("ngIf",!r.hideBreadcrumbs),i.xp6(1),i.Q6J("visible",r.assetDetailsPopupVisible)("width",1200)("height",700)("showTitle",!0)("title",r.equipmentPopupTitle)("dragEnabled",!1)("showCloseButton",!0),i.xp6(16),i.Q6J("visible",r.popupVisible)("width",1200)("height",700)("showTitle",!0)("title",r.inspectionPopupTitle)("dragEnabled",!1)("showCloseButton",!0),i.xp6(12),i.Q6J("visible",r.submissionPopupVisible)("width",1200)("height",700)("showTitle",!0)("dragEnabled",!1)("showCloseButton",!0),i.xp6(1),i.Q6J("text",r.submissionPopupTitle),i.xp6(1),i.Q6J("ngIf",r.submissionPopupVisible),i.xp6(4),i.hij("Recommendations",r.isFromOverview?" - "+r.breaCrumbLabel:""," "),i.xp6(1),i.Q6J("ngIf",!r.isHideButton),i.xp6(2),i.Q6J("ngIf",r.isHideButton),i.xp6(2),i.Q6J("ngIf",r.availableSites&&!r.isFromOverview),i.xp6(1),i.Q6J("ngIf",r.availableSites&&!r.isFromOverview),i.xp6(1),i.Q6J("ngIf",r.availableSites),i.xp6(2),i.Q6J("wordWrapEnabled",!0)("rowAlternationEnabled",!0)("allowColumnResizing",!0)("filterSyncEnabled",!0)("allowColumnReordering",!0)("dataSource",r.anomalyDataSource)("showColumnLines",!1)("showRowLines",!0)("showBorders",!0)("filterValue",r.filterValue),i.xp6(2),i.Q6J("enabled",!0),i.xp6(1),i.Q6J("enabled",!0)("pageSize",10),i.xp6(1),i.Q6J("showPageSizeSelector",!0)("allowedPageSizes",i.DdM(66,On)),i.xp6(3),i.Q6J("options",i.VKq(67,Rn,r.restoreAssetsDefaultsClicked)),i.xp6(3),i.Q6J("visible",!0),i.xp6(1),i.Q6J("visible",!0),i.xp6(1),i.Q6J("visible",!0),i.xp6(1),i.Q6J("enabled",!0),i.xp6(1),i.Q6J("enabled",!0)("customLoad",r.loadState)("customSave",r.saveState),i.xp6(1),i.Q6J("enabled",!0)("width",310),i.xp6(1),i.Q6J("cellTemplate","assetdetails"),i.xp6(3),i.Q6J("calculateCellValue",r.formatAnomalyDescription)("width",200),i.xp6(1),i.Q6J("showInColumnChooser",!1)("visible",!1),i.xp6(1),i.Q6J("calculateCellValue",r.formatAnomalyProposedRecom)("width",200),i.xp6(1),i.Q6J("cellTemplate","inspectiondetails"),i.xp6(2),i.Q6J("cellTemplate",r.anomalyCellTemplate),i.xp6(1),i.Q6J("dxTemplateOf","assetdetails"),i.xp6(1),i.Q6J("dxTemplateOf","inspectiondetails"),i.xp6(1),i.Q6J("ngIf",r.isLoading))},dependencies:[ut.O5,Re.rH,te.Ak0,pe.p6,Or.K,te.k7e,te.QjJ,te.ZT3,$e.e,te.Auv,te.qvW,te.tZE,te.ecQ,te.I62,te.mKI,te.ilc,te.sXh,te.C9T,te.y1f,Rr._,Mr.N,Zr.I,Hr.Z,Br.x,Nr.n,Gr.t,Vr.w,$r.v,kr.i,Ur.j,Wr.r,zr.B,Jr.A,Qr.X,Kr.J,Yr.E,jr.$,ht.L],styles:[".dx-datagrid-filter-panel-text{white-space:normal!important}#large-indicator[_ngcontent-%COMP%]{position:absolute;top:40%;left:50%;transform:translate -50%,-50%;z-index:1000;pointer-events:none}.listpagebuttons[_ngcontent-%COMP%]{margin-left:5px}.top-row[_ngcontent-%COMP%]{display:flex;gap:.5px;width:100%}"]}),e})(),data:{pageTitle:"Recommendations"}}];let Hn=(()=>{class e{}return e.\u0275fac=function(t){return new(t||e)},e.\u0275mod=i.oAB({type:e}),e.\u0275inj=i.cJS({imports:[Re.Bz.forChild(Zn),Re.Bz]}),e})();var Bn=L(22284),Nn=L(68665),Gn=L(83108),ea=L(11481),Ue=L(67771),ta=L(37075),M=L(83520),me=L(56836),J=L(80316),X=L(83684),Z=L(91553),ae=L(21374),de=L(83626),H=L(64596),we=L(34500),vt=L(1037),Te=L(24705),Ze=L(16129),ie=L(40911),aa=L(40206),ra=L(20325),na=L(46068),gt=L(8880),ia=L(68082),Vn=L(68948),$n=L(7554),We=L(31996),ze=L(14422),Je=L(26372),pt=L(56821),Ce=function(e,a,t,r){var n=e._initProperties=e._initProperties||{},l=r?t:e[a];(!Object.prototype.hasOwnProperty.call(n,a)||r)&&"_initProperties"!==a&&(n[a]=l),e[a]=t},la=new Date;function oa(e){return function a(t,r,n,l,h,s){var d,f;function v(A,_,y){(0,J.gx)(a(t,r,n,l,_,y)).done(A.resolve)}for(l=l||[],t=t||[],d=h=h||0;d<t.length;d+=1){if(e&&d>h&&d%1e4==0&&new Date-la>=300)return la=new Date,v(f=new J.BH,d,!1),f;var g=t[d];if(!s){if(l.unshift(g),n&&!1===r(l,d))return;if(g.children){var x=a(g.children,r,n,l);if(e&&x)return f=new J.BH,x.done(v(f,d,!0)),f}}if(s=!1,!n&&!1===r(l,d))return;l.shift(),t[d]!==g&&(d-=1)}}}var ce=oa(!1),xe=oa(!0);function Ee(e,a){if(e&&(0,H.$K)(a))for(var t=0;t<e.length;t+=1){var r=e[t];if(r.name===a||r.caption===a||r.dataField===a||r.index===a)return t}return-1}function Qe(e,a){var r={value:e,valueText:e==e&&Je.Z.format(e,a.format)||""};return a.customizeText?a.customizeText.call(a,r):r.valueText}function Ke(e){return function(a,t){var r=0,n=e(a),l=e(t),h=(0,H.$K)(n),s=(0,H.$K)(l);return h&&s&&(n>l?r=1:n<l&&(r=-1)),h&&!s&&(r=1),!h&&s&&(r=-1),r}}function ge(e){for(var a=[],t=e.length-1;t>=0;t-=1)a.push(e[t].key||e[t].value);return a}function Ye(e,a,t,r){t=t||0,r=r||"children",e.length&&a(e,t);for(var n=0;n<e.length;n+=1){var l=e[n];l[r]&&l[r].length&&Ye(l[r],a,t+1,r)}}function sa(e,a){for(var t=[],r=0;r<e.length;r+=1)t.push(Math.max(e[r]||0,a[r]||0));return t}function je(e,a){var t=e[a],r=0;for(e.headerName===a?r=e.path.length:e.headerName&&e.headerName!==a&&e.oppositePath?r=e.oppositePath.length:(0,Z.S6)(("columns"===a?e.columnExpandedPaths:e.rowExpandedPaths)||[],(l,h)=>{r=Math.max(r,h.length)});t[r+1]&&t[r].expanded;)r+=1;return r}function da(e,a,t,r){var n=[];return Object.keys(a||[]).forEach(l=>{if(!l||!l.startsWith("__")){for(var g,h=1,s=t.length?"".concat(t,".").concat(l):l,d=r[s],f=(0,We.vb)(s),v=a[l];!(0,H.$K)(v)&&e[h];)v=f(e[h]),h+=1;!d&&(0,H.$K)(v)&&(d=(0,H.dt)(v)),g=[{dataField:s,dataType:d,groupName:"date"===d?l:void 0,groupInterval:void 0,displayFolder:t}],"date"===d?g=g.concat(function Wn(e){return(0,Z.UI)(["year","quarter","month"],(a,t)=>(0,X.l)({},e,{groupInterval:a,groupIndex:t}))}(g[0])):"object"===d&&(g=da(e,v,s,r)),n.push.apply(n,g)}}),n}function ca(e,a){var t=mt(a);return da(e,e[0],"",t)}function mt(e){var a={};return(0,Z.S6)(e,(t,r)=>{a[r.dataField]=a[r.dataField]||r.dataType}),a}var zn={month:e=>pt.Z.getMonthNames()[e-1],quarter:e=>pt.Z.format(new Date(2e3,3*e-1),"quarter"),dayOfWeek:e=>pt.Z.getDayNames()[e]};function ft(e){if("date"===e.dataType)e.format||Ce(e,"format",zn[e.groupInterval]);else if("number"===e.dataType){var a=(0,H.kE)(e.groupInterval)&&e.groupInterval>0&&e.groupInterval;a&&!e.customizeText&&Ce(e,"customizeText",t=>{var n=Je.Z.format(t.value+a,e.format);return t.valueText&&n?"".concat(t.valueText," - ").concat(n):""})}}function be(e,a){var t=[];a=a||[];for(var r=0;r<a.length;r+=1)t.push((0,X.l)({},e[r],{groupIndex:null,groupName:null,filterType:"include",filterValues:[a[r]]}));return t}var _t={createDrillDownDataSource(e,a){var t=this.getDrillDownItems(e,a);function r(l){return function(h){var s;return s=new J.BH,(0,J.gx)(t).done(d=>{new ke.Z(d)[l](h).done(s.resolve).fail(s.reject)}).fail(s.reject),s}}return new ze.o({load:r("load"),totalCount:r("totalCount"),key:this.key()})}};function Ct(e){return e.charAt(0).toUpperCase()+e.slice(1)}var ua=e=>e.offsetWidth-e.clientWidth,ha=(0,$n.Z)(()=>{var e=ia.Z.getDocument();e.body.insertAdjacentHTML("beforeend",'<div class="'.concat("dx-pivotgrid-scrollbar-measure-element",'"></div>'));var a=e.body.lastElementChild,t=ua(a);return a&&e.body.removeChild(a),t});const xt={setFieldProperty:Ce,sendRequest:function Un(e){return Vn.Z.sendRequest(e)},foreachTree:ce,foreachTreeAsync:xe,findField:Ee,formatValue:Qe,getCompareFunction:Ke,createPath:ge,foreachDataLevel:Ye,mergeArraysByMaxValue:sa,getExpandedLevel:je,discoverObjectFields:ca,getFieldsDataType:mt,setDefaultFieldValueFormatting:ft,getFiltersByPath:be,storeDrillDownMixin:_t,capitalizeFirstLetter:Ct,getScrollbarWidth:ua,calculateScrollbarWidth:ha};var Jn={number:"numeric",date:"datetime"},St="dxPivotGridUnbinding";function va(e,a){var t=[],r=a.length-1;return(0,Z.S6)(e,(n,l)=>{t.push(l.text||Qe(l.value,a[r-n]))}),t.reverse()}function ga(e){var a=0;return ce(e,t=>{a=Math.max(a,t.length-1)}),a}function Yn(e,a){var t=e.getAreaFields("data");if("args"!==a.putDataFieldsInto&&"singleAxis"!==a.dataFieldsDisplayMode||1===t.length){var r=[];return(0,Z.S6)(t,(n,l)=>{var h={name:l.caption,title:l.caption,valueType:Jn[l.dataType]||l.dataType,label:{format:l.format}};l.customizeText&&(h.label.customizeText=function(s){return l.customizeText.call(l,s)}),"splitPanes"===a.dataFieldsDisplayMode&&(h.pane=l.caption),r.push(h)}),r}return[{}]}function jn(e,a){var t=[],r=e.getAreaFields("data");return r.length>1&&"splitPanes"===a.dataFieldsDisplayMode&&"args"!==a.putDataFieldsInto&&(0,Z.S6)(r,(n,l)=>{t.push({name:l.caption})}),t.length||t.push({}),t}var pa={bindChart(e,a){a=(0,X.l)({},a);var t=this,r=function(){a.grandTotalText=t.option("texts.grandTotal");var l=function Xn(e,a){var{customizeSeries:t}=a,{customizeChart:r}=a,n={valueAxis:Yn(e,a),panes:jn(e,a)},l={};return r&&(n=(0,X.l)(!0,{},n,r(n))),n.dataSource=function Kn(e,a,t){var x,A,_,y,V,N,U,C,o,u,r=e.getData(),n=[],l=e.getAreaFields("data"),h=e.getAreaFields("row"),s=e.getAreaFields("column"),d=[{index:r.grandTotalColumnIndex,children:r.columns}],f=[{index:r.grandTotalRowIndex,children:r.rows}],v=ga(f),g=ga(d);function c(){var T,S=(r.values[_]||[])[U]||[],E=S[x],b={rowPath:N,maxRowLevel:v,rowPathFormatted:V,rowFields:h,columnPathFormatted:u,maxColumnLevel:g,columnPath:o,columnFields:s,dataFields:l,dataIndex:x,dataValues:S,visible:C&&y},I=(a.inverted?u:V).join(" - "),P=(a.inverted?V:u).join("/");l.length>1&&(("args"===a.putDataFieldsInto||"both"===a.putDataFieldsInto)&&(P+=" | ".concat(A.caption)),"args"!==a.putDataFieldsInto&&(I+=" | ".concat(A.caption),"singleAxis"!==a.dataFieldsDisplayMode&&(T=A.caption))),b.chartDataItem={val:void 0===E?null:E,series:I,arg:P},b=function Qn(e,a){var{chartDataItem:t}=e,r=a&&a(e);return r?(t=(0,X.l)({},t,r.chartDataItem),r=(0,X.l)({},e,r,{chartDataItem:t})):e}(b,a.processCell),b.visible&&(t[b.chartDataItem.series]=t[b.chartDataItem.series]||T,n.push(b.chartDataItem))}function p(S){ce(f,E=>{_=E[0].index,E=E.slice(0,E.length-1),y=v===E.length,N=ge(E),V=va(E,h),0===N.length&&(V=[a.grandTotalText]),ce(d,T=>{U=T[0].index,T=T.slice(0,T.length-1),C=g===T.length,o=ge(T),u=va(T,s),0===o.length&&(u=[a.grandTotalText]),S()})})}function w(S){(0,Z.S6)(l,(E,T)=>{A=T,x=E,S()})}return!1===a.alternateDataFields?w(()=>{p(c)}):p(()=>{w(c)}),n}(e,a,l),n.seriesTemplate={nameField:"series",customizeSeries(h){var s={};return"splitPanes"===a.dataFieldsDisplayMode?s.pane=l[h]:"singleAxis"!==a.dataFieldsDisplayMode&&(s.axis=l[h]),t&&(s=(0,X.l)(s,t(h,s))),s}},n}(t.getDataSource(),a);e.option(l)};if(e=function qn(e){if(!e)return!1;if(e.NAME)return"dxChart"===e.NAME&&e;var a=(0,M.Z)(e);return a.data("dxChart")&&a.dxChart("instance")}(e),!e)return null;(function ei(e){var a=e.$element().data(St);a&&a()})(e),t.on("changed",r),r();var n=function(){e.$element().removeData(St),t.off("changed",r)};return e.on("disposing",n),this.on("disposing",n),e.$element().data(St,n),n}},ti=L(45303),Xe=L(87462),ye=L(24245),qe=L(17839),ai=L(55053),ma=function(e){var a=0,{offsetWidth:t}=e;if(e.getBoundingClientRect){var r=(0,qe.l)(e);(a=r.width)||(a=r.right-r.left),a<=t-1&&(a=t)}return a>0?a:t};function fa(e,a,t,r){var n=0,l=0,h=t/2;return e+r-(a+t)>1?(e>=a+t+h&&(l=parseInt((e-(a+t))/h,10)),n=a+t+h*l):e<a?(e<=a-h&&(l=parseInt((e-(a-h))/h,10)),n=a-(t-h*l)):n=a,n}var et=ye.Z.inherit({ctor(e){this.component=e},option(){return this.component.option.apply(this.component,arguments)},_getRowElement(e){return this._tableElement&&this._tableElement.length>0?this._tableElement[0].rows[e]:null},_createGroupElement:()=>(0,M.Z)("<div>"),_createTableElement:()=>(0,M.Z)("<table>"),_getCellText(e,a){var t=e.isWhiteSpace?"&nbsp":e.text||"&nbsp";return a&&(-1!==t.indexOf("<")||-1!==t.indexOf(">"))&&(t=(0,M.Z)("<div>").text(t).html()),t},_getRowClassNames(){},_applyCustomStyles(e){e.cell.width&&e.cssArray.push("min-width:".concat(e.cell.width,"px")),e.cell.sorted&&e.classArray.push("dx-pivotgrid-sorted")},_getMainElementMarkup:()=>"<tbody>",_getCloseMainElementMarkup:()=>"</tbody>",_renderTableContent(e,a){var r,n,l,h,s,g,t=a.length,d=this.option("rtlEnabled"),f=[],v=this.option("encodeHtml");for(e.data("area",this._getAreaName()),e.data("data",a),e.css("width",""),f.push(this._getMainElementMarkup()),l=0;l<t;l+=1){r=a[l];var x=[];for(g=[],f.push("<tr "),h=0;h<r.length;h+=1){if(this._getRowClassNames(l,n=r[h],g),x.push("<td "),n){n.rowspan&&x.push("".concat("rowspan='"+(n.rowspan||1),"'")),n.colspan&&x.push("".concat("colspan='"+(n.colspan||1),"'"));var A={cellElement:void 0,cell:n,cellsCount:r.length,cellIndex:h,rowElement:void 0,rowIndex:l,rowsCount:t,rtlEnabled:d,classArray:[],cssArray:[]};this._applyCustomStyles(A),A.cssArray.length&&(x.push("style='"),x.push(A.cssArray.join(";")),x.push("'")),A.classArray.length&&(x.push("class='"),x.push(A.classArray.join(" ")),x.push("'")),x.push(">"),(0,H.$K)(n.expanded)&&x.push("<div class='dx-expand-icon-container'><span class='".concat("dx-expand","'></span></div>")),s=this._getCellText(n,v)}else s="";x.push("<span "),(0,H.$K)(n.wordWrapEnabled)&&x.push("style='white-space:",n.wordWrapEnabled?"normal":"nowrap",";'"),x.push(">".concat(s,"</span>")),n.sorted&&x.push("<span class='dx-icon-sorted'></span>"),x.push("</td>")}g.length&&(f.push("class='"),f.push(g.join(" ")),f.push("'")),f.push(">"),f.push(x.join("")),f.push("</tr>")}f.push(this._getCloseMainElementMarkup()),e.append(f.join("")),this._triggerOnCellPrepared(e,a)},_triggerOnCellPrepared(e,a){var h,s,d,v,g,x,A,t=e.find("tr"),r=this._getAreaName(),n=this.option("onCellPrepared"),l=this.component._eventsStrategy.hasEvent("cellPrepared"),f=this.component._defaultActionArgs();if(n||l)for(x=0;x<a.length;x+=1)for(v=a[x],h=t.eq(x),A=0;A<v.length;A+=1)g=v[A],s=h.children().eq(A),d={area:r,rowIndex:x,columnIndex:A,cellElement:(0,ta.u)(s),cell:g},l?this.component._trigger("onCellPrepared",d):n((0,X.l)(d,f))},_getRowHeight(e){var a=this._getRowElement(e),t=0,{offsetHeight:r}=a;return a&&a.lastChild?(a.getBoundingClientRect&&(t=(0,qe.l)(a).height)<=r-1&&(t=r),t>0?t:r):0},_setRowHeight(e,a){var t=this._getRowElement(e);t&&(t.style.height="".concat(a,"px"))},getRowsLength(){return this._tableElement&&this._tableElement.length>0?this._tableElement[0].rows.length:0},getRowsHeight(){var t,e=[],a=this.getRowsLength();for(t=0;t<a;t+=1)e.push(this._getRowHeight(t));return e},setRowsHeight(e){var r,a=0,t=e.length;for(r=0;r<t;r+=1)a+=e[r],this._setRowHeight(r,e[r]);this._tableHeight=a,this._tableElement[0].style.height="".concat(a,"px")},getColumnsWidth(){var a,t,r,n,e=this.getRowsLength(),l=[],h=[],s=function(d,f,v,g,x){var A,_;for(A=0;A<g;A+=1)for(_=0;_<x;_+=1)d[f+A]=d[f+A]||[],d[f+A][v+_]=!0};if(e)for(a=0;a<e;a+=1)for(l[a]=l[a]||[],t=this._getRowElement(a),r=0;r<t.cells.length;r+=1){for(n=0;l[a][n];n+=1);s(l,a,n,t.cells[r].rowSpan,t.cells[r].colSpan),1===t.cells[r].colSpan&&(h[n]=h[n]||ma(t.cells[r]))}return h},setColumnsWidth(e){var a,t=this._tableElement[0],r="",n=this.getColumnsCount(),l=[];for(a=0;a<n;a+=1)l.push(e[a]||0);for(a=n;a<e.length&&e;a+=1)l[n-1]+=e[a];for(a=0;a<n;a+=1)r+='<col style="width: '.concat(l[a],'px">');this._colgroupElement.html(r),this._tableWidth=l.reduce((h,s)=>h+s,0),t.style.width="".concat(this._tableWidth,"px"),t.style.tableLayout="fixed"},resetColumnsWidth(){(0,ae.cl)(this._colgroupElement.find("col"),"auto"),this._tableElement.css({width:"",tableLayout:""})},setGroupWidth(e){this._getScrollable().option("width",e)},setGroupHeight(e){this._getScrollable().option("height",e)},getGroupHeight(){return this._getGroupElementSize("height")},getGroupWidth(){return this._getGroupElementSize("width")},_getGroupElementSize(e){var a=this.groupElement()[0].style[e];return a.indexOf("px")>0?parseFloat(a):null},groupElement(){return this._groupElement},tableElement(){return this._tableElement},element(){return this._rootElement},headElement(){return this._tableElement.find("thead")},_setTableCss(e){this.option("rtlEnabled")&&(e.right=e.left,delete e.left),this.tableElement().css(e)},setVirtualContentParams(e){this._virtualContent.css({width:e.width,height:e.height}),this._getScrollable()?.isRenovated()?this._getScrollable().option("classes","dx-virtual-mode"):this.groupElement().addClass("dx-virtual-mode")},disableVirtualMode(){this._getScrollable()?.isRenovated()?this._getScrollable().option("classes",""):this.groupElement().removeClass("dx-virtual-mode")},_renderVirtualContent(){!this._virtualContent&&"virtual"===this.option("scrolling.mode")&&(this._virtualContent=(0,M.Z)("<div>").addClass("dx-virtual-content").insertBefore(this._tableElement))},reset(){var e=this._tableElement[0];if(this._fakeTable&&this._fakeTable.detach(),this._fakeTable=null,this.disableVirtualMode(),this.setGroupWidth("100%"),this.setGroupHeight("auto"),this.resetColumnsWidth(),e){for(var a=0;a<e.rows.length;a+=1)e.rows[a].style.height="";e.style.height="",e.style.width="100%"}},_updateFakeTableVisibility(){var e=this.tableElement()[0],a=this.option("rtlEnabled")?"right":"left",t=this._fakeTable[0];e.style.top===t.style.top&&t.style[a]===e.style[a]?this._fakeTable.addClass("dx-hidden"):this._fakeTable.removeClass("dx-hidden")},_moveFakeTableHorizontally(e){var t=this.option("rtlEnabled")?"right":"left",n=fa(e,parseFloat(this.tableElement()[0].style[t]),this._tableWidth,this.getGroupWidth());parseFloat(this._fakeTable[0].style[t])!==n&&(this._fakeTable[0].style[t]="".concat(n,"px"))},_moveFakeTableTop(e){var t=fa(e,parseFloat(this.tableElement()[0].style.top),this._tableHeight,this.getGroupHeight());parseFloat(this._fakeTable[0].style.top)!==t&&(this._fakeTable[0].style.top="".concat(t,"px"))},_moveFakeTable(){this._updateFakeTableVisibility()},_createFakeTable(){this._fakeTable||(this._fakeTable=this.tableElement().clone().addClass("dx-pivot-grid-fake-table").appendTo(this._virtualContent))},render(e,a){if(this._tableElement){try{this._tableElement[0].innerHTML=""}catch{this._tableElement.empty()}this._tableElement.removeAttr("style")}else this._groupElement=this._createGroupElement(),this._tableElement=this._createTableElement(),this._tableElement.appendTo(this._groupElement),this._groupElement.appendTo(e),this._rootElement=e;this._colgroupElement=(0,M.Z)("<colgroup>").appendTo(this._tableElement),this._renderTableContent(this._tableElement,a),this._renderVirtualContent()},_getScrollable(){return this.groupElement().data("dxScrollable")},_getMemoizeScrollTo(){var e;return this._memoizeScrollTo=null!==(e=this._memoizeScrollTo)&&void 0!==e?e:(0,ai.n)(()=>this._getScrollable()),this._memoizeScrollTo},_getMaxLeftOffset(e){var a=(0,M.Z)(e.container()).get(0);return a.scrollWidth-a.clientWidth},on(e,a){var t=this,r=t._getScrollable();return r&&r.on(e,n=>{t.option("rtlEnabled")&&(0,H.$K)(n.scrollOffset.left)&&(n.scrollOffset.left=t._getMaxLeftOffset(r)-n.scrollOffset.left),a(n)}),this},off(e){var a=this._getScrollable();return a&&a.off(e),this},scrollTo(e){var a=arguments.length>1&&void 0!==arguments[1]&&arguments[1],t=this._getScrollable();if(t){var r=this.option("rtlEnabled"),n=this._getAreaName(),l=(0,Xe.Z)((0,Xe.Z)({},e),{left:!r||"column"!==n&&"data"!==n?e.left:this._getMaxLeftOffset(t)-e.left});this._getMemoizeScrollTo()(l,a),this._virtualContent&&(this._createFakeTable(),this._moveFakeTable(e))}},updateScrollable(){var e=this._getScrollable();if(e)return e.update()},getColumnsCount(){var t,e=0,a=this._getRowElement(0);if(a)for(var r=0,n=(t=a.cells).length;r<n;++r)e+=t[r].colSpan;return e},getData(){var e=this._tableElement;return e?e.data("data"):[]}});const ci={DataArea:et.inherit({_getAreaName:()=>"data",_createGroupElement:()=>(0,M.Z)("<div>").addClass("dx-pivotgrid-area").addClass("dx-pivotgrid-area-data").css("borderTopWidth",0),_applyCustomStyles(e){var{cell:a}=e,{classArray:t}=e;("T"===a.rowType||"T"===a.columnType)&&t.push("dx-total"),("GT"===a.rowType||"GT"===a.columnType)&&t.push("dx-grandtotal"),("T"===a.rowType||"GT"===a.rowType)&&t.push("dx-row-total"),e.rowIndex===e.rowsCount-1&&e.cssArray.push("border-bottom: 0px"),this.callBase(e)},_moveFakeTable(e){this._moveFakeTableHorizontally(e.x),this._moveFakeTableTop(e.y),this.callBase()},renderScrollable(){this._groupElement.dxScrollable({useNative:this.getUseNativeValue(),useSimulatedScrollbar:!1,rtlEnabled:this.component.option("rtlEnabled"),bounceEnabled:!1,updateManually:!0})},getUseNativeValue(){var{useNative:e}=this.component.option("scrolling");return"auto"===e?!!ti.nativeScrolling:!!e},getScrollbarWidth(){return this.getUseNativeValue()?ha():0},updateScrollableOptions(e){var{direction:a,rtlEnabled:t}=e,r=this._getScrollable();r.option("useNative",this.getUseNativeValue()),r.option({direction:a,rtlEnabled:t})},getScrollableDirection:(e,a)=>e&&!a?"horizontal":!e&&a?"vertical":"both",reset(){this.callBase(),this._virtualContent&&this._virtualContent.parent().css("height","auto")},setVirtualContentParams(e){this.callBase(e),this._virtualContent.parent().css("height",e.height),this._setTableCss({top:e.top,left:e.left})}})};var Fe=L(47685),ui=L(39644),wt=L(40300),hi=L(5042),vi=L(38852),gi=L(46769),pi=L(68262),mi=L(60577),yt=L(2928),_a=L(47331),fi=L(32451),_i=L(82510),Ca=L(6059),Sa=ye.Z.inherit(function(){var e={year:C=>C&&C.getFullYear(),quarter:C=>C&&Math.floor(C.getMonth()/3)+1,month:C=>C&&C.getMonth()+1,day:C=>C&&C.getDate(),dayOfWeek:C=>C&&C.getDay()};function a(C){return-1!==C.indexOf(".")?(0,We.vb)(C):function(o){return o[C]}}function t(C){return function(o){var u=C(o);return u&&!(u instanceof Date)&&(u=_a.Z.deserializeDate(u)),u}}function r(C){(0,Z.S6)(C||[],(o,u)=>{var c,p,S,T,{dataField:w}=u,{levels:E}=u;if(!u.selector){if(T=w?a(w):function(I){return I},E&&r(E),"date"===u.dataType){p=e[u.groupInterval];var b=t(T);c=function(I){var P=b(I);return p?p(P):P}}else"number"===u.dataType?(S=(0,H.kE)(u.groupInterval)&&u.groupInterval>0&&u.groupInterval,c=function(I){var P=T(I);return(0,H.HD)(P)&&(P=Number(P)),S?Math.floor(P/S)*S:P}):c=T;ft(u),Ce(u,"selector",c)}})}function n(C,o,u,c){var p=[0],w=o.headerName===c?o.path.length:0,S="rows"===c?o.rowExpandedPaths:o.columnExpandedPaths;return function T(b,I,P,m,D){var R,B,G,$,W,Q,F=I.dimensions[m],{expandedPathsHash:O}=I;F&&(R=F.selector(I.data),D=void 0!==D?D+"/./"+R:"".concat(R),G=P,(Q=(W=I.childrenHash)[$=D])||(Q={value:R,index:W.length++},W[$]=Q,G.push(Q)),b.push((B=Q).index),(O&&O[D]||F.expanded)&&(B.children||(B.children=[]),T(b,I,B.children,m+1,D)))}(p,{data:C,childrenHash:u["".concat(c,"Hash")],dimensions:o[c],expandedPathsHash:o.headerName!==c&&S&&S.hash},u[c],w),p}function l(C,o,u,c){var w,S,E,T,p=[],b=n(C,c,u,"rows"),I=n(C,c,u,"columns");for(S=0;S<b.length;S+=1)for(o[E=b[S]]=o[E]||[],w=0;w<I.length;w+=1)p.push(o[E][T=I[w]]=o[E][T]||[]);return p}function h(C){if(C){var o=C.hash={};C.forEach(u=>{var c=u.map(p=>"".concat(p)).join("/./");o[c]=!0})}}function s(C){C.rows=C.rows||[],C.columns=C.columns||[],C.filters=C.filters||[],h(C.columnExpandedPaths),h(C.rowExpandedPaths),r(C.columns),r(C.rows),r(C.values),r(C.filters)}function d(C){return"custom"===C.summaryType?(C.calculateCustomSummary=C.calculateCustomSummary||me.ZT,{seed(){var o={summaryProcess:"start",totalValue:void 0};return C.calculateCustomSummary(o),o},step:(o,u)=>(o.summaryProcess="calculate",o.value=u,C.calculateCustomSummary(o),o),finalize:o=>(o.summaryProcess="finalize",delete o.value,C.calculateCustomSummary(o),o.totalValue)}):Ca.bG[C.summaryType]||Ca.bG.count}function f(C,o,u){for(var c=0;c<C.length;c+=1)for(var p=C[c],w=p.selector(u),S=d(p),E="function"==typeof S.seed,T=0;T<o.length;T+=1){var b=o[T];b.length<=c&&(b[c]=E?S.seed():S.seed),void 0===b[c]?b[c]=w:(0,H.$K)(w)&&(b[c]=S.step(b[c],w))}}function v(C,o){var u=C&&C.valueOf(),c=o&&o.valueOf();if(Array.isArray(C)){o=o||[];for(var p=0;p<C.length;p+=1)if((u=C[p]&&C[p].valueOf())!==(c=o[p]&&o[p].valueOf()))return!1;return!0}return u===c}function g(C){var o=[];return(0,Z.S6)(C,(u,c)=>{var p=c.filterValues||[],{groupName:w}=c;w&&(0,H.kE)(c.groupIndex)||p.length&&o.push(function(S){for(var E=c.levels?function(I,P){var m=[];return(0,Z.S6)(I,(D,F)=>{m.push(F.selector(P))}),m}(c.levels,S):c.selector(S),T=!1,b=0;b<p.length;b+=1)if(v(p[b],E)){T=!0;break}return"exclude"===c.filterType?!T:T})}),o}function x(C){var o=g(C.rows).concat(g(C.columns)).concat(g(C.filters)),u=C[C.headerName],{path:c}=C;return u&&o.push(p=>{for(var w,S=0;S<c.length;S+=1)if(w=u[S].selector(p),(0,We.Ex)(w,!0)!==(0,We.Ex)(c[S],!0))return!1;return!0}),function(p){for(var w=0;w<o.length;w+=1)if(!o[w](p))return!1;return!0}}function _(C,o){var u=C.filter();return C.store()instanceof fi.Z&&u?(u=U(u,o),(0,_i.Z)(C.items()).filter(u).toArray()):C.items()}function y(C,o,u){var c=new J.BH,p=function(S){C.store()instanceof ke.Z&&(S.storeLoadOptions.filter=U(S.storeLoadOptions.filter,o))};if(C.on("customizeStoreLoadOptions",p),!C.isLoaded()||u){var w=u?C.load():C.reload();(0,J.gx)(w).done(()=>{y(C,o).done(()=>{c.resolve(_(C,o))}).fail(c.reject)}).fail(c.reject)}else c.resolve(_(C,o));return c.always(()=>{C.off("customizeStoreLoadOptions",p)})}function V(C,o){o.forEach(u=>{if(u.dataField&&"date"===u.dataType){var c=t(a(u.dataField));C[u.dataField]=function(p){return c(p)}}})}function N(C){var o={};return Array.isArray(C)?V(o,C):C&&["rows","columns","filters"].forEach(u=>{C[u]&&V(o,C[u])}),o}function U(C,o){if(!Array.isArray(C))return C;C=C.slice(0),(0,H.HD)(C[0])&&(C[1]instanceof Date||C[2]instanceof Date)&&(C[0]=o[C[0]]);for(var u=0;u<C.length;u+=1)C[u]=U(C[u],o);return C}return{ctor(C){this._progressChanged=C.onProgressChanged||me.ZT,this._dataSource=new ze.o(C),this._dataSource.paginate(!1)},getFields(C){var o=this._dataSource,u=new J.BH;return y(o,N(C)).done(c=>{u.resolve(ca(c,C))}).fail(u.reject),u},key(){return this._dataSource.key()},load(C){var o=this,u=o._dataSource,c=new J.BH;return s(C),y(u,N(C),C.reload).done(p=>{(0,J.gx)(function A(C,o,u){var w,S,c={columns:[],rows:[],columnsHash:{length:1},rowsHash:{length:1}},p=[],E=new J.BH,T=0,b=x(o);return function I(){for(var P=new Date,m=T;T<C.length;T+=1){if(T>m&&T%1e4==0&&new Date-P>=300)return u(T/C.length),void setTimeout(I,0);b(S=C[T])&&(w=l(S,p,c,o),f(o.values,w,S))}var F;F=p,(0,Z.S6)(o.values,(O,R)=>{var B=d(R);B.finalize&&(0,Z.S6)(F,(z,G)=>{(0,Z.S6)(G,($,W)=>{W&&void 0!==W[O]&&(W[O]=B.finalize(W[O]))})})}),u(1),E.resolve({rows:c.rows,columns:c.columns,values:p,grandTotalRowIndex:0,grandTotalColumnIndex:0})}(),E}(p,C,o._progressChanged)).done(c.resolve)}).fail(c.reject),c},filter(){var C=this._dataSource;return C.filter.apply(C,arguments)},supportPaging:()=>!1,getDrillDownItems(C,o){o=o||{},s(C=C||{});for(var p,u=[],c=this._dataSource.items(),{maxRowCount:w}=o,{customColumns:S}=o,E=x(C),T=x({rows:be(C.rows,o.rowPath),columns:be(C.columns,o.columnPath),filters:[]}),b=0;b<c.length;b+=1){if(T(c[b])&&E(c[b])){if(S){p={};for(var I=0;I<S.length;I+=1)p[S[I]]=c[b][S[I]]}else p=c[b];u.push(p)}if(w>0&&u.length===w)break}return u}}}()).include(_t),wa=function e(a,t,r){a=a||[],r=r||0;for(var n=0;n<a.length;n+=1){var l=a[n];t(l,r),l&&l.items&&l.items.length&&e(l.items,t,r+1)}};function ya(e,a){var t=[];return(0,Z.S6)(e,(r,n)=>{t.push({selector:n.dataField,groupInterval:n.groupInterval,desc:a&&"desc"===n.sortOrder,isExpanded:r<e.length-1})}),t}function Ta(e,a,t,r){return[[e,r?"<":">=",t],r?"or":"and",[e,r?">=":"<",t+a]]}function Ea(e,a){var t=[];return e.searchValue?[e.dataField,"contains",e.searchValue]:(a="exclude"===e.filterType?a||"and":a||"or",(0,Z.S6)(e.filterValues,(r,n)=>{var l=[];Array.isArray(n)?e.levels&&e.levels.length&&(l=Ea({filterValues:n,filterType:e.filterType,levels:e.levels},"and")):l=function xi(e,a){var t=function Ci(e){var a=e.dataField,{groupInterval:t}=e;return"date"===e.dataType&&"string"==typeof t&&("quarter"===t.toLowerCase()&&(t="Month"),a="".concat(a,".").concat(Ct(t))),a}(e),r="exclude"===e.filterType,n=[t,r?"<>":"=",a];return(0,H.$K)(e.groupInterval)&&("string"==typeof e.groupInterval&&"quarter"===e.groupInterval.toLowerCase()?n=Ta(t,3,3*(a-1)+1,r):"number"==typeof e.groupInterval&&"date"!==e.dataType&&(n=Ta(t,e.groupInterval,a,r))),n}(e.levels?e.levels[r]:e,n),!l.length||(t.length&&t.push(a),t.push(l))}),t)}function ba(e){var a=[];return(0,Z.S6)(e,(t,r)=>{var n=Ea(r);if(!n.length)return[];a.length&&a.push("and"),a.push(n)}),1===a.length&&(a=a[0]),a}function Aa(e,a){var r=function(n){return n&&n.length};return r(e)&&r(a)?[e,"and",a]:r(e)?e:a}function Da(e,a,t,r,n){e[t]=e[t]||[],e[t][r]=e[t][r]||[],(0,H.$K)(e[t][r][n])||(e[t][r][n]=a)}function wi(e,a){return a&&"number"===a.dataType&&(0,H.HD)(e)?Number(e):!a||"date"!==a.dataType||a.groupInterval||e instanceof Date?e:_a.Z.deserializeDate(e)}function yi(e,a,t,r){var n=[],l=[],{rowHash:h}=r,{columnHash:s}=r;function f(v,g,x,A,_){var V,U,o,y=r["".concat(g,"Hash")],C=x.slice(0,A+1).join("/");return void 0!==y[C]?U=y[C]:(U={value:wi(v.key,_),index:r["".concat(g,"Index")]++,displayText:v.displayText},o=x.slice(0,A).join("/"),(A>0&&void 0!==y[o]?(V=y[o]).children=V.children||[]:r["".concat(g,"s")]).push(U),y[C]=U),U}return a&&a.summary&&(0,Z.S6)(a.summary,(v,g)=>{Da(r.values,g,r.grandTotalRowIndex,r.grandTotalColumnIndex,v)}),a&&a.groupCount>=0&&((e=[...Array(t.rows.length?t.rowSkip:t.columnSkip)].concat(e)).length=a.groupCount),wa(e,(v,g)=>{var _,y,x=g>=t.rows.length?t.rows.length:g,A=g>=t.rows.length?g-t.rows.length:0;if(!(g>=t.rows.length&&A>=t.columns.length)){g<t.rows.length&&(l=[]),g>=t.rows.length?v?(l[A]="".concat(v.key),_=f(v,"column",l,A,t.columns[A]),y=h[n.slice(0,x+1).join("/")]):r.columns.push({}):v?(n[x]="".concat(v.key),y=f(v,"row",n,x,t.rows[x]),_=s[l.slice(0,A+1).join("/")]):r.rows.push({});var V=y&&y.index||r.grandTotalRowIndex,N=_&&_.index||r.grandTotalColumnIndex;(0,Z.S6)(v&&v.summary||[],(U,C)=>{Da(r.values,C,V,N,U)})}}),r}function tt(e){return(e||[]).filter(a=>a.filterValues&&a.filterValues.length||a.searchValue)}function Pa(e,a){if(e.headerName){if(a===e.headerName)return e.path.length;if(e.oppositePath)return e.oppositePath.length}return 0}function Tt(e,a,t,r){var n=[],l=t>r?0:r,h=e.headerName!==a?e[a].slice(l,t):[],s="rows"===a?e.rowExpandedPaths:e.columnExpandedPaths;return(0,Z.S6)(h,(d,f)=>{var v=[];(0,Z.S6)(s,(g,x)=>{if(x=x.slice(l,t),d<x.length){var A=x[d];v.includes(A)||v.push(A)}}),v.length&&n.push((0,X.l)({},f,{filterType:"include",filterValues:v}))}),n}function Fa(e,a,t,r,n,l){var v,s="columns"===a?"rows":"columns",d=e[a],f=[];if((("columns"===a?e.columnExpandedPaths:e.rowExpandedPaths)||[]).length)for(var g=t;g<r+1;g+=1)(v={filters:n.concat(Tt(e,a,g,l))})[a]=d.slice(t,g+1),v[s]=[],f.push((0,X.l)({},e,v));else(v={filters:n})[a]=d.slice(t,r+1),v[s]=[],f.push((0,X.l)({},e,v));return f[0].includeTotalSummary=!0,f}function Ia(e){var a=0;return(0,Z.S6)(e,(t,r)=>{if(!r.expanded)return a=t,!1}),a}function at(e){(0,Z.S6)(e||[],(a,t)=>{var{levels:r}=t;r&&at(r),ft(t)})}var La=ye.Z.inherit({ctor(e){this._dataSource=new ze.o(e),this._store=this._dataSource.store()},getFields(e){var a=new J.BH;return this._store.load({skip:0,take:20}).done(t=>{var r=(0,yt.r6)(t);a.resolve(xt.discoverObjectFields(r.data,e))}).fail(a.reject),a},key(){return this._store.key()},load(e){var a=this,t=new J.BH,r={rows:[],columns:[],values:[],grandTotalRowIndex:0,grandTotalColumnIndex:0,rowHash:{},columnHash:{},rowIndex:1,columnIndex:1},n=function Ei(e){var a=je(e,"rows"),t=je(e,"columns"),r=e.filters||[],n=Pa(e,"columns"),l=Ia(e.columns),h=Ia(e.rows),s=Pa(e,"rows"),d=[],f=Fa(e,"columns",n,t,r=r.concat(tt(e.rows)).concat(tt(e.columns)).concat(function Ti(e){return be(e[e.headerName],e.path).concat(be(e["rows"===e.headerName?"columns":"rows"],e.oppositePath||[]))}(e)),l);if(e.rows.length&&e.columns.length){"rows"!==e.headerName&&(d=d.concat(f));for(var v=s;v<a+1;v+=1)for(var g=e.rows.slice(s,v+1),x=Tt(e,"rows",v,h),A=n;A<t+1;A+=1){var _=(0,X.l)({},e,{columns:e.columns.slice(n,A+1),rows:g,filters:r.concat(Tt(e,"columns",A,l)).concat(x)});d.push(_)}}else d=e.columns.length?f:Fa(e,"rows",s,a,r,h);return d}(e),l=[];return at(e.rows),at(e.columns),at(e.filters),(0,Z.S6)(n,(h,s)=>{l.push(a._store.load(function Si(e,a,t){var r=ba(e.filters),n=ya(e.rows,e.rowTake).concat(ya(e.columns,e.columnTake)),l={groupSummary:[],totalSummary:[],group:n.length?n:void 0,take:n.length?void 0:1};return e.rows.length&&e.rowTake?(l.skip=e.rowSkip,l.take=e.rowTake,l.requireGroupCount=!0):e.columns.length&&e.columnTake&&!t&&(l.skip=e.columnSkip,l.take=e.columnTake,l.requireGroupCount=!0),a&&(r=Aa(r,a)),r.length&&(l.filter=r),(0,Z.S6)(e.values,(h,s)=>{var d={selector:s.dataField,summaryType:s.summaryType||"count"};l.groupSummary.push(d),e.includeTotalSummary&&l.totalSummary.push(d)}),l}(s,a.filter(),e.rows.length)))}),J.gx.apply(null,l).done(function(){var h=l.length>1?arguments:[arguments];(0,Z.S6)(h,(s,d)=>{var f=(0,yt.r6)(d[0],d[1]);yi(f.data,f.extra,n[s],r)}),t.resolve({rows:r.rows,columns:r.columns,values:r.values,grandTotalRowIndex:r.grandTotalRowIndex,grandTotalColumnIndex:r.grandTotalColumnIndex})}).fail(t.reject),t},filter(){return this._dataSource.filter.apply(this._dataSource,arguments)},supportPaging:()=>!1,createDrillDownDataSource(e,a){var t=this._store,n=ba(be((e=e||{}).rows,(a=a||{}).rowPath).concat(be(e.columns,a.columnPath)).concat(tt(e.rows)).concat(e.filters||[]).concat(tt(e.columns)));return new ze.o({load:l=>t.load((0,X.l)({},l,{filter:Aa(n,l.filter),select:a.customColumns}))})}}),Ae="column",Ie="row",he=null,Oa=function(e,a){var t=e/a;return(!(0,H.$K)(e)||isNaN(t))&&(t=he),t},Et=function(e,a){return Oa(e.value(),e.grandTotal(a).value())},Ra=function(e,a){var t=e.parent(a),r=t?t.value():e.value();return Oa(e.value(),r)},Ma=function(e){return function(a){var t=a.prev(Ae,e),r=t&&t.value();return(0,H.$K)(r)&&(0,H.$K)(a.value())?a.value()-r:he}},Za={percentOfColumnTotal:e=>Ra(e,Ie),percentOfRowTotal:e=>Ra(e,Ae),percentOfColumnGrandTotal:e=>Et(e,Ie),percentOfRowGrandTotal:e=>Et(e,Ae),percentOfGrandTotal:e=>Et(e)},Ai=function e(a,t){if(a&&a.parent(t)){var r=a.prev(t);return r||(r=e(a.parent(t),t)),r}},Di=function(e){if(e.runningTotal){var a=e.runningTotal===Ae?Ie:Ae;return function(t){var r=e.allowCrossGroupCalculation?Ai(t,a):t.prev(a,!1),n=t.value(!0),l=r&&r.value(!0);return(0,H.$K)(l)&&(0,H.$K)(n)?n=l+n:(0,H.$K)(l)&&(n=l),n}}};function At(e,a,t){var r={index:-1};if(!(0,H.Kn)(a))if(t.fields[a])a=t[a];else{var n=e.columns.concat(e.rows).concat(e.values),l=Ee(n,a);a=t[a]=n[l]}if(a){var h=a.area||"data";r=t.positions[a.index]=t.positions[a.index]||{area:h,index:e["data"===h?"values":"".concat(h,"s")].indexOf(a)}}return r}function He(e){return e===Ie?"_rowPath":"_columnPath"}var De=function(e,a,t,r,n,l){this._columnPath=e,this._rowPath=a,this._fieldIndex=n,this._fieldsCache=l||{fields:{},positions:{}},this._data=t,this._descriptions=r;var h=t.values&&t.values[a[0].index]&&t.values[a[0].index][e[0].index];h&&(h.originalCell=h.originalCell||h.slice(),h.postProcessedFlags=h.postProcessedFlags||[],this._cell=h)};function Ha(e){var{summaryDisplayMode:a}=e,t=e.allowCrossGroupCalculation,r=he;return(0,H.mf)(e.calculateSummaryValue)?r=e.calculateSummaryValue:a&&(r="absoluteVariation"===a?Ma(t):"percentVariation"===a?function(e){var a=Ma(e);return function(t){var r=a(t),n=t.prev(Ae,e),l=n&&n.value();return r!==he&&l?r/l:he}}(t):Za[a],r&&!e.format&&-1!==a.indexOf("percent")&&xt.setFieldProperty(e,"format","percent")),r}function Ba(e,a,t,r){var n=e.values[a][t]=e.values[a][t]||[],{originalCell:l}=n;!l||((n.allowResetting||!r)&&(e.values[a][t]=l.slice()),e.values[a][t].allowResetting=r)}De.prototype=(0,X.l)(De.prototype,{_getPath(e){return this[He(e)]},_getDimension(e){return this._descriptions[e=e===Ie?"rows":"columns"]},_createCell(e){return new De(e._columnPath||this._columnPath,e._rowPath||this._rowPath,this._data,this._descriptions,this._fieldIndex)},parent(e){var a=this._getPath(e).slice(),t={};return a.shift(),a.length?(t[He(e)]=a,this._createCell(t)):he},children(e){var a=this._getPath(e).slice(),t=a[0],r=[],n={};if(t.children)for(var l=0;l<t.children.length;l+=1)n[He(e)]=[t.children[l]].concat(a.slice()),r.push(this._createCell(n));return r},grandTotal(e){var a={},t=this._rowPath,r=this._columnPath,n=this._getPath(e),l=He(e);return e?a[l]=[n[n.length-1]]:(a._rowPath=[t[t.length-1]],a._columnPath=[r[r.length-1]]),this._createCell(a)},next(e,a){var l,t=this._getPath(e),r=t[0],n=this.parent(e);if(n){var h=t[1].children.indexOf(r);if((l=n.children(e))[h+1])return l[h+1]}if(a&&n){do{l=(n=n.next(e,a))?n.children(e):[]}while(n&&!l.length);return l[0]||he}return he},prev(e,a){var l,t=this._getPath(e),r=t[0],n=this.parent(e);if(n){var h=t[1].children.indexOf(r);if((l=n.children(e))[h-1])return l[h-1]}if(a&&n){do{l=(n=n.prev(e,a))?n.children(e):[]}while(n&&!l.length);return l[l.length-1]||he}return he},cell(){return this._cell},field(e){if("data"===e)return this._descriptions.values[this._fieldIndex];var a=this._getPath(e);return this._getDimension(e)[a.length-2]||he},child(e,a){for(var t,r=this.children(e),n=0;n<r.length;n+=1)if(t=t||r[n].field(e),r[n].value(t)===a)return r[n];return he},slice(e,a){var t={},r=At(this._descriptions,e,this._fieldsCache),{area:n}=r,l=r.index,h=he;if(n===Ie||n===Ae){var s=this._getPath(n).slice(),d=-1!==l&&s.length-2-l;if(s[d]){for(var f=d;f>=0;f-=1){if(s[f+1]){var v=s[f+1].children||[],g=f===d?a:s[f].value;s[f]=void 0;for(var x=0;x<v.length;x+=1)if(v[x].value===g){s[f]=v[x];break}}if(void 0===s[f])return h}t[He(n)]=s,h=this._createCell(t)}}return h},value(e,a){var t=this._cell,r=this._fieldIndex,n=!0===e||!1===e,l=n?he:e,h=n&&e||a;if((0,H.$K)(l)){var s=At(this._descriptions,l,this._fieldsCache);if(r=s.index,"data"!==s.area){var d=this._getPath(s.area),f=-1!==r&&d.length-2-r;return d[f]&&d[f].value}}return t&&t.originalCell?h?t[r]:t.originalCell[r]:he},isPostProcessed(e){var a=this._fieldIndex;if((0,H.$K)(e)){var t=At(this._descriptions,e,this._fieldsCache);if(a=t.index,"data"!==t.area)return!1}return!(!this._cell||!this._cell.postProcessedFlags[a])}});const Dt_applyRunningTotal=function Fi(e,a){var t=[],r=[{index:a.grandTotalColumnIndex,children:a.columns}],n=[{index:a.grandTotalRowIndex,children:a.rows}],l=e.values,h={fields:{},positions:{}};a.values=a.values||[],ce(n,s=>{var d=s[0];a.values[d.index]=a.values[d.index]||[],ce(r,f=>{Ba(a,d.index,f[0].index,!0);for(var g=0;g<l.length;g+=1){var A=t[g]=void 0===t[g]?Di(l[g]):t[g];if(A){var _=new De(f,s,a,e,g,h),y=_.cell();y[g]=A(_),y.postProcessedFlags[g]=!0}}},!1)},!1)},Dt_createMockSummaryCell=function Ii(e,a,t){var r=new De([],[],{},e,0);return r.value=function(n){if((0,H.$K)(n)){var l=Ee(a,n),h=a[l];!t[l]&&h&&!(0,H.$K)(h.area)&&(e.values.push(h),t[l]=!0)}},r.grandTotal=function(){return this},r.children=function(){return[]},r},Dt_applyDisplaySummaryMode=function Pi(e,a){var t=[],r=[{index:a.grandTotalColumnIndex,children:a.columns}],n=[{index:a.grandTotalRowIndex,children:a.rows}],l=e.values,h={fields:{},positions:{}};a.values=a.values||[],ce(r,s=>{s[0].isEmpty=[]},!1),ce(n,s=>{var d=s[0];d.isEmpty=[],a.values[d.index]=a.values[d.index]||[],ce(r,f=>{var g,v=f[0];Ba(a,d.index,v.index,!1);for(var x=0;x<l.length;x+=1){var _=t[x]=void 0===t[x]?Ha(l[x]):t[x];if(g=!1,_){var y=new De(f,s,a,e,x,h),V=y.cell(),N=V[x]=_(y);V.postProcessedFlags[x]=!0,g=null==N}void 0===v.isEmpty[x]&&(v.isEmpty[x]=!0),void 0===d.isEmpty[x]&&(d.isEmpty[x]=!0),g||(d.isEmpty[x]=v.isEmpty[x]=!1)}},!1)},!1),a.isEmptyGrandTotalRow=n[0].isEmpty,a.isEmptyGrandTotalColumn=r[0].isEmpty};var rt=L(30389),Li=L(73990),Oi=(0,we.Jj)();const Mi={XmlaStore:ye.Z.inherit(function(){var e='<Envelope xmlns="http://schemas.xmlsoap.org/soap/envelope/"><Body><Discover xmlns="urn:schemas-microsoft-com:xml-analysis"><RequestType>{2}</RequestType><Restrictions><RestrictionList><CATALOG_NAME>{0}</CATALOG_NAME><CUBE_NAME>{1}</CUBE_NAME></RestrictionList></Restrictions><Properties><PropertyList><Catalog>{0}</Catalog>{3}</PropertyList></Properties></Discover></Body></Envelope>',a="SELECT {2} FROM {0} {1} CELL PROPERTIES VALUE, FORMAT_STRING, LANGUAGE, BACK_COLOR, FORE_COLOR, FONT_FLAGS";function t(m,D){var F=new J.BH,{beforeSend:O}=m,R={url:m.url,dataType:"text",data:D,headers:{"Content-Type":"text/xml"},xhrFields:{},method:"POST"};return(0,H.mf)(O)&&O(R),xt.sendRequest(R).fail(function(){F.reject(arguments)}).done(B=>{var G,z=new Oi.DOMParser;try{try{G=z.parseFromString(B,"text/xml")}catch{G=void 0}if(!G||G.getElementsByTagName("parsererror").length||0===G.childNodes.length)throw new rt.R0.Error("E4023",B)}catch($){F.reject({statusText:$.message,stack:$.stack,responseText:B})}F.resolve(G)}),F}function r(){var m=(0,Li.D)();return void 0!==m?(0,de.WU)("<LocaleIdentifier>{0}</LocaleIdentifier>",m):""}function n(m){return"".concat(m.hierarchyName||m.dataField,".[All]")}function l(m){var D="".concat(m.dataField,".allMembers"),{searchValue:F}=m;return F&&(F=F.replace(/'/g,"''"),D="Filter(".concat(D,", instr(").concat(m.dataField,".currentmember.member_caption,'").concat(F,"') > 0)")),D}function h(m){var D=m.join(",");return m.length>1?(0,de.WU)("CrossJoin({0})",D):D}function s(m,D,F,O,R,B,z,G){for(var k,q,ee,$=[],W=B[z],Q=[],Y=O;Y<=D;Y+=1){var j=W[Y],{dataField:re}=j,oe=W[Y-1]&&W[Y-1].hierarchyName,{hierarchyName:ne}=j,se=!ne||!W[Y+1]||W[Y+1].hierarchyName!==ne,ve=m.length+F+O;if(k=null,Q.push(j),Y<m.length)se&&(k="(".concat(re,".").concat(c(m[Y],re),")"));else if(Y<=ve)if(0===Y&&0===F){var le=n(W[O]);k=ne?"".concat(le,",").concat(W[O].dataField):l(W[O])}else ne?(ee=c(R[R.length-1]),(se||Y===ve)&&(oe===ne?(R.length&&(q=W[R.length-1]),(!q||q.hierarchyName!==ne)&&(q=W[Y-1],ee=""),xr=re,k="Descendants({".concat(ee||q.dataField,"}, ").concat(xr,", SELF_AND_BEFORE)")):k=l(j))):k=l(j);else(!ne||oe!==ne)&&(k="(".concat(n(j),")"));k&&(k=(0,de.WU)("{{0}}",k),G&&(k=(0,de.WU)("Order({0}, {1}, {2})",k,(j.hierarchyName||j.dataField)+("displayText"===j.sortBy?".MEMBER_CAPTION":".MEMBER_VALUE"),"desc"===j.sortOrder?"DESC":"ASC")),$.push(k))}var xr;return h($)}function d(m,D,F,O,R,B,z,G,$,W){var q,Q=-1,k=B[z];do{q=D.length+(Q+=1)+O;var ee=s(D,F,Q,O,R,B,z,$);!$&&!W&&(ee=(0,de.WU)("NonEmpty({0}, {1})",ee,G)),m.push(ee)}while(k[q]&&k[q+1]&&k[q].expanded)}function f(m,D,F,O){return F=F||"[DX_Set_".concat(D.length,"]"),D.push((0,de.WU)("{0} {1} as {2}",O=O||"set",F,m)),F}function v(m,D,F,O,R){var re,oe,B=m[D],z=[],G=[],$=[],W=0,Q=0,k=[],q=(0,de.WU)("{{0}}",F.join(","));if(B&&B.length){m.headerName===D?W=(G=m.path).length:m.headerName&&m.oppositePath?W=(G=m.oppositePath).length:$=("columns"===D?m.columnExpandedPaths:m.rowExpandedPaths)||$,Q=je(m,D),d(z,[],Q,W,G,m,D,q,"rows"===D?m.rowTake:m.columnTake,m.totalsOnly),(0,Z.S6)($,(ne,se)=>{d(z,se,Q,W,se,m,D,q)});for(var ee=Q;ee>=G.length;ee-=1)B[ee].hierarchyName&&(R.visibleLevels[B[ee].hierarchyName]=R.visibleLevels[B[ee].hierarchyName]||[],R.visibleLevels[B[ee].hierarchyName].push(B[ee].dataField))}if(z.length){var Y=(oe=(re=z).join(","),re.length>1?"Union(".concat(oe,")"):oe);"rows"===D&&m.rowTake&&(Y=(0,de.WU)("Subset({0}, {1}, {2})",Y,m.rowSkip>0?m.rowSkip+1:0,m.rowSkip>0?m.rowTake:m.rowTake+1)),"columns"===D&&m.columnTake&&(Y=(0,de.WU)("Subset({0}, {1}, {2})",Y,m.columnSkip>0?m.columnSkip+1:0,m.columnSkip>0?m.columnTake:m.columnTake+1));var j="[DX_".concat(D,"]");k.push(f(Y,O,j)),m.totalsOnly&&k.push(f("COUNT(".concat(j,")"),O,"[DX_".concat(D,"_count]"),"member"))}return"columns"===D&&F.length&&!m.skipValues&&k.push(q),(0,de.WU)("{0} DIMENSION PROPERTIES PARENT_UNIQUE_NAME,HIERARCHY_UNIQUE_NAME, MEMBER_VALUE ON {1}",h(k),D)}function g(m){var D=[];return(0,Z.S6)(m,(F,O)=>{var G,{dataField:R}=O,B=[],z=O.filterValues||[];O.hierarchyName&&(0,H.kE)(O.groupIndex)||((0,Z.S6)(z,($,W)=>{var Q="".concat(R,".").concat(c(Array.isArray(W)?W[W.length-1]:W,R));"exclude"===O.filterType&&(B.push("".concat(Q,".parent")),Q="Descendants(".concat(Q,")")),B.push(Q)}),z.length&&(G=(0,de.WU)("{{0}}",B.join(",")),"exclude"===O.filterType&&(G="Except(".concat(l(O),",").concat(G,")")),D.push(G)))}),D.length?h(D):""}function x(m,D,F,O){var R="[".concat(O,"]");return(0,Z.S6)([m,D,F],(B,z)=>{z&&(R=(0,de.WU)("(SELECT {0} FROM {1})","".concat(z,"on 0"),R))}),R}function A(m,D,F,O,R,B,z){var G=arguments.length>7&&void 0!==arguments[7]?arguments[7]:{},$="",W="".concat(D.length?"with ".concat(D.join(" ")):""," ");if(m.length){var Q;if(G.totalsOnly){var k=[];O.length&&k.push("[DX_rows_count]"),F.length&&k.push("[DX_columns_count]"),Q="{".concat(k.join(","),"} on columns")}else Q=m.join(",");$=W+(0,de.WU)(a,x(g(F),g(O),g(R||[]),z),B.length?(0,de.WU)("WHERE ({0})",B.join(",")):"",Q)}return $}function _(m,D){return(0,Z.UI)(D,F=>((0,H.HD)(F.expression)&&f(F.expression,m,F.dataField,"member"),F.dataField))}function y(m,D,F,O){(0,Z.S6)(O,(R,B)=>{var z=D[F][R];(!z.hierarchyName||z.hierarchyName!==D[F][R+1].hierarchyName)&&m.push("".concat(z.dataField,".").concat(c(B,z.dataField)))})}function V(m,D,F){var O=m.columns||[],R=m.rows||[],B=m.values&&m.values.length?m.values:[{dataField:"[Measures]"}],z=[],G=[],$=[],W=_(G,B);return F.measureCount=m.skipValues?1:B.length,F.visibleLevels={},m.headerName&&m.path&&y(z,m,m.headerName,m.path),m.headerName&&m.oppositePath&&y(z,m,"rows"===m.headerName?"columns":"rows",m.oppositePath),(O.length||W.length)&&$.push(v(m,"columns",W,G,F)),R.length&&$.push(v(m,"rows",W,G,F)),A($,G,O,R,m.filters,z,D,m)}function N(m,D,F){(0,Z.S6)(F,(O,R)=>{var B=D[O];B.hierarchyName&&(D[O+1]||{}).hierarchyName===B.hierarchyName||m.push("".concat(B.dataField,".").concat(c(R,B.dataField)))})}function U(m){return parseInt(m,10)}function C(m,D){return o((m.getElementsByTagName(D)||[])[0])}function o(m){return m&&(m.textContent||m.text||m.innerHTML)||""}function u(m,D,F){for(var O=[],R=[],B=0,z=[],G=m.getElementsByTagName("Cell"),$={},W=0;W<G.length;W+=1){var Q=G[W],k=Q.getElementsByTagName("Value")[0],q=k&&k.getElementsByTagName("Error")||[],ee=0===q.length?o(k):"#N/A",Y=parseFloat(ee),j=ee-Y+1>0,re=U(Q.getAttribute("CellOrdinal"));q.length&&($[o(q[0].getElementsByTagName("ErrorCode")[0])]=o(q[0].getElementsByTagName("Description")[0])),z[re]={value:j?Y:ee||null}}return(0,Z.S6)(D[1],()=>{var oe=[];O.push(oe),(0,Z.S6)(D[0],()=>{0==B%F&&oe.push(R=[]),R.push(z[B]?z[B].value:null),B+=1})}),Object.keys($).forEach(oe=>{rt.R0.log("W4002",$[oe])}),O}function c(m,D){return m&&(m=(0,H.HD)(m)&&m.includes("&")?m:"[".concat(m,"]"),D&&0===m.indexOf("".concat(D,"."))&&(m=m.slice(D.length+1,m.length))),m}function p(m,D,F,O){var R=m[D];return R||(m[D]=R={}),!(0,H.$K)(R.value)&&F&&(R.text=F.caption,R.value=F.value,R.key=D||"",R.levelName=F.levelName,R.hierarchyName=F.hierarchyName,R.parentName=F.parentName,R.index=O,R.level=F.level),R}function w(m,D){var F=[],O=m.children&&(m.children.length?m.children:Object.keys(m.children.grandTotalHash||{}).reduce((G,$)=>G.concat(m.children.grandTotalHash[$].children),[])),R=O&&O[0];if(R&&(D[R.hierarchyName]&&D[R.hierarchyName].includes(R.levelName)||!D[R.hierarchyName]||0===R.level)){var B=O.filter(G=>G.hierarchyName===R.hierarchyName);return B.grandTotalHash=O.grandTotalHash,B}if(R)for(var z=0;z<O.length;z+=1)O[z].hierarchyName===R.hierarchyName&&F.push.apply(F,w(O[z],D));return F}function S(m,D,F,O){var R=[];(0,Z.S6)(D,(G,$)=>{var W={children:R},Q=(0,H.$K)(F)?Math.floor(G/F):G;(0,Z.S6)($,(k,q)=>{W=function(ee,Y,j){var re=j.children=j.children||[],oe=re.hash=re.hash||{},ne=re.grandTotalHash=re.grandTotalHash||{};Y.parentName&&(re=(j=p(oe,Y.parentName)).children=j.children||[]);var se=p(oe,Y.name,Y,ee);return Y.hasValue&&!se.added&&(se.index=ee,se.added=!0,re.push(se)),j.value&&j.parentName||!Y.parentName?ne[j.name]&&delete ne[Y.parentName]:ne[Y.parentName]=j,se}(Q,q,W)})});var B={children:R};B.children=w(B,O);var z=function(G,$){var W;if(1===G.children.length&&""===G.children[0].parentName){W=G.children[0].index;var{grandTotalHash:Q}=G.children;G.children=G.children[0].children||[],G.children.grandTotalHash=Q,G.children=w(G,$)}else 0===G.children.length&&(W=0);return W}(B,O);return ce(B.children,G=>{var $=G[0],W=w($,O);W.length?$.children=W:delete $.children,delete $.levelName,delete $.hierarchyName,delete $.added,delete $.parentName,delete $.level},!0),(0,Z.S6)(B.children||[],(G,$)=>{m.push($)}),z}function E(m){var D=m.getElementsByTagName("soap:Fault"),F=m.getElementsByTagName("Fault"),O=(0,M.Z)([].slice.call(F.length?F:D)).find("Error");if(O.length){var R=O.attr("Description"),B=new rt.R0.Error("E4000",R);return rt.R0.log("E4000",R),B}return null}function b(m,D,F,O){var R=[],B="MEASURE"===D,z=B?"MEASUREGROUP_NAME":"".concat(D,"_DISPLAY_FOLDER");return(0,Z.S6)(m.getElementsByTagName("row"),(G,$)=>{var W="LEVEL"===D?C($,"HIERARCHY_UNIQUE_NAME"):void 0,Q=C($,"LEVEL_NUMBER"),k=C($,z);if(B&&(k=O[k]||k),("0"!==Q||"true"!==C($,"".concat(D,"_IS_VISIBLE")))&&"2"!==C($,"DIMENSION_TYPE")){var q=B?"DX_MEASURES":C($,"DIMENSION_UNIQUE_NAME"),ee=C($,"".concat(D,"_UNIQUE_NAME"));R.push({dimension:F.names[q]||q,groupIndex:Q?U(Q)-1:void 0,dataField:ee,caption:C($,"".concat(D,"_CAPTION")),hierarchyName:W,groupName:W,displayFolder:k,isMeasure:B,isDefault:!!F.defaultHierarchies[ee]})}}),R}function I(m){var D=(m=m.replace(/_x(....)_/g,(F,O)=>String.fromCharCode(parseInt(O,16)))).match(/\[.+?\]/gi);return D&&D.length&&(m=D[D.length-1]),m.replace(/\[/gi,"").replace(/\]/gi,"").replace(/\$/gi,"").replace(/\./gi," ")}function P(m,D){return D=(0,M.Z)("<div>").text(D).html(),t(m,(0,de.WU)('<Envelope xmlns="http://schemas.xmlsoap.org/soap/envelope/"><Body><Execute xmlns="urn:schemas-microsoft-com:xml-analysis"><Command><Statement>{0}</Statement></Command><Properties><PropertyList><Catalog>{1}</Catalog><ShowHiddenCubes>True</ShowHiddenCubes><SspropInitAppName>Microsoft SQL Server Management Studio</SspropInitAppName><Timeout>3600</Timeout>{2}</PropertyList></Properties></Execute></Body></Envelope>',D,m.catalog,r()))}return{ctor(m){this._options=m},getFields(){var m=this._options,{catalog:D}=m,{cube:F}=m,O=r(),R=t(m,(0,de.WU)(e,D,F,"MDSCHEMA_DIMENSIONS",O)),B=t(m,(0,de.WU)(e,D,F,"MDSCHEMA_MEASURES",O)),z=t(m,(0,de.WU)(e,D,F,"MDSCHEMA_HIERARCHIES",O)),G=t(m,(0,de.WU)(e,D,F,"MDSCHEMA_LEVELS",O)),$=new J.BH;return(0,J.gx)(R,B,z,G).then((W,Q,k,q)=>{t(m,(0,de.WU)(e,D,F,"MDSCHEMA_MEASUREGROUPS",O)).done(ee=>{var le,Y=(le={names:{},defaultHierarchies:{}},(0,Z.S6)((0,M.Z)(W).find("row"),function(){var fe=(0,M.Z)(this),ct="2"===fe.children("DIMENSION_TYPE").text()?"DX_MEASURES":fe.children("DIMENSION_UNIQUE_NAME").text();le.names[ct]=fe.children("DIMENSION_CAPTION").text(),le.defaultHierarchies[fe.children("DEFAULT_HIERARCHY").text()]=!0}),le),j=b(k,"HIERARCHY",Y),re=b(q,"LEVEL",Y),oe=function(ve){var le={};return(0,Z.S6)(ve.getElementsByTagName("row"),(fe,_e)=>{le[C(_e,"MEASUREGROUP_NAME")]=C(_e,"MEASUREGROUP_CAPTION")}),le}(ee),ne=b(Q,"MEASURE",Y,oe).concat(j),se={};(0,Z.S6)(re,(ve,le)=>{se[le.hierarchyName]=se[le.hierarchyName]||[],se[le.hierarchyName].push(le)}),(0,Z.S6)(j,(ve,le)=>{se[le.dataField]&&se[le.dataField].length>1&&(le.groupName=le.hierarchyName=le.dataField,ne.push.apply(ne,se[le.hierarchyName]))}),$.resolve(ne)}).fail($.reject)}).fail($.reject),$},load(m){var B,D=new J.BH,F=this._options,O={skipValues:m.skipValues},R=V(m,F.cube,O);(m.rowSkip||m.rowTake||m.columnTake||m.columnSkip)&&(B=V((0,X.l)({},m,{totalsOnly:!0,rowSkip:null,rowTake:null,columnSkip:null,columnTake:null}),F.cube,{}));var z=()=>{R?(0,J.gx)(P(F,R),B&&P(F,B)).done((G,$)=>{var W=E(G)||$&&E($);if(W)D.reject(W);else{var Q=function T(m,D){var F={columns:[],rows:[]},{measureCount:O}=D,R=function(B,z){var G=[];for((0,Z.S6)(B.getElementsByTagName("Axis"),($,W)=>{var Q=W.getAttribute("name"),k=[],q=0;0===Q.indexOf("Axis")&&(0,H.kE)(U(Q.substr(4)))&&(G.push(k),(0,Z.S6)(W.getElementsByTagName("Tuple"),(ee,Y)=>{var _e,j=Y.childNodes,re=0,oe=[],ne=z?j.length:j.length-1;1===G.length&&(ne-=1),k.push(oe);for(var ve=ne;ve>=0;ve-=1){var le=j[ve],fe=U(C(le,"LNum"));oe[ve]={caption:C(le,"Caption"),value:(_e=C(le,"MEMBER_VALUE"),(0,H.kE)(_e)?parseFloat(_e):_e),level:fe,index:q++,hasValue:!(re||!fe&&0!==ve),name:C(le,"UName"),hierarchyName:j[ve].getAttribute("Hierarchy"),parentName:C(le,"PARENT_UNIQUE_NAME"),levelName:C(le,"LName")},re+=fe}}))});G.length<2;)G.push([[{level:0}]]);return G}(m,D.skipValues);return F.grandTotalColumnIndex=S(F.columns,R[0],O,D.visibleLevels),F.grandTotalRowIndex=S(F.rows,R[1],void 0,D.visibleLevels),F.values=u(m,R,O),F}(G,O);$&&function(k,q,ee){var Y=[],j=q.columns||[],re=q.rows||[];j.length&&Y.push({}),re.length&&Y.push({});var oe=u(ee,[[{}],[{},{}]],1);if(!j.length&&re.length&&(k.rowCount=Math.max(oe[0][0][0]-1,0)),!re.length&&j.length&&(k.columnCount=Math.max(oe[0][0][0]-1,0)),re.length&&j.length&&(k.rowCount=Math.max(oe[0][0][0]-1,0),k.columnCount=Math.max(oe[1][0][0]-1,0)),void 0!==k.rowCount&&q.rowTake){k.rows=[...Array(q.rowSkip)].concat(k.rows),k.rows.length=k.rowCount;for(var ne=0;ne<k.rows.length;ne+=1)k.rows[ne]=k.rows[ne]||{}}if(void 0!==k.columnCount&&q.columnTake){k.columns=[...Array(q.columnSkip)].concat(k.columns),k.columns.length=k.columnCount;for(var se=0;se<k.columns.length;se+=1)k.columns[se]=k.columns[se]||{}}}(Q,m,$),D.resolve(Q)}}).fail(D.reject):D.resolve({columns:[],rows:[],values:[],grandTotalColumnIndex:0,grandTotalRowIndex:0})};return m.delay?setTimeout(z,m.delay):z(),D},supportPaging:()=>!0,getDrillDownItems(m,D){var F=new J.BH,O=this._options,R=function(B,z,G){var $=B.columns||[],W=B.rows||[],k=[],q=[],ee=[],Y=_(q,B.values&&B.values.length?B.values:[{dataField:"[Measures]"}]),{maxRowCount:j}=G,re=G.customColumns||[],oe=re.length>0?" return ".concat(re.join(",")):"";N(k,$,G.columnPath||[]),N(k,W,G.rowPath||[]),($.length||Y.length)&&ee.push(["".concat(Y[G.dataIndex]||Y[0]," on 0")]);var ne=A(ee,q,$,W,B.filters,k,z);return ne&&"drillthrough".concat(j>0?" maxrows ".concat(j):"").concat(ne).concat(oe)}(m,O.cube,D);return R?(0,J.gx)(P(O,R)).done(B=>{var z=E(B);z?F.reject(z):F.resolve(function(G){for(var $=G.getElementsByTagName("row"),W=[],Q={},k=0;k<$.length;k+=1){for(var q=$[k].childNodes,ee={},Y=0;Y<q.length;Y+=1){var{tagName:j}=q[Y];ee[Q[j]=Q[j]||I(j)]=o(q[Y])}W.push(ee)}return W}(B))}).fail(F.reject):F.resolve([]),F},key:me.ZT,filter:me.ZT}}()).include(_t)};function Ga(e,a,t,r){var n=a[t]||[],h=[];Ye(e[t],(s,d)=>{var v=h[d]=h[d]||function Zi(e,a,t,r,n){var l=n?"asc":e.sortOrder,h=function Bi(e,a){var t="text";return"none"===e?t="index":(a||"displayText"!==e)&&(t="value"),t}(e.sortBy,n),s=e.sortingMethod?function(v,g){return e.sortingMethod(v,g)}:Ke(v=>v[h]),d=!n&&function Hi(e,a,t,r){var{values:n}=a,l=Ee(t.values,e.sortBySummaryField),h="rows"===r,d=h?a.grandTotalRowIndex:a.grandTotalColumnIndex,f=e.sortBySummaryPath||[],v=f.length?function Ni(e,a){var t=null,r=(a||[]).join(".");return r.length&&ce(e,n=>{var l=n[0],h=ge(n).join("."),s=(0,Z.UI)(n,d=>d.text).reverse().join(".");if(r===h||l.key&&s===r)return t=n[0].index,!1}),t}(h?a.columns:a.rows,f):d;if(n&&n.length&&l>=0&&(0,H.$K)(v))return function(g){var _=((n[h?g.index:v]||[[]])[h?v:g.index]||[])[l];return(0,H.$K)(_)?_:null}}(e,a,t,r),f=d&&Ke(d);return function(v,g){var x=f&&f(v,g)||s(v,g);return"desc"===l?-x:x}}(n[d]||{},e,a,t,r);s.sort(v)},a.headerName===t?a.path.length:0)}var Pt={row:"rows",column:"columns",data:"values",filter:"filters"},Ft=["area","areaIndex","sortOrder","filterType","filterValues","sortBy","sortBySummaryField","sortBySummaryPath","expanded","summaryType","summaryDisplayMode"],It=["format","selector","customizeText","caption"],Va=It.concat(["allowSorting","allowSortingBySummary","allowFiltering","allowExpandAll"]);function Lt(e,a){var t=e._initProperties||{};(0,Z.S6)(a,(r,n)=>{Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})}function Ot(e,a){Lt(e,a),(0,H.$K)(e.caption)||Ce(e,"caption",function Gi(e){var a=e.dataField||e.groupName||"",t=(e.summaryType||"").toLowerCase();return(0,H.HD)(e.groupInterval)&&(a+="_".concat(e.groupInterval)),t&&"custom"!==t?(t=t.replace(/^./,t[0].toUpperCase()),a.length&&(t=" (".concat(t,")"))):t="",(0,pi.MI)(a)+t}(e))}function ki(e){return e.rows.length||e.columns.length||e.values.length}var Rt=ye.Z.inherit(function(){var e=function(o,u){if(o._cacheByPath)return o._cacheByPath[u.join(".")]||null},a=function o(u,c){var p,S,w=-1;if(u)for(p=0;p<u.length;p+=1)void 0!==(S=u[p]).index&&(w=Math.max(w,S.index)),S.children?w=Math.max(w,o(S.children)):S.collapsedChildren&&(w=Math.max(w,o(S.collapsedChildren)));return(0,H.$K)(c)&&(w=Math.max(w,c)),w},t=function(o,u,c,p){var E,w=a(c)+1,S=a(o,p)+1,T=[],b=!1,I=new J.BH;if(u.children&&u.children.length===c.length)for(var P=0;P<c.length;P+=1){var m=c[P];void 0!==m.index&&(void 0===u.children[P].index?(m.index=T[m.index]=S++,u.children[P]=m):T[m.index]=u.children[P].index)}else{for(b=!0,E=0;E<w;E+=1)T[E]=S++;u.children=c}return(0,J.gx)(xe(u.children,D=>{b&&(D[0].index=T[D[0].index])})).done(()=>{I.resolve(T)}),I},r=function(o,u,c){var p=new J.BH,w=c>=0&&a(o,c)+1,S=[];return(0,J.gx)(xe(o,E=>{delete E[0].collapsedChildren})).done(()=>{(0,J.gx)(xe(u,(E,T)=>{var b=E[0];if(b.index>=0){var I=e(o,ge(E));if(I&&I.index>=0)S[b.index]=I.index;else if(w){var P=ge(E.slice(1));I=e(o,P);var m=P.length?I&&I.children:o;m&&(m[T]=b,b.index=S[b.index]=w++)}}})).done(()=>{p.resolve(S)})}),p},n=function(o,u,c,p){var w,S,E,T,b,I,P=o.values;if(u)for(w=0;w<u.length;w+=1)if(E=u[w],(0,H.$K)(b=c[w])||(b=o.grandTotalRowIndex),E&&(0,H.$K)(b))for(P[b]||(P[b]=[]),S=0;S<E.length;S+=1)T=E[S],(0,H.$K)(I=p[S])||(I=o.grandTotalColumnIndex),(0,H.$K)(T)&&(0,H.$K)(I)&&(P[b][I]=T)};function l(o,u){return new(o.remoteOperations||o.paginate?La:Sa)((0,X.l)((0,yt.aN)(o),{onChanged:null,onLoadingChanged:null,onProgressChanged:u}))}function h(o,u,c,p){var w=[],S=u&&u[c]||[],E=p&&p[c]||[];return ce(o[c],T=>{var b=T[0],I=ge(T);b.children&&S[I.length-1]&&!S[I.length-1].expanded&&I.length<S.length&&(!p||function(P,m,D){for(var F=0;F<D;F+=1)if(!P[F]||!m[F]||P[F].index!==m[F].index)return!1;return!0}(S,E,I.length))&&w.push(I.slice())},!0),w}function s(o,u,c,p){return u?(0,Z.S6)(p,(w,S)=>{if(c)o[S]=u[S];else{if(("summaryType"===S||"summaryDisplayMode"===S)&&void 0===u[S])return;Ce(o,S,u[S])}}):Lt(o,p),o}function d(o,u){var c=[];return(0,Z.S6)(o,(p,w)=>{c.push(s({dataField:w.dataField,name:w.name},w,!0,u))}),c}function f(o){return o.name?o.name:"".concat(o.dataField)}function v(o,u){var c=[];return(0,Z.S6)(o||[],(p,w)=>{f(w)===u&&c.push(w)}),c}function g(o,u){o=o||[];var p,c={};return(0,Z.S6)(u,(w,S)=>{p=f(S),c[p]||(c[p]=v(u,f(S)))}),(0,Z.S6)(c,(w,S)=>{var E;E=(E=v(o,w))||[],(0,Z.S6)(S,(b,I)=>{s(I,E[b],!1,Ft),Ot(I,It)})}),u}function x(o){o.sort((u,c)=>u.areaIndex-c.areaIndex||u.groupIndex-c.groupIndex)}function A(o,u){var c=o.groupName||"";return(o.dataField||c)+(o.groupInterval?c+o.groupInterval:"NOGROUP")+(u?"":c)}function _(o,u,c){var T,p=[],w={},S={},E=mt(o);return u?((0,Z.S6)(u,(T,b)=>{w[A(b,c)]=b}),(0,Z.S6)(o,(T,b)=>{var m,I=A(b,c),P=w[I]||S[I];P?(P._initProperties&&Lt(P,Va),m=(0,X.l)({},P,b,{_initProperties:null})):w[I]=m=b,!m.dataType&&E[b.dataField]&&(m.dataType=E[b.dataField]),delete w[I],S[I]=P,p.push(m)}),c&&(0,Z.S6)(w,(T,b)=>{p.push(b)})):p=o,p.push.apply(p,[]),(T=p).forEach(b=>{if(b.groupName&&b.groupInterval&&void 0===b.groupIndex){var I=T.filter(P=>P.groupName===b.groupName&&(0,H.kE)(P.groupIndex)).map(P=>P.groupIndex).reduce((P,m)=>Math.max(P,m),-1);b.groupIndex=I+1}}),p}function y(o){var w,u=new J.BH,c=o._store,p=c&&c.getFields(o._fields);return(0,J.gx)(p).done(S=>{o._storeFields=S,w=_(o._fields,S,o._retrieveFields),u.resolve(w)}).fail(u.reject),u}function V(o,u,c){return xe(o[c],p=>{var w=p[0];w.text=w.text||Qe(w.value,u[c][ge(p).length-1])})}function N(o,u){return(0,J.gx)(V(u,o,"columns"),V(u,o,"rows"))}function U(o){var u=new J.BH,c={};return(0,J.gx)(xe(o,p=>{var w=ge(p).join(".");c[w]=p[0]})).done(u.resolve),o._cacheByPath=c,u}function C(o,u){var c=[];return(0,Z.S6)(o,function(){var p,w,S;p=this,S="data"===(w=u)||!1!==p.visible,p.area===w&&!(0,H.$K)(p.groupIndex)&&S&&c.push(this)}),c}return{ctor(o){o=o||{},this._eventsStrategy=new vi.m(this);var p,w,S,E,u=this,c=(w=p=>{u._eventsStrategy.fireEvent("progressChanged",[p])},(0,H.PO)(p=o)&&p.load?S=l(p,w):(p&&!p.store&&(p={store:p}),"xmla"===(E=p.store).type?S=new Mi.XmlaStore(E):(0,H.PO)(E)&&E.type||E instanceof mi.Z||Array.isArray(E)?S=l(p,w):E instanceof ye.Z&&(S=E)),S);u._store=c,u._paginate=!!o.paginate,u._pageSize=o.pageSize||40,u._data={rows:[],columns:[],values:[]},u._loadingCount=0,u._isFieldsModified=!1,(0,Z.S6)(["changed","loadError","loadingChanged","progressChanged","fieldsPrepared","expandValueChanging"],(p,w)=>{var S="on".concat(w[0].toUpperCase()).concat(w.slice(1));Object.prototype.hasOwnProperty.call(o,S)&&this.on(w,o[S])}),u._retrieveFields=!(0,H.$K)(o.retrieveFields)||o.retrieveFields,u._fields=o.fields||[],u._descriptions=o.descriptions?(0,X.l)(u._createDescriptions(),o.descriptions):void 0,c||(0,X.l)(!0,u._data,o.store||o)},getData(){return this._data},getAreaFields(o,u){var c=[];return u||"data"===o?x(c=C(this._fields,o)):c=(this._descriptions||{})[Pt[o]]||[],c},fields(o){return o&&(this._fields=_(o,this._storeFields,this._retrieveFields),this._fieldsPrepared(this._fields)),this._fields},field(o,u){var w,c=this._fields,p=c&&c[(0,H.kE)(o)?o:Ee(c,o)];return p&&u&&((0,Z.S6)(u,(S,E)=>{var T=!Ft.includes(S);if(Ce(p,S,E,T),"sortOrder"===S){w=p.levels||[];for(var b=0;b<w.length;b+=1)w[b][S]=E}}),Ot(p,It),this._descriptions=this._createDescriptions(p),this._isFieldsModified=!0,this._eventsStrategy.fireEvent("fieldChanged",[p])),p},getFieldValues(o,u,c){var b,p=this,w=this._fields&&this._fields[o],S=this.store(),E=[],T={columns:E,rows:[],values:this.getAreaFields("data"),filters:u?this._fields.filter(P=>P!==w&&P.area&&P.filterValues&&P.filterValues.length):[],skipValues:!0},I=new J.BH;return c&&(b=c.searchValue,T.columnSkip=c.skip,T.columnTake=c.take),w&&S?((0,Z.S6)(w.levels||[w],function(){E.push((0,X.l)({},this,{expanded:!0,filterValues:null,sortOrder:"asc",sortBySummaryField:null,searchValue:b}))}),S.load(T).done(P=>{T.columnSkip&&(P.columns=P.columns.slice(T.columnSkip)),T.columnTake&&(P.columns=P.columns.slice(0,T.columnTake)),N(T,P),T.columnTake||p._sort(T,P),I.resolve(P.columns)}).fail(I)):I.reject(),I},reload(){return this.load({reload:!0})},filter(){var o=this._store;return o.filter.apply(o,arguments)},load:function(o){var u=this,c=new J.BH;function p(){u._delayedLoadTask=void 0,u._descriptions?u._loadCore(o,c):(0,J.gx)(y(u)).done(w=>{u._fieldsPrepared(w),u._loadCore(o,c)}).fail(c.reject).fail(u._loadErrorHandler)}return o=o||{},u.beginLoading(),c.fail(w=>{u._eventsStrategy.fireEvent("loadError",[w])}).always(()=>{u.endLoading()}),u.store()?u._delayedLoadTask=(0,me.Wi)(p):p(),c},createDrillDownDataSource(o){return this._store.createDrillDownDataSource(this._descriptions,o)},_createDescriptions(o){var u=this.fields(),c={rows:[],columns:[],values:[],filters:[]};(0,Z.S6)(["row","column","data","filter"],(w,S)=>{(0,gi.Sq)(C(u,S),"areaIndex",o)}),(0,Z.S6)(u||[],(w,S)=>{var P,T=c[Pt[S.area]],{groupName:b}=S;b&&!(0,H.kE)(S.groupIndex)&&(S.levels=(P=S,u.filter(m=>m.groupName===P.groupName&&(0,H.kE)(m.groupIndex)&&!1!==m.visible).map(m=>(0,X.l)(m,{areaIndex:P.areaIndex,area:P.area,expanded:(0,H.$K)(m.expanded)?m.expanded:P.expanded,dataField:m.dataField||P.dataField,dataType:m.dataType||P.dataType,sortBy:m.sortBy||P.sortBy,sortOrder:m.sortOrder||P.sortOrder,sortBySummaryField:m.sortBySummaryField||P.sortBySummaryField,sortBySummaryPath:m.sortBySummaryPath||P.sortBySummaryPath,visible:m.visible||P.visible,showTotals:(0,H.$K)(m.showTotals)?m.showTotals:P.showTotals,showGrandTotals:(0,H.$K)(m.showGrandTotals)?m.showGrandTotals:P.showGrandTotals})).sort((m,D)=>m.groupIndex-D.groupIndex))),!(!T||b&&(0,H.kE)(S.groupIndex)||!1===S.visible&&"data"!==S.area&&"filter"!==S.area)&&(S.levels&&T!==c.filters&&T!==c.values?(T.push.apply(T,S.levels),S.filterValues&&S.filterValues.length&&c.filters.push(S)):T.push(S))}),(0,Z.S6)(c,(w,S)=>{x(S)});var p={};return(0,Z.S6)(c.values,(w,S)=>{var E=S.calculateSummaryValue;(0,H.mf)(E)&&E(Dt_createMockSummaryCell(c,u,p))}),c},_fieldsPrepared(o){this._fields=o,(0,Z.S6)(o,(p,w)=>{w.index=p,Ot(w,Va)});var u=d(o,["caption"]);this._eventsStrategy.fireEvent("fieldsPrepared",[o]);for(var c=0;c<o.length;c+=1)o[c].caption!==u[c].caption&&Ce(o[c],"caption",o[c].caption,!0);this._descriptions=this._createDescriptions()},isLoading(){return this._loadingCount>0},state(o,u){var c=this;return arguments.length?(o=(0,X.l)({rowExpandedPaths:[],columnExpandedPaths:[]},o),void(c._descriptions?(c._fields=g(o.fields,c._fields),c._descriptions=c._createDescriptions(),!u&&c.load(o)):(c.beginLoading(),(0,J.gx)(y(c)).done(p=>{c._fields=g(o.fields,p),c._fieldsPrepared(p),!u&&c.load(o)}).always(()=>{c.endLoading()})))):{fields:d(c._fields,Ft),columnExpandedPaths:h(c._data,c._descriptions,"columns",c._lastLoadOptions),rowExpandedPaths:h(c._data,c._descriptions,"rows",c._lastLoadOptions)}},beginLoading(){this._changeLoadingCount(1)},endLoading(){this._changeLoadingCount(-1)},_changeLoadingCount(o){var u=this.isLoading();this._loadingCount+=o;var c=this.isLoading();u^c&&this._eventsStrategy.fireEvent("loadingChanged",[c])},_hasPagingValues(o,u,c){var p="".concat(u,"Take"),w="".concat(u,"Skip"),{values:S}=this._data,E=this._data["".concat(u,"s")],T="row"===u?"column":"row",b=[];if(o.path&&o.area===u){var I=e(E,o.path);if(!(E=I&&I.children))return!1}if(o.oppositePath&&o.area===T){var P=e(E,o.oppositePath);if(!(E=P&&P.children))return!1}for(var m=o[w];m<o[w]+o[p];m+=1)E[m]&&b.push(E[m].index);return b.every(D=>{if(void 0!==D)return"row"===u?(S[D]||[])[c]:(S[c]||[])[D]})},_processPagingCacheByArea(o,u,c){var T,p="".concat(c,"Take"),w="".concat(c,"Skip"),S=this._data["".concat(c,"s")],E="row"===c?"column":"row";if(o[p]){if(o.path&&o.area===c){var b=e(S,o.path);S=b&&b.children||[]}if(o.oppositePath&&o.area===E){var I=e(S,o.oppositePath);S=I&&I.children||[]}do{if((T=S[o[w]])&&void 0!==T.index){if(!this._hasPagingValues(o,E,T.index))break;o[w]++,o[p]--}}while(T&&void 0!==T.index&&o[p]);if(o[p]){var P=Math.floor(o[w]/u)*u,m=Math.ceil((o[w]+o[p])/u)*u;o[w]=P,o[p]=m-P}}},_processPagingCache(o){var u=this._pageSize;if(!(u<0))for(var c=0;c<o.length;c+=1)this._processPagingCacheByArea(o[c],u,"row"),this._processPagingCacheByArea(o[c],u,"column")},_loadCore(o,u){var c=this,p=this._store,w=this._descriptions,S=o.reload||this.paginate()&&c._isFieldsModified,E=this.paginate(),T=Pt[o.area];if(o=o||{},p){(0,X.l)(o,w),o.columnExpandedPaths=o.columnExpandedPaths||h(this._data,o,"columns",c._lastLoadOptions),o.rowExpandedPaths=o.rowExpandedPaths||h(this._data,o,"rows",c._lastLoadOptions),E&&(o.pageSize=this._pageSize),T&&(o.headerName=T),c.beginLoading(),u.always(()=>{c.endLoading()});var b=[o];if(c._eventsStrategy.fireEvent("customizeStoreLoadOptions",[b,S]),S||c._processPagingCache(b),!(b=b.filter(P=>!(P.rows.length&&0===P.rowTake||P.columns.length&&0===P.columnTake))).length)return void c._update(u);var I=b.map(P=>p.load(P));J.gx.apply(null,I).done(function(){for(var P=arguments,m=0;m<P.length;m+=1){var D=b[m],F=P[m],O=m===P.length-1;D.path?c.applyPartialDataSource(D.area,D.path,F,!!O&&u,D.oppositePath):E&&!S&&ki(c._data)?c.mergePartialDataSource(F,!!O&&u):((0,X.l)(c._data,F),c._lastLoadOptions=D,c._update(!!O&&u))}}).fail(u.reject)}else c._update(u)},_sort(o,u,c){this._store&&!this._paginate&&function Na(e,a,t){Ga(a,e,"rows",t),Ga(a,e,"columns",t)}(o,u,c)},sortLocal(){this._sort(this._descriptions,this._data),this._eventsStrategy.fireEvent("changed")},paginate(){return this._paginate&&this._store&&this._store.supportPaging()},isEmpty(){var o=this.getAreaFields("data").filter(c=>!1!==c.visible),u=this.getData();return!o.length||!u.values.length},_update(o){var u=this,c=u._descriptions,p=u._data,w=c.values,S=function Vi(e){return e.some(a=>a.summaryDisplayMode||a.calculateSummaryValue)}(w);return(0,J.gx)(N(c,p),U(p.rows),U(p.columns)).done(()=>{S&&(u._sort(c,p,S),!u.isEmpty()&&Dt_applyDisplaySummaryMode(c,p)),u._sort(c,p),!u.isEmpty()&&function $i(e){return e.some(a=>!!a.runningTotal)}(w)&&Dt_applyRunningTotal(c,p),u._data=p,!1!==o&&(0,J.gx)(o).done(()=>{u._isFieldsModified=!1,u._eventsStrategy.fireEvent("changed"),(0,H.$K)(u._data.grandTotalRowIndex)&&(p.grandTotalRowIndex=u._data.grandTotalRowIndex),(0,H.$K)(u._data.grandTotalColumnIndex)&&(p.grandTotalColumnIndex=u._data.grandTotalColumnIndex)}),o&&o.resolve(u._data)}),o},store(){return this._store},collapseHeaderItem(o,u){var p=e("column"===o?this._data.columns:this._data.rows,u),w=this.getAreaFields(o)[u.length-1];return!(!p||!p.children||(this._eventsStrategy.fireEvent("expandValueChanging",[{area:o,path:u,expanded:!1}]),w&&(w.expanded=!1),p.collapsedChildren=p.children,delete p.children,this._update(),this.paginate()&&this.load(),0))},collapseAll(o){var u=!1,c=this.field(o)||{},p=[this.getAreaFields(c.area).indexOf(c)];c.expanded=!1,c&&c.levels&&(p=[],c.levels.forEach(w=>{p.push(this.getAreaFields(c.area).indexOf(w)),w.expanded=!1})),ce(this._data["".concat(c.area,"s")],w=>{var S=w[0],E=ge(w);S&&S.children&&p.includes(E.length-1)&&(S.collapsedChildren=S.children,delete S.children,u=!0)},!0),u&&this._update()},expandAll(o){var u=this.field(o);u&&u.area&&(u.expanded=!0,u&&u.levels&&u.levels.forEach(c=>{c.expanded=!0}),this.load())},expandHeaderItem(o,u){var p=e("column"===o?this._data.columns:this._data.rows,u);if(p&&!p.children){var w=!!p.collapsedChildren,S={area:o,path:u,expanded:!0,needExpandData:!w};return this._eventsStrategy.fireEvent("expandValueChanging",[S]),w?(p.children=p.collapsedChildren,delete p.collapsedChildren,this._update()):this.store()&&this.load(S),w}return!1},mergePartialDataSource(o,u){var w,S,c=this,p=c._data;o&&o.values&&(o.rows=o.rows||[],o.columns=o.columns||[],w=r(p.rows,o.rows,p.grandTotalColumnIndex),S=r(p.columns,o.columns,p.grandTotalColumnIndex),(0,J.gx)(w,S).done((E,T)=>{(E.length||T.length)&&n(p,o.values,E,T),c._update(u)}))},applyPartialDataSource(o,u,c,p,w){var b,P,m,D,S=this,E=S._data,T="column"===o?E.columns:E.rows,I="column"===o?E.rows:E.columns;c&&c.values&&(c.rows=c.rows||[],c.columns=c.columns||[],b=e(T,u),P=w&&e(I,w),b&&("column"===o?(D=t(T,b,c.columns,E.grandTotalColumnIndex),m=P?t(I,P,c.rows,E.grandTotalRowIndex):r(E.rows,c.rows,E.grandTotalRowIndex)):(m=t(T,b,c.rows,E.grandTotalRowIndex),D=P?t(I,P,c.columns,E.grandTotalColumnIndex):r(E.columns,c.columns,E.grandTotalColumnIndex)),(0,J.gx)(m,D).done((F,O)=>{("row"===o&&F.length||"column"===o&&O.length)&&n(E,c.values,F,O),S._update(p)})))},on(o,u){return this._eventsStrategy.on(o,u),this},off(o,u){return this._eventsStrategy.off(o,u),this},dispose(){var o=this._delayedLoadTask;this._eventsStrategy.dispose(),o&&o.abort(),this._isDisposed=!0},isDisposed(){return!!this._isDisposed}}}()),Mt=Math,Ne=function(e,a,t){e[a]||(e[a]=function(){var r=this._dataSource;return r?r[a].apply(r,arguments):t})};const Ji={DataController:ye.Z.inherit(function(){function e(s,d,f){return"#N/A"===s?f:Qe(s,d)}var a=function(){function f(v,g,x){var _=(v[g]=v[g]||[])[x]={};if(v[g+1]){_.children=v[g+1];for(var y=g+1;y<v.length;y+=1)v[y]=void 0;v.length=g+1}return _}return function(v,g,x,A,_){var C,y=[],V=(C=0,ce(v,o=>{C=Mt.max(C,o.length)}),C||1),N=new J.BH;return function(v,g,x,A,_){var C,o,w,u,c,p,y=x.length,V=(C=v,u=(o=g)&&o.length||0,c=[],p=new J.BH,(0,J.gx)(xe(C,(S,E)=>{var T=S[0],b=ge(S);(w=f(c,b.length,E)).type="D",w.value=T.value,w.path=b,w.text=T.text,w.index=T.index,w.displayText=T.displayText,w.key=T.key,w.isEmpty=T.isEmpty,b.length<u&&(!T.children||0!==T.children.length)&&(w.expanded=!!T.children)})).done(()=>{p.resolve(f(c,0,0).children||[])}),p),{dataFields:N}=_,U=new J.BH;return(0,J.gx)(V).done(C=>{_.notifyProgress(.5),_.showGrandTotals&&C[_.showTotalsPrior?"unshift":"push"]({type:"GT",isEmpty:_.isEmptyGrandTotal});var o=!1===_.showTotals||N.length>0&&N.length===_.hiddenTotals.length,u=N.length>0&&_.hiddenValues.length===N.length;u&&o&&(A=1),(!o||"tree"===_.layout)&&function(c,p,w,S){w=w||S,ce(c,(E,T)=>{var b=E[0];"D"===b.type&&b.expanded&&(!1!==p[E.length-1].showTotals||S)&&(-1!==T&&((E[1]?E[1].children:c)||[]).splice(w?T:T+1,0,(0,X.l)({},b,{children:null,type:"T",expanded:!!w||null,isAdditionalTotal:!0})),w&&(b.expanded=null))})}(C,g,_.showTotalsPrior,"tree"===_.layout),(0,J.gx)(xe(C,c=>{var p=c[0];(!p.children||0===p.children.length)&&(p.depthSize=A-c.length+1)})).done(()=>{y>1&&function(c,p,w){ce(c,S=>{var T,E=S[0];if(!E.children||0===E.children.length)for(E.children=[],T=0;T<p.length;T+=1){var I="T"===E.type,P="D"===E.type;!1===p[T].visible||"GT"===E.type&&w.hiddenGrandTotals.includes(T)||I&&w.hiddenTotals.includes(T)||P&&w.hiddenValues.includes(T)||E.children.push({caption:p[T].caption,path:E.path,type:E.type,value:T,index:E.index,dataIndex:T,isMetric:!0,isEmpty:E.isEmpty&&E.isEmpty[T]})}})}(C,x,_),!_.showEmpty&&function(c){ce([{children:c}],(p,w)=>{var S=p[0],E=(p[1]?p[1].children:c)||[],{isEmpty:T}=S;T&&T.length&&(T=S.isEmpty.filter(b=>b).length===T.length),S&&!S.children&&T&&(E.splice(w,1),function b(I,P){var m=I[P+1];!I[P].children.length&&m&&m.children&&(m.children.splice(m.children.indexOf(I[P]),1),b(I,P+1))}(p,1))})}(C),_.notifyProgress(.75),(0,J.gx)(xe(C,c=>{var p=c[0],{isMetric:w}=p,S=g[c.length-1]||{};if("D"===p.type&&!w&&(p.width=S.width),u&&"D"===p.type){var E=(c[1]?c[1].children:C)||[];E.splice(E.indexOf(p),1)}else p.wordWrapEnabled=w?x[p.dataIndex].wordWrapEnabled:S.wordWrapEnabled,p.isLast=!p.children||!p.children.length,p.isLast&&(0,Z.S6)(_.sortBySummaryPaths,(T,b)=>{if((0,H.$K)(p.dataIndex)||(b=b.slice(0)).pop(),D=(I=c)[0],F=(0,H.HD)((P=b)[0]),O=D.dataIndex>=0?I[1]:D,m=F&&-1!==P[0].indexOf("&[")&&O.key||!O.key?ge(I):(0,Z.UI)(I,R=>R.dataIndex>=0?R.value:R.text).reverse(),"GT"===D.type&&(m=m.slice(1)),m.join("/")===P.join("/"))return p.sorted=!0,!1;var I,P,m,D,F,O}),p.text=function(T,b,I){var{text:P}=T;return(0,H.$K)(T.displayText)?P=T.displayText:(0,H.$K)(T.caption)?P=T.caption:"GT"===T.type&&(P=I.texts.grandTotal),T.isAdditionalTotal&&(P=(0,de.WU)(I.texts.total||"",P)),P}(p,0,_)})).done(()=>{C.length||C.push({}),_.notifyProgress(1),U.resolve(C)})})}),U}(v,g,x,V,_).done(U=>{(function(C,o,u,c,p){var E,w=0,T=[0];ce(o,b=>{var I=b[0];for(E=I.isMetric?u:b.length-1;T.length-1<E;)T.push(T[T.length-1]);w=function(v,g){var _,y,V,N,U;return function(_,y,V,N,U){for(var C=U?N:V;!_[C];)_.push([]);U?_[C].push(y):_[C].unshift(y)}(v,(y=g.lastIndex-g.index||1,V=g.isHorizontal,N=g.isTree,U={type:(_=g.headerItem).type,text:_.text},_.path&&(U.path=_.path),_.width&&(U.width=_.width),(0,H.$K)(_.wordWrapEnabled)&&(U.wordWrapEnabled=_.wordWrapEnabled),_.isLast&&(U.isLast=!0),_.sorted&&(U.sorted=!0),_.isMetric&&(U.dataIndex=_.dataIndex),(0,H.$K)(_.expanded)&&(U.expanded=_.expanded),y>1&&(U[V?"colspan":"rowspan"]=y),_.depthSize&&_.depthSize>1&&(U[V?"rowspan":"colspan"]=_.depthSize),_.index>=0&&(U.dataSourceIndex=_.index),N&&_.children&&_.children.length&&!_.children[0].isMetric&&(U.width=null,U.isWhiteSpace=!0),U),g.index,g.depth,g.isHorizontal),g.headerItem.children&&0!==g.headerItem.children.length?g.lastIndex:g.lastIndex+1}(C,{headerItem:I,index:T[E]||0,lastIndex:w,depth:E,isHorizontal:c,isTree:p}),T.length=E,T.push(w)})})(y,U,V,A,"tree"===_.layout),_.notifyProgress(1),N.resolve(y)}),N}}();function t(s,d){var f=[];return(0,Z.S6)(s,(v,g)=>{var x=Ee(d,g.sortBySummaryField);x>=0&&f.push((g.sortBySummaryPath||[]).concat([x]))}),f}function r(s,d){for(var f=0,v=[],g=0;g<s.length;g+=1)for(var x=0;x<s[g].length;x+=1){var _=g+((s[g][x].rowspan||1)-1);if(v[g]&&(f-=v[g],v[g]=0),!1===d(s[g][x],_,g,x,f))break;v[g+(s[g][x].rowspan||1)]=(v[g+(s[g][x].rowspan||1)]||0)+1,f+=1}}function n(s,d){var f=0,v=[];return ce(s,g=>{var x=g[0],A=ge(g);if(!x.children||!1!==d.showTotals){var _=(0,X.l)(!0,{},x,{visibleIndex:f+=1,path:A});(0,H.$K)(_.index)?v[_.index]=_:v.push(_)}}),v}function l(s,d,f){return new hi.ZP.VirtualScrollController(d,(0,X.l)({hasKnownLastPage:()=>!0,pageCount(){return Mt.ceil(this.totalItemsCount()/this.pageSize())},updateLoading(){},itemsCount(){return this.pageIndex()<this.pageCount()-1?this.pageSize():this.totalItemsCount()%this.pageSize()},items:()=>[],viewportItems:()=>[],onChanged(){},isLoading:()=>s.isLoading(),changingDuration:()=>s._dataSource.paginate()?300:s._changingDuration||0},f))}var h={ctor(s){var d=this,f=d._fireChanged.bind(d);s=d._options=s||{},d.dataSourceChanged=(0,Fe.Z)(),d._dataSource=d._createDataSource(s),s.component&&"virtual"===s.component.option("scrolling.mode")&&(d._rowsScrollController=l(d,s.component,{totalItemsCount:()=>d.totalRowCount(),pageIndex:v=>d.rowPageIndex(v),pageSize:()=>d.rowPageSize(),load(){return d._rowsScrollController.pageIndex()>=this.pageCount()&&d._rowsScrollController.pageIndex(this.pageCount()-1),d._rowsScrollController.handleDataChanged(function(){d._dataSource.paginate()?d._dataSource.load():f.apply(this,arguments)})}}),d._columnsScrollController=l(d,s.component,{totalItemsCount:()=>d.totalColumnCount(),pageIndex:v=>d.columnPageIndex(v),pageSize:()=>d.columnPageSize(),load(){return d._columnsScrollController.pageIndex()>=this.pageCount()&&d._columnsScrollController.pageIndex(this.pageCount()-1),d._columnsScrollController.handleDataChanged(function(){d._dataSource.paginate()?d._dataSource.load():f.apply(this,arguments)})}})),d._stateStoringController=new ui.Z.StateStoringController(s.component).init(),d._columnsInfo=[],d._rowsInfo=[],d._cellsInfo=[],d.expandValueChanging=(0,Fe.Z)(),d.loadingChanged=(0,Fe.Z)(),d.progressChanged=(0,Fe.Z)(),d.scrollChanged=(0,Fe.Z)(),d.load(),d._update(),d.changed=(0,Fe.Z)()},_fireChanged(){var s=new Date;this.changed&&!this._lockChanged&&this.changed.fire(),this._changingDuration=new Date-s},_correctSkipsTakes(s,d,f,v,g,x){var A=f?s+f-1:s;g[v.length]=g[v.length]||0,x[v.length]=x[v.length]||0,A<d?g[v.length]+=1:x[v.length]+=1},_calculatePagingForRowExpandedPaths(s,d,f,v,g){var N,U,C,x=this._rowsInfo,A=Math.min(s.rowSkip+s.rowTake,x.length),{rowExpandedPaths:_}=s,y=[],V={};for(_.forEach((c,p)=>{V[c]=p}),N=0;N<A;N+=1){for(f.length=d.length=y.length+1,U=0;U<x[N].length;U+=1){var o=x[N][U];if("D"===o.type){this._correctSkipsTakes(N,s.rowSkip,o.rowspan,y,d,f);var u=(C=o.path||C)&&C.length>1?V[C.slice(0,-1)]:-1;u>=0&&(v[u]=d[y.length]||0,g[u]=f[y.length]||0),o.rowspan&&y.push(o.rowspan)}}y=y.map(c=>c-1).filter(c=>c>0)}},_calculatePagingForColumnExpandedPaths(s,d,f,v,g){var x={},A={};(0,wt.L)(this._columnsInfo,(_,y)=>{if("D"===_.type&&_.path&&void 0===_.dataIndex){var V=_.colspan||1,N=_.path.slice(0,-1).toString();x[N]=x[N]||0,A[N]=A[N]||0,y+V<=s.columnSkip?x[N]+=1:y<s.columnSkip+s.columnTake&&(A[N]+=1)}}),d[0]=x[""],f[0]=A[""],s.columnExpandedPaths.forEach((_,y)=>{var V=x[_],N=A[_];void 0!==V&&(v[y]=V),void 0!==N&&(g[y]=N)})},_processPagingForExpandedPaths(s,d,f,v){var g=s["".concat(d,"ExpandedPaths")],x=g.map(()=>0),A=g.map(()=>v?s.pageSize:0),_=[],y=[];v||("row"===d?this._calculatePagingForRowExpandedPaths(s,_,y,x,A):this._calculatePagingForColumnExpandedPaths(s,_,y,x,A)),this._savePagingForExpandedPaths(s,d,f,_[0],y[0],x,A)},_savePagingForExpandedPaths(s,d,f,v,g,x,A){var _=s["".concat(d,"ExpandedPaths")];s["".concat(d,"ExpandedPaths")]=[],s["".concat(d,"Skip")]=void 0!==v?v:s["".concat(d,"Skip")],s["".concat(d,"Take")]=void 0!==g?g:s["".concat(d,"Take")];for(var y=0;y<_.length;y+=1)if(A[y]){var V=s.area&&s.area!==d;f.push((0,X.l)({area:d,headerName:"".concat(d,"s")},s,{["".concat(d,"Skip")]:x[y],["".concat(d,"Take")]:A[y],[V?"oppositePath":"path"]:_[y]}))}},_handleCustomizeStoreLoadOptions(s,d){var f=s[0],v=this._rowsScrollController;if(this._dataSource.paginate()&&v){var g=v.pageSize();"rows"===f.headerName?(f.rowSkip=0,f.rowTake=g,f.rowExpandedPaths=[]):(f.rowSkip=v.beginPageIndex()*g,f.rowTake=(v.endPageIndex()-v.beginPageIndex()+1)*g,this._processPagingForExpandedPaths(f,"row",s,d))}var x=this._columnsScrollController;if(this._dataSource.paginate()&&x){var A=x.pageSize();s.forEach(_=>{"columns"===_.headerName?(_.columnSkip=0,_.columnTake=A,_.columnExpandedPaths=[]):(_.columnSkip=x.beginPageIndex()*A,_.columnTake=(x.endPageIndex()-x.beginPageIndex()+1)*A,this._processPagingForExpandedPaths(_,"column",s,d))})}},load(){var s=this,d=this._stateStoringController;d.isEnabled()&&!d.isLoaded()?d.load().always(f=>{f?s._dataSource.state(f):s._dataSource.load()}):s._dataSource.load()},calculateVirtualContentParams(s){var d=this._rowsScrollController,f=this._columnsScrollController;if(d&&f)return d.viewportItemSize(s.virtualRowHeight),d.viewportSize(s.viewportHeight/d.viewportItemSize()),d.setContentItemSizes(s.itemHeights),f.viewportItemSize(s.virtualColumnWidth),f.viewportSize(s.viewportWidth/f.viewportItemSize()),f.setContentItemSizes(s.itemWidths),(0,me.Su)(()=>{f.loadIfNeed(),d.loadIfNeed()}),this.scrollChanged.fire({left:f.getViewportPosition(),top:d.getViewportPosition()}),{contentTop:d.getContentOffset(),contentLeft:f.getContentOffset(),width:f.getVirtualContentSize(),height:d.getVirtualContentSize()}},setViewportPosition(s,d){this._rowsScrollController.setViewportPosition(d||0),this._columnsScrollController.setViewportPosition(s||0)},subscribeToWindowScrollEvents(s){var d;null===(d=this._rowsScrollController)||void 0===d||d.subscribeToWindowScrollEvents(s)},updateWindowScrollPosition(s){var d;null===(d=this._rowsScrollController)||void 0===d||d.scrollTo(s)},updateViewOptions(s){(0,X.l)(this._options,s),this._update()},_handleExpandValueChanging(s){this.expandValueChanging.fire(s)},_handleLoadingChanged(s){this.loadingChanged.fire(s)},_handleProgressChanged(s){this.progressChanged.fire(s)},_handleFieldsPrepared(s){this._options.onFieldsPrepared&&this._options.onFieldsPrepared(s)},_createDataSource(s){var v,d=this,f=s.dataSource;return d._isSharedDataSource=f instanceof Rt,v=d._isSharedDataSource?f:new Rt(f),d._expandValueChangingHandler=d._handleExpandValueChanging.bind(d),d._loadingChangedHandler=d._handleLoadingChanged.bind(d),d._fieldsPreparedHandler=d._handleFieldsPrepared.bind(d),d._customizeStoreLoadOptionsHandler=d._handleCustomizeStoreLoadOptions.bind(d),d._changedHandler=function(){d._update(),d.dataSourceChanged.fire()},d._progressChangedHandler=function(g){d._handleProgressChanged(.8*g)},v.on("changed",d._changedHandler),v.on("expandValueChanging",d._expandValueChangingHandler),v.on("loadingChanged",d._loadingChangedHandler),v.on("progressChanged",d._progressChangedHandler),v.on("fieldsPrepared",d._fieldsPreparedHandler),v.on("customizeStoreLoadOptions",d._customizeStoreLoadOptionsHandler),v},getDataSource(){return this._dataSource},isLoading(){return this._dataSource.isLoading()},beginLoading(){this._dataSource.beginLoading()},endLoading(){this._dataSource.endLoading()},_update(){var w,s=this,d=s._dataSource,f=s._options,v=d.getAreaFields("column"),g=d.getAreaFields("row"),x=d.getAreaFields("data"),A="row"===f.dataFieldArea?x:[],_="row"!==f.dataFieldArea?x:[],y=d.getData(),V=(w=[],(0,Z.S6)(x,(S,E)=>{!1===E.showTotals&&w.push(S)}),w),N=function(p){var w=[];return p.forEach((S,E)=>{(void 0===S.showValues&&!1===S.showTotals||!1===S.showValues)&&w.push(E)}),w}(x),U=function(p,w){var S=[];return(0,Z.S6)(p,(E,T)=>{!1===T.showGrandTotals&&S.push(E)}),0===w.length&&S.length===p.length&&(S=[]),S}(x,v),C=!(x.length>0)||U.length!==x.length,o={isEmptyGrandTotal:y.isEmptyGrandTotalRow,texts:f.texts||{},hiddenTotals:V,hiddenValues:N,hiddenGrandTotals:[],showTotals:f.showRowTotals,showGrandTotals:!1!==f.showRowGrandTotals&&C,sortBySummaryPaths:t(v,x),showTotalsPrior:"rows"===f.showTotalsPrior||"both"===f.showTotalsPrior,showEmpty:!f.hideEmptySummaryCells,layout:f.rowHeaderLayout,fields:g,dataFields:x,progress:0},u={isEmptyGrandTotal:y.isEmptyGrandTotalColumn,texts:f.texts||{},hiddenTotals:V,hiddenValues:N,hiddenGrandTotals:U,showTotals:f.showColumnTotals,showTotalsPrior:"columns"===f.showTotalsPrior||"both"===f.showTotalsPrior,showGrandTotals:!1!==f.showColumnGrandTotals&&C,sortBySummaryPaths:t(g,x),showEmpty:!f.hideEmptySummaryCells,fields:v,dataFields:x,progress:0},c=function(p){this.progress=p,s._handleProgressChanged(.8+.1*o.progress+.1*u.progress)};o.notifyProgress=c,u.notifyProgress=c,(0,H.$K)(y.grandTotalRowIndex)||(y.grandTotalRowIndex=n(y.rows,o).length),(0,H.$K)(y.grandTotalColumnIndex)||(y.grandTotalColumnIndex=n(y.columns,u).length),d._changeLoadingCount(1),(0,J.gx)(a(y.columns,v,_,!0,u),a(y.rows,g,A,!1,o)).always(()=>{d._changeLoadingCount(-1)}).done((p,w)=>{s._columnsInfo=p,s._rowsInfo=w,s._rowsScrollController&&s._columnsScrollController&&s.changed&&!s._dataSource.paginate()&&(s._rowsScrollController.reset(!0),s._columnsScrollController.reset(!0),s._lockChanged=!0,s._rowsScrollController.load(),s._columnsScrollController.load(),s._lockChanged=!1)}).done(()=>{s._fireChanged(),s._stateStoringController.isEnabled()&&!s._dataSource.isLoading()&&(s._stateStoringController.state(s._dataSource.state()),s._stateStoringController.save())})},getRowsInfo(s){var v,d=this._rowsInfo,f=this._rowsScrollController;if(f&&!s){var g=f.beginPageIndex()*this.rowPageSize(),x=f.endPageIndex()*this.rowPageSize()+this.rowPageSize(),A=[],_=1;return r(d,(y,V,N,U,C)=>{var u=N<g?0:N-g,c=y;if(!(V>=g&&N<x))return!1;A[u]=A[u]||[],g+u+(v=N<g?y.rowspan-(g-N)||1:y.rowspan)>x&&(v=x-(u+g)||1),v!==y.rowspan&&(c=(0,X.l)({},c,{rowspan:v})),A[u].push(c),_=Mt.max(_,C+1)}),r(A,(y,V,N,U,C)=>{C+(y.colspan||1)>_&&(A[N][U]=(0,X.l)({},y,{colspan:_-C||1}))}),A}return d},getColumnsInfo(s){var d=this._columnsInfo,f=this._columnsScrollController;if(f&&!s){var v=f.beginPageIndex()*this.columnPageSize(),g=f.endPageIndex()*this.columnPageSize()+this.columnPageSize();d=(0,wt.P)(d,v,g)}return d},totalRowCount(){return this._rowsInfo.length},rowPageIndex(s){return void 0!==s&&(this._rowPageIndex=s),this._rowPageIndex||0},totalColumnCount(){var s=0;if(this._columnsInfo&&this._columnsInfo.length)for(var d=0;d<this._columnsInfo[0].length;d+=1)s+=this._columnsInfo[0][d].colspan||1;return s},rowPageSize(s){return void 0!==s&&(this._rowPageSize=s),this._rowPageSize||20},columnPageSize(s){return void 0!==s&&(this._columnPageSize=s),this._columnPageSize||20},columnPageIndex(s){return void 0!==s&&(this._columnPageIndex=s),this._columnPageIndex||0},getCellsInfo(s){var x,A,_,y,N,U,C,o,d=this.getRowsInfo(s),f=this.getColumnsInfo(s),v=this._dataSource.getData(),g=this._options.texts||{};return x=d,A=f,_=v,y=this._dataSource.getAreaFields("data"),N=g.dataNotAvailable,U=[],C="row"===this._options.dataFieldArea,(o=_.values).length&&r(x,(u,c)=>{var p=U[c]=[],w=o[u.dataSourceIndex>=0?u.dataSourceIndex:_.grandTotalRowIndex]||[];u.isLast&&(0,wt.L)(A,(S,E)=>{var T=(C?u.dataIndex:S.dataIndex)||0,b=y[T];if(S.isLast&&b&&!1!==b.visible){var I=w[S.dataSourceIndex>=0?S.dataSourceIndex:_.grandTotalColumnIndex];Array.isArray(I)||(I=[I]);var P=I[T];p[E]={text:e(P,b,N),value:P,format:b.format,dataType:b.dataType,columnType:S.type,rowType:u.type,rowPath:u.path||[],columnPath:S.path||[],dataIndex:T},b.width&&(p[E].width=b.width)}})}),U},dispose(){this._isSharedDataSource?(this._dataSource.off("changed",this._changedHandler),this._dataSource.off("expandValueChanging",this._expandValueChangingHandler),this._dataSource.off("loadingChanged",this._loadingChangedHandler),this._dataSource.off("progressChanged",this._progressChangedHandler),this._dataSource.off("fieldsPrepared",this._fieldsPreparedHandler),this._dataSource.off("customizeStoreLoadOptions",this._customizeStoreLoadOptionsHandler)):this._dataSource.dispose(),this._columnsScrollController&&this._columnsScrollController.dispose(),this._rowsScrollController&&this._rowsScrollController.dispose(),this._stateStoringController.dispose(),this.expandValueChanging.empty(),this.changed.empty(),this.loadingChanged.empty(),this.progressChanged.empty(),this.scrollChanged.empty(),this.dataSourceChanged.empty()}};return Ne(h,"applyPartialDataSource"),Ne(h,"collapseHeaderItem"),Ne(h,"expandHeaderItem"),Ne(h,"getData"),Ne(h,"isEmpty"),h}()),DataController__internals:{NO_DATA_AVAILABLE_TEXT:"#N/A"}};var Ua=L(14290),Qi=L(12384),Ki=L(50089),Wa={exportToExcel(){(0,Ua.xD)(this.getDataProvider(),{fileName:this.option("export.fileName"),format:"EXCEL",rtlEnabled:this.option("rtlEnabled"),exportingAction:this._actions.onExporting,exportedAction:this._actions.onExported,fileSavingAction:this._actions.onFileSaving},Ua.Ak.getData)},_getLength(e){var a,t=e[0].length,r=0;for(a=0;a<t;a+=1)r+=e[0][a].colspan||1;return r},_correctCellsInfoItemLengths(e,a){for(var t=0;t<e.length;t+=1)for(;e[t].length<a;)e[t].push({});return e},_calculateCellInfoItemLength(e){for(var a=0,t=0;t<e.length;t+=1)a+=(0,H.$K)(e[t].colspan)?e[t].colspan:1;return a},_getEmptyCell:()=>({text:"",value:void 0,colspan:1,rowspan:1}),_getAllItems(e,a,t){var r,n,l=t,h=this._getLength(a),s=e.length;if(e.length>0&&e[0].length>0&&t.length>0&&0===t[0].length){var d=this._calculateCellInfoItemLength(e[0]);d>0&&(l=this._correctCellsInfoItemLengths(t,d))}if(0===l.length)for(var f=a.length,v=e.map(_=>_.filter(y=>!y.expanded).length).reduce((_,y)=>_+y,0),g=0;g<f;g+=1){l[g]=[];for(var x=0;x<v;x+=1)l[g][x]=this._getEmptyCell()}var A=e.concat(l);for(n=0;n<a.length;n+=1)for(r=a[n].length-1;r>=0;r-=1)(0,H.$K)(A[n+s])||(A[n+s]=[]),A[n+s].splice(0,0,(0,X.l)({},a[n][r]));return A[0].splice(0,0,(0,X.l)({},this._getEmptyCell(),{alignment:(0,qe.q)(this._options.rtlEnabled),colspan:h,rowspan:s})),(0,Ki.$)(A,this._getEmptyCell())},getDataProvider(){return new za(this)}},za=ye.Z.inherit({ctor(e){this._exportController=e},ready(){this._initOptions();var e=this._options;return(0,J.gx)(e.items).done(a=>{var r=a[a[0][0].rowspan-1];(0,Z.S6)(r,(n,l)=>{l.width=100}),e.columns=r,e.items=a})},_initOptions(){var e=this._exportController,a=e._dataController,t=new J.BH;a.beginLoading(),setTimeout(()=>{var r=(0,X.l)(!0,[],a.getColumnsInfo(!0)),n=(0,X.l)(!0,[],a.getRowsInfo(!0)),l=a.getCellsInfo(!0);t.resolve(e._getAllItems(r,n,l)),a.endLoading()}),this._options={items:t,rtlEnabled:e.option("rtlEnabled"),dataFields:e.getDataSource().getAreaFields("data"),customizeExcelCell:e.option("export.customizeExcelCell"),rowsArea:e._rowsArea,columnsArea:e._columnsArea}},getColumns(){return this._options.columns},getColumnsWidths(){var e=this._options.columnsArea,{rowsArea:a}=this._options,{columns:t}=this._options;return!(0,we.Ym)()||"virtual"===e.option("scrolling.mode")||e.element().is(":hidden")?t.map(()=>100):a.getColumnsWidth().concat(e.getColumnsWidth())},getRowsCount(){return this._options.items.length},getGroupLevel:()=>0,getCellMerging(e,a){var{items:t}=this._options,r=t[e]&&t[e][a];return r?{colspan:r.colspan-1,rowspan:r.rowspan-1}:{colspan:0,rowspan:0}},getFrozenArea(){return{x:this.getRowAreaColCount(),y:this.getColumnAreaRowCount()}},getCellType(e,a){var t=this.getStyles()[this.getStyleId(e,a)];return t&&t.dataType||"string"},getCellData(e,a,t){var r={},{items:n}=this._options,l=n[e]&&n[e][a]||{};if(t){r.cellSourceData=l;var h=this._tryGetAreaName(l,e,a);h&&(r.cellSourceData.area=h),r.cellSourceData.rowIndex=e,r.cellSourceData.columnIndex=a}return r.value="string"===this.getCellType(e,a)?l.text:l.value,r.cellSourceData&&r.cellSourceData.isWhiteSpace&&(r.value=""),r},_tryGetAreaName(e,a,t){return this.isColumnAreaCell(a,t)?"column":this.isRowAreaCell(a,t)?"row":(0,H.$K)(e.dataIndex)?"data":void 0},isRowAreaCell(e,a){return e>=this.getColumnAreaRowCount()&&a<this.getRowAreaColCount()},isColumnAreaCell(e,a){return a>=this.getRowAreaColCount()&&e<this.getColumnAreaRowCount()},getColumnAreaRowCount(){return this._options.items[0][0].rowspan},getRowAreaColCount(){return this._options.items[0][0].colspan},getHeaderStyles(){return[{alignment:"center",dataType:"string"},{alignment:(0,qe.q)(this._options.rtlEnabled),dataType:"string"}]},getDataFieldStyles(){var{dataFields:e}=this._options,a={alignment:this._options.rtlEnabled?"left":"right"},t=[];return e.length?(e.forEach(r=>{t.push((0,Xe.Z)((0,Xe.Z)({},a),{format:r.format,dataType:this.getCellDataType(r)}))}),t):[a]},getStyles(){return this._styles||(this._styles=[...this.getHeaderStyles(),...this.getDataFieldStyles()]),this._styles},getCellDataType(e){if(e&&e.customizeText)return"string";if(e.dataType)return e.dataType;if(e.format){if(1===Qi.Z.parse(Je.Z.format(1,e.format)))return"number";if(Je.Z.format(new Date,e.format))return"date"}return"string"},getStyleId(e,a){var{items:t}=this._options,r=t[e]&&t[e][a]||{};return 0===a&&0===e||this.isColumnAreaCell(e,a)?0:this.isRowAreaCell(e,a)?1:this.getHeaderStyles().length+(r.dataIndex||0)},hasCustomizeExcelCell(){return(0,H.$K)(this._options.customizeExcelCell)},customizeExcelCell(e){this._options.customizeExcelCell&&this._options.customizeExcelCell(e)}}),Xi=L(84667),qi=L(61846),K_area_self="dx-area",K_area_box="dx-area-box",K_area_caption="dx-area-caption",K_area_icon="dx-area-icon",K_area_field="dx-area-field",K_area_fieldContainer="dx-area-field-container",K_area_fieldContent="dx-area-field-content",K_area_fieldList="dx-area-fields",K_area_fieldListHeader="dx-area-fields-header",K_pivotGrid_dragAction="dx-pivotgrid-drag-action",K_pivotGrid_fieldsContainer="dx-pivotgrid-fields-container",K_fieldChooser_self="dx-pivotgridfieldchooser",K_fieldChooser_container="dx-pivotgridfieldchooser-container",K_fieldChooser_contextMenu="dx-pivotgridfieldchooser-context-menu",K_layout_zero="dx-layout-0",K_layout_second="dx-layout-2",K_treeView_self="dx-treeview",K_treeView_borderVisible="dx-treeview-border-visible",K_scrollable_self="dx-scrollable",K_col="dx-col",Ja_targets_drag="drag",el=L(62481),Nt=L(14232),tl=L(63610),al=L(49699),Se=L(63429),rl=L(29962),{getSwatchContainer:nl}=rl.Z,lt="dxSortable",Qa="dx-sortable-old";function ol(e,a,t){return!(0,H.$K)(a.posHorizontal)||(t?e>a.posHorizontal:e<a.posHorizontal)}var Gt=al.Z.inherit({_getDefaultOptions(){return(0,X.l)(this.callBase(),{onChanged:null,onDragging:null,itemRender:null,groupSelector:null,itemSelector:".dx-sort-item",itemContainerSelector:".dx-sortable-old",sourceClass:"dx-drag-source",dragClass:"dx-drag",targetClass:"dx-drag-target",direction:"vertical",allowDragging:!0,groupFilter:null,useIndicator:!1})},_renderItem(e,a){var r,t=this.option("itemRender");return t?r=t(e,a):(r=e.clone()).css({width:(0,ae.dz)(e),height:(0,ae.Cr)(e)}),r},_renderIndicator(e,a,t,r){var n=(0,ae.zp)(e,!0),l=(0,ae.Y_)(e,!0),h=e.offset().top-t.offset().top,s=e.offset().left-t.offset().left;this._indicator.css({position:"absolute",top:r&&a?h+n:h,left:r&&!a?s+l:s}).toggleClass("dx-position-indicator-horizontal",!a).toggleClass("dx-position-indicator-vertical",!!a).toggleClass("dx-position-indicator-last",!!r).appendTo(t),(0,ae.QQ)(this._indicator,""),(0,ae.cl)(this._indicator,""),a?(0,ae.cl)(this._indicator,l):(0,ae.QQ)(this._indicator,n)},_renderDraggable(e){this._$draggable&&this._$draggable.remove(),this._$draggable=this._renderItem(e,"drag").addClass(this.option("dragClass")).appendTo(nl(e)).css({zIndex:1e6,position:"absolute"})},_detachEventHandlers(){var e=[Se.pB,Se.BL,Se.ut,Se.IO,Se.Zu,Se.Cw].join(" ");Te.Z.off(this._getEventListener(),(0,Ze.V4)(e,lt),void 0)},_getItemOffset(e,a,t){for(var r=0;r<a.length;r+=1){var n=void 0,l=t.pageY<a[r].posVertical;if(e?n=l:l&&!(n=ol(t.pageX,a[r],this.option("rtlEnabled")))&&a[r+1]&&a[r+1].posVertical>a[r].posVertical&&(n=!0),n)return a[r]}},_getEventListener(){var e=this.option("groupSelector"),a=this.$element();return e?a.find(e):a},_attachEventHandlers(){var d,f,v,g,x,A,y,e=this,a=e.option("itemSelector"),t=e.option("itemContainerSelector"),r=e.option("groupSelector"),n=e.option("sourceClass"),l=e.option("targetClass"),h=e.option("onDragging"),s=e.option("groupFilter"),_=e.$element(),V=null,N=-1,U=function(){V?.dispose(),V=null};if(e._detachEventHandlers(),e.option("allowDragging")){var C=e._getEventListener();Te.Z.on(C,(0,Ze.V4)(Se.BL,lt),a,o=>{var c,u=(d=(0,M.Z)(o.currentTarget)).closest(r);A=u.attr("group"),f=function sl(e,a){var t=-1,r=a.get(0);return(0,Z.S6)(e,(n,l)=>{var h=(0,M.Z)(l);if(h.attr("item-group")&&h.attr("item-group")===e.eq(n-1).attr("item-group")||(t+=1),l===r)return!1}),t===e.length?-1:t}((r?u:_).find(a),d),d.attr("item-group")&&(d=u.find("[item-group='".concat(d.attr("item-group"),"']"))),e._renderDraggable(d),v=e._renderItem(d,"target").addClass(l),d.addClass(n),x=[],(0,Z.S6)(d,(c,p)=>{x.push((0,M.Z)(p).offset())}),c=ia.Z.getRootNode(e.$element().get(0)),y=r?s?(0,M.Z)(c).find(r).filter(s):_.find(r):_,e._indicator=(0,M.Z)("<div>").addClass("dx-position-indicator")}),Te.Z.on(C,(0,Ze.V4)(Se.pB,lt),o=>{var u,c,p,O;if(d){if(N=-1,e._indicator.detach(),(0,Z.S6)(e._$draggable,(O,R)=>{(0,M.Z)(R).css({top:x[O].top+o.offset.y,left:x[O].left+o.offset.x})}),g&&g.removeClass(l),(g=function dl(e,a){var t;return(0,Z.S6)(a,function(){(function ll(e,a,t){var r=(0,M.Z)(e),n=r.offset();if(a>=n.left&&a<=n.left+(0,ae.Y_)(r,!0)&&t>=n.top&&t<=n.top+(0,ae.zp)(r,!0))return!0})(this,e.pageX,e.pageY)&&(t=(0,M.Z)(this))}),t}(o,y))&&(O={sourceGroup:A,sourceIndex:f,sourceElement:d,targetGroup:g.attr("group"),targetIndex:g.find(a).index(v)},h&&h(O),O.cancel&&(g=void 0)),g&&V&&g.get(0)!==V.element().get(0)&&U(),V&&V.moveIfNeed(o),!g)return void v.detach();!V&&g.attr("allow-scrolling")&&(V=function ul(e){var a,t=e.scrollTop(),r=e.$element(),{top:n}=r.offset(),l=(0,ae.Cr)(r),h=0;function s(v){t=v.scrollOffset.top}function d(){f(),e.scrollTo(t+=h),a=setTimeout(d,10)}function f(){clearTimeout(a)}return e.on("scroll",s),{moveIfNeed:function(v){if(v.pageY<=n+20)h=-2;else{if(!(v.pageY>=n+l-20))return h=0,void f();h=2}d()},element:()=>r,dispose(){f(),e.off("scroll",s)}}}(g.dxScrollable("instance"))),g.addClass(l);var w=g.find(t),S=w.find(a),E=g.closest(".".concat(Qa)).data("dxSortableOld"),T=E.option("useIndicator"),b="vertical"===(E||e).option("direction"),I=function cl(e,a,t){for(var r=[],n=[],l=0;l<e.length;l+=n.length)if((n=e.eq(l)).attr("item-group")&&(n=t.find("[item-group='".concat(n.attr("item-group"),"']"))),n.is(":visible")){var h={item:n,index:r.length,posVertical:a?(n.last().offset().top+n.offset().top+(0,ae.zp)(n.last(),!0))/2:(0,ae.zp)(n.last(),!0)+n.last().offset().top,posHorizontal:a?void 0:((0,ae.Y_)(n.last(),!0)+n.last().offset().left+n.offset().left)/2};r.push(h)}return r}(S,b,w),P=e._getItemOffset(b,I,o);if(P){if(p=I[P.index-1]&&I[P.index-1].item,(u=P.item).hasClass(n)||p&&p.hasClass(n)&&p.is(":visible"))return void v.detach();if(N=P.index,!T)return void v.insertBefore(u);var m=g.attr("group")!==A,D=N===f,F=N===f+1;if(m)return void e._renderIndicator(u,b,g,e.option("rtlEnabled")&&!b);!D&&!F&&e._renderIndicator(u,b,g,e.option("rtlEnabled")&&!b)}else{if((c=S.last()).is(":visible")&&c.hasClass(n))return;w.length&&(N=I.length?I[I.length-1].index+1:0),T?S.length&&e._renderIndicator(c,b,g,!e.option("rtlEnabled")||b):v.appendTo(w)}}}),Te.Z.on(C,(0,Ze.V4)(Se.ut,lt),()=>{if(U(),d){var o=e.option("onChanged"),u={sourceIndex:f,sourceElement:d,sourceGroup:A,targetIndex:N,removeSourceElement:!0,removeTargetElement:!1,removeSourceClass:!0};g&&(g.removeClass(l),u.targetGroup=g.attr("group"),(A!==u.targetGroup||N>-1)&&(o&&o(u),u.removeSourceElement&&d.remove())),e._indicator.detach(),u.removeSourceClass&&d.removeClass(n),d=null,e._$draggable.remove(),e._$draggable=null,u.removeTargetElement&&v.remove(),v.removeClass(l),v=null}})}},_init(){this.callBase(),this._attachEventHandlers()},_render(){this.callBase(),this.$element().addClass(Qa)},_dispose(){this.callBase.apply(this,arguments),this._$draggable&&this._$draggable.detach(),this._indicator&&this._indicator.detach()},_optionChanged(e){switch(e.name){case"onDragging":case"onChanged":case"itemRender":case"groupSelector":case"itemSelector":case"itemContainerSelector":case"sourceClass":case"targetClass":case"dragClass":case"allowDragging":case"groupFilter":case"useIndicator":this._attachEventHandlers();break;case"direction":break;default:this.callBase(e)}},_useTemplates:()=>!1});function fl(e,a){var t=function pl(e,a){var t=e.hasClass(K_area_box),r=e.attr("tree-view-item");return t?function vl(e,a){var t=e.clone();return a===Ja_targets_drag&&e.each((r,n)=>{var l=parseFloat((0,ae.Y_)(n));return t.eq(r).css("width",l),!0}),t}(e,a):r?function hl(e){return e.clone().addClass(K_area_box).css("width",parseFloat((0,ae.Y_)(e)))}(e):function gl(e){return(0,M.Z)("<div>").addClass(K_area_field).addClass(K_area_box).text(e.text())}(e)}(e,a);return a===Ja_targets_drag?function ml(e){var a=(0,M.Z)("<div>");return e.each((t,r)=>{var n=(0,M.Z)("<div>").addClass(K_pivotGrid_fieldsContainer).addClass("dx-widget").append((0,M.Z)(r));return a.append(n),!0}),a.children()}(t):t}(0,Ue.Z)("dxSortableOld",Gt);var Cl=Nt.U3.inherit({_getSearchExpr(e){return e.useDefaultSearchExpr=!0,this.callBase(e)}});function Vt(e,a){var t=a;return(0,H.$K)(a.groupIndex)&&(t=e.getAreaFields(a.area,!0)[a.areaIndex]),t}function Xa(e){return e=e||{},JSON.stringify([e.fields,e.columnExpandedPaths,e.rowExpandedPaths])}var ot=na.Z.inherit(el.Z).inherit(tl.Z).inherit(Nt.Tr).inherit({_getDefaultOptions(){return(0,X.l)(this.callBase(),{allowFieldDragging:!0,applyChangesMode:"instantly",state:null,headerFilter:{width:252,height:325,searchTimeout:500,texts:{emptyValue:ie.Z.format("dxDataGrid-headerFilterEmptyValue"),ok:ie.Z.format("dxDataGrid-headerFilterOK"),cancel:ie.Z.format("dxDataGrid-headerFilterCancel")}},remoteSort:!1})},_init(){this.callBase(),this._headerFilterView=new Cl(this),this._refreshDataSource(),this.subscribeToEvents()},_refreshDataSource(){var e=this.option("dataSource");e&&e.fields&&e.load&&(this._dataSource=e)},_optionChanged(e){switch(e.name){case"dataSource":this._refreshDataSource();break;case"applyChangesMode":case"remoteSort":break;case"state":if(this._skipStateChange||!this._dataSource)break;"instantly"===this.option("applyChangesMode")&&Xa(this._dataSource.state())!==Xa(e.value)?this._dataSource.state(e.value):(this._clean(!0),this._renderComponent());break;case"headerFilter":case"allowFieldDragging":this._invalidate();break;default:this.callBase(e)}},renderField(e,a){var t=(0,M.Z)("<div>").addClass(K_area_fieldContent).text(e.caption||e.dataField),r=(0,M.Z)("<div>").addClass(K_area_field).addClass(K_area_box).data("field",e).append(t),n=Vt(this._dataSource,e);return"data"!==e.area&&(e.allowSorting&&this._applyColumnState({name:"sort",rootElement:r,column:{alignment:this.option("rtlEnabled")?"right":"left",sortOrder:"desc"===e.sortOrder?"desc":"asc",allowSorting:e.allowSorting},showColumnLines:a}),this._applyColumnState({name:"headerFilter",rootElement:r,column:{alignment:this.option("rtlEnabled")?"right":"left",filterValues:n.filterValues,allowFiltering:n.allowFiltering&&!e.groupIndex,allowSorting:e.allowSorting},showColumnLines:a})),e.groupName&&r.attr("item-group",e.groupName),r},_clean(){},_render(){this.callBase(),this._headerFilterView.render(this.$element())},renderSortable(){var e=this;e._createComponent(e.$element(),Gt,(0,X.l)({allowDragging:e.option("allowFieldDragging"),itemSelector:".".concat(K_area_field),itemContainerSelector:".".concat(K_area_fieldContainer),groupSelector:".".concat(K_area_fieldList),groupFilter(){var a=e._dataSource,t=(0,M.Z)(this).closest(".dx-sortable-old"),r=t.data("dxPivotGrid"),n=t.data("dxPivotGridFieldChooser");return r?r.getDataSource()===a:!!n&&n.option("dataSource")===a},itemRender:fl,onDragging(a){var t=a.sourceElement.data("field"),{targetGroup:r}=a;a.cancel=!1,!0===t.isMeasure?("column"===r||"row"===r||"filter"===r)&&(a.cancel=!0):!1===t.isMeasure&&"data"===r&&(a.cancel=!0)},useIndicator:!0,onChanged(a){var t=a.sourceElement.data("field");if(a.removeSourceElement=!!a.sourceGroup,e._adjustSortableOnChangedArgs(a),t){var n,{targetIndex:r}=a,l=0;e._processDemandState(h=>{var s=h.getAreaFields(t.area,!0);n=Vt(h,t);var f=s.filter(v=>!1!==v.visible)[r-1];f&&(l=s.filter(v=>!1===v.visible&&v.areaIndex<=f.areaIndex).length)}),e._applyChanges([n],{area:a.targetGroup,areaIndex:r+l})}}},e._getSortableOptions()))},_processDemandState(e){var a="instantly"===this.option("applyChangesMode"),t=this._dataSource;if(a)e(t,a);else{var r=t.state(),n=this.option("state");n&&t.state(n,!0),e(t,a),t.state(r,!0)}},_applyChanges(e,a){var t=this;t._processDemandState((r,n)=>{e.forEach(l=>{var{index:h}=l;r.field(h,a)}),n?r.load():t._changedHandler()})},_applyLocalSortChanges(e,a){this._processDemandState(t=>{t.field(e,{sortOrder:a}),t.sortLocal()})},_adjustSortableOnChangedArgs(e){e.removeSourceElement=!1,e.removeTargetElement=!0,e.removeSourceClass=!1},_getSortableOptions:()=>({direction:"auto"}),subscribeToEvents(e){var a=this;Te.Z.on(e||a.$element(),vt.u,".".concat(K_area_field,".").concat(K_area_box),function(r){var n=(0,M.Z)(r.currentTarget).data("field"),l=(0,X.l)(!0,{},Vt(a._dataSource,n)),h=(0,M.Z)(r.target).hasClass("dx-header-filter"),s=a._dataSource,d=l.groupName?"tree":"list",f=s.paginate()&&"list"===d;if(h)a._headerFilterView.showHeaderFilterMenu((0,M.Z)(r.currentTarget),(0,X.l)(l,{type:d,encodeHtml:a.option("encodeHtml"),dataSource:{useDefaultSearch:!f,load(x){var{userData:A}=x;if(A.store)return A.store.load(x);var _=new J.BH;return s.getFieldValues(l.index,a.option("headerFilter.showRelevantValues"),f?x:void 0).done(y=>{var V=a.option("headerFilter.texts.emptyValue");y.forEach(N=>{N.text||(N.text=V)}),f?_.resolve(y):(A.store=new ke.Z(y),A.store.load(x).done(_.resolve).fail(_.reject))}).fail(_.reject),_},postProcess:x=>(function(e,a){var t=[],r=!!a.groupName,n="exclude"===a.filterType;a.filterValues&&(0,Z.S6)(a.filterValues,(l,h)=>{t.push(Array.isArray(h)?h.join("/"):h&&h.valueOf())}),ce(e,l=>{var h=l[0],s=ge(l),d=r?(0,Z.UI)(l,v=>v.text).reverse().join("/"):h.text;h.value=r?s.slice(0):h.key||h.value;var f=r?s.join("/"):h.value&&h.value.valueOf();h.children&&(h.items=h.children,h.children=null),(0,Nt.By)(h,h.key&&t.includes(d)||t.includes(f),n)})}(x,l),x)},apply(){a._applyChanges([l],{filterValues:this.filterValues,filterType:this.filterType})}}));else if(n.allowSorting&&"data"!==n.area){var v=a.option("remoteSort"),g=(e=>"desc"===e?"asc":"desc")(n.sortOrder);v?a._applyChanges([n],{sortOrder:g}):a._applyLocalSortChanges(n.index,g)}})},_initTemplates:me.ZT,addWidgetPrefix:e=>"dx-pivotgrid-".concat(e)});(0,Ue.Z)("dxPivotGridFieldChooserBase",ot);var ue="<div>",Sl=(0,we.Ym)();function qa(e,a){var t=[];if(e.items)for(var r=0;r<e.items.length;r+=1)t.push.apply(t,qa(e.items[r],a));else(0,H.$K)(e.index)&&t.push(a[e.index]);return t}function $t(e,a){if(e.items)for(var t=0;t<e.items.length;t+=1){var r=$t(e.items[t],a);if(r)return r}if(a(e))return e}var er=[function(e,a){return+!!a.isMeasure-!!e.isMeasure},function(e,a){return+!(!a.items||!a.items.length)-!(!e.items||!e.items.length)},function(e,a){return+!!(!1===e.isMeasure&&e.field&&e.field.levels&&e.field.levels.length)-!!(!1===a.isMeasure&&a.field&&a.field.levels&&a.field.levels.length)},Ke(e=>e.text)];function wl(e,a){for(var t=0,r=0;!t&&er[r];)t=er[r++](e,a);return t}function tr(e){return e.find(".".concat(K_scrollable_self)).dxScrollable("instance")}var kt=ot.inherit({_getDefaultOptions(){return(0,X.l)(this.callBase(),{height:400,layout:0,dataSource:null,encodeHtml:!0,onContextMenuPreparing:null,allowSearch:!1,searchTimeout:500,texts:{columnFields:ie.Z.format("dxPivotGrid-columnFields"),rowFields:ie.Z.format("dxPivotGrid-rowFields"),dataFields:ie.Z.format("dxPivotGrid-dataFields"),filterFields:ie.Z.format("dxPivotGrid-filterFields"),allFields:ie.Z.format("dxPivotGrid-allFields")}})},_refreshDataSource(){var e=this;e._expandedPaths=[],e._changedHandler=e._changedHandler||function(){(0,Z.S6)(e._dataChangedHandlers,(a,t)=>{t()}),e._fireContentReadyAction(),e._skipStateChange=!0,e.option("state",e._dataSource.state()),e._skipStateChange=!1},e._disposeDataSource(),e.callBase(),e._dataSource&&e._dataSource.on("changed",e._changedHandler)},_disposeDataSource(){var e=this._dataSource;e&&(e.off("changed",this._changedHandler),this._dataSource=void 0)},_dispose(){this._disposeDataSource(),this.callBase.apply(this,arguments)},_init(){this.callBase(),this._refreshDataSource(),this._dataChangedHandlers=[],this._initActions()},_initActions(){this._actions={onContextMenuPreparing:this._createActionByOption("onContextMenuPreparing")}},_trigger(e,a){this._actions[e](a)},_setOptionsByReference(){this.callBase(),(0,X.l)(this._optionsByReference,{dataSource:!0})},_optionChanged(e){switch(e.name){case"dataSource":this._refreshDataSource(),this._invalidate();break;case"layout":case"texts":case"allowSearch":case"searchTimeout":case"encodeHtml":this._invalidate();break;case"onContextMenuPreparing":this._actions[e.name]=this._createActionByOption(e.name);break;default:this.callBase(e)}},_clean(e){!e&&this._dataSource&&this.option("state",this._dataSource.state()),this.$element().children(".".concat(K_fieldChooser_container)).remove()},_renderLayout0(e){e.addClass(K_layout_zero);var a=(0,M.Z)(ue).addClass("dx-row").appendTo(e),t=(0,M.Z)(ue).addClass("dx-row").appendTo(e),r=(0,M.Z)(ue).addClass(K_col).appendTo(a),n=(0,M.Z)(ue).addClass(K_col).appendTo(a),l=(0,M.Z)(ue).addClass(K_col).appendTo(t),h=(0,M.Z)(ue).addClass(K_col).appendTo(t);this._renderArea(r,"all"),this._renderArea(n,"row"),this._renderArea(n,"column"),this._renderArea(l,"filter"),this._renderArea(h,"data")},_renderLayout1(e){var a=(0,M.Z)(ue).addClass(K_col).appendTo(e),t=(0,M.Z)(ue).addClass(K_col).appendTo(e);this._renderArea(a,"all"),this._renderArea(t,"filter"),this._renderArea(t,"row"),this._renderArea(t,"column"),this._renderArea(t,"data")},_renderLayout2(e){e.addClass(K_layout_second);var a=(0,M.Z)(ue).addClass("dx-row").appendTo(e);this._renderArea(a,"all");var t=(0,M.Z)(ue).addClass("dx-row").appendTo(e),r=(0,M.Z)(ue).addClass(K_col).appendTo(t),n=(0,M.Z)(ue).addClass(K_col).appendTo(t);this._renderArea(r,"filter"),this._renderArea(r,"row"),this._renderArea(n,"column"),this._renderArea(n,"data")},_initMarkup(){var e=this.$element(),a=(0,M.Z)(ue).addClass(K_fieldChooser_container).appendTo(e),t=this.option("layout");this.callBase(),e.addClass(K_fieldChooser_self).addClass(K_pivotGrid_fieldsContainer),this._dataChangedHandlers=[];var r=this._dataSource,n="instantly"!==this.option("applyChangesMode")&&r&&r.state();n&&this.option("state")&&r.state(this.option("state"),!0),0===t?this._renderLayout0(a):1===t?this._renderLayout1(a):this._renderLayout2(a),n&&r.state(n,!0)},_renderContentImpl(){this.callBase(),this.renderSortable(),this._renderContextMenu(),this.updateDimensions()},_fireContentReadyAction(){(!this._dataSource||!this._dataSource.isLoading())&&this.callBase()},_getContextMenuArgs(e){var r,n,a=(0,M.Z)(e.target).closest(".".concat(K_area_field)),t=(0,M.Z)(e.target).closest(".".concat(K_area_fieldList));if(a.length){var l=a.data("field");l&&(r=this.getDataSource().field(l.index)||l)}return t.length&&(n=t.attr("group")),{event:e,field:r,area:n,items:[]}},_renderContextMenu(){var e=this,a=e.$element();e._contextMenu&&e._contextMenu.$element().remove(),e._contextMenu=e._createComponent((0,M.Z)(ue).appendTo(a),aa.Z,{onPositioning(t){var{event:r}=t;if(r){var n=e._getContextMenuArgs(r);e._trigger("onContextMenuPreparing",n),n.items&&n.items.length?t.component.option("items",n.items):t.cancel=!0}},target:a,onItemClick(t){t.itemData.onItemClick&&t.itemData.onItemClick(t)},cssClass:K_fieldChooser_contextMenu})},_createTreeItems(e,a,t){var n,r=this,l=[],h=[],s=a[0],d={};return s?((0,Z.S6)(e,(f,v)=>{var g=v[s]||"";d[g]=d[g]||[],d[g].push(v),void 0===n&&(n=!0),n=n&&!0===v.isMeasure}),(0,Z.S6)(d,(f,v)=>{var g=t?"".concat(t,".").concat(f):f,x=r._createTreeItems(v,a.slice(1),g);f?h.push({key:f,text:f,path:g,isMeasure:x.isMeasure,expanded:r._expandedPaths.includes(g),items:x}):l=x}),(l=h.concat(l)).isMeasure=n):(0,Z.S6)(e,(f,v)=>{var g;!0===v.isMeasure&&(g="measure"),!1===v.isMeasure&&(g=v.groupName?"hierarchy":"dimension"),l.push({index:v.index,field:v,key:v.dataField,selected:(0,H.$K)(v.area),text:v.caption||v.dataField,icon:g,isMeasure:v.isMeasure,isDefault:v.isDefault})}),l},_createFieldsDataSource(e){var a=e&&e.fields()||[];a=a.filter(r=>!1!==r.visible&&!(0,H.$K)(r.groupIndex));var t=this._createTreeItems(a,["dimension","displayFolder"]);return Ye(t,r=>{r.sort(wl)},0,"items"),t},_renderFieldsTreeView(e){var a=this,t=a._dataSource,r=a._createComponent(e,qi.Z,{dataSource:a._createFieldsDataSource(t),showCheckBoxesMode:"normal",expandNodesRecursive:!1,searchEnabled:a.option("allowSearch"),searchTimeout:a.option("searchTimeout"),useNativeScrolling:!1,itemTemplate(n,l,h){var s,d=(0,M.Z)("<div>").toggleClass(K_area_field,!n.items).attr("tree-view-item",!0).data("field",n.field).appendTo(h);n.icon&&(null===(s=(0,Xi.H)(n.icon))||void 0===s||s.appendTo(d)),(0,M.Z)("<span>").text(n.text).appendTo(d)},onItemCollapsed(n){var l=a._expandedPaths.indexOf(n.itemData.path);l>=0&&a._expandedPaths.splice(l,1)},onItemExpanded(n){a._expandedPaths.indexOf(n.itemData.path)<0&&a._expandedPaths.push(n.itemData.path)},onItemSelectionChanged(n){var h,s,f,l=n.itemData,d=!0;if(l.items){if(l.selected)return void r.unselectItem(l);if(a._processDemandState(()=>{s=qa(l,t.fields());for(var g=0;g<s.length;g+=1)if(s[g].area){d=!1;break}}),d){var v=$t(l,g=>g.isDefault)||$t(l,g=>(0,H.$K)(g.index));return void(v&&r.selectItem(v))}}else h=t.fields()[l.index],l.selected&&(f=h.isMeasure?"data":"column"),h&&(s=[h]);a._applyChanges(s,{area:f,areaIndex:void 0})}});a._dataChangedHandlers.push(function(){var n=tr(e),l=n?n.scrollTop():0;r.option({dataSource:a._createFieldsDataSource(t)}),(n=tr(e))&&(n.scrollTo({y:l}),n.update())})},_renderAreaFields(e,a){var t=this,r=t._dataSource,n=r?(0,X.l)(!0,[],r.getAreaFields(a,!0)):[];e.empty(),(0,Z.S6)(n,(l,h)=>{!1!==h.visible&&t.renderField(h,!0).appendTo(e)})},_renderArea(e,a){var h,s,t=this,r=(0,M.Z)(ue).addClass(K_area_self).appendTo(e),n=(0,M.Z)(ue).addClass(K_area_fieldListHeader).appendTo(r),l=t.option("texts.".concat(a,"Fields"));(0,M.Z)("<span>").addClass(K_area_icon).addClass("dx-area-icon-".concat(a)).appendTo(n),(0,M.Z)("<span>").html("&nbsp;").appendTo(n),(0,M.Z)("<span>").addClass(K_area_caption).text(l).appendTo(n);var d=(0,M.Z)(ue).addClass(K_area_fieldList).addClass(K_pivotGrid_dragAction).appendTo(r);"all"!==a?(d.attr("group",a).attr("allow-scrolling",!0),h=(0,M.Z)(ue).addClass(K_area_fieldContainer).appendTo(d),s=function(){t._renderAreaFields(h,a)},t._dataChangedHandlers.push(s),s(),d.dxScrollable({useNative:!1})):(r.addClass("dx-all-fields"),d.addClass(K_treeView_borderVisible),t._renderFieldsTreeView(d))},_getSortableOptions:()=>({}),_adjustSortableOnChangedArgs(){},resetTreeView(){var e=this.$element().find(".".concat(K_treeView_self)).dxTreeView("instance");e&&(e.option("searchValue",""),e.collapseAll())},applyChanges(){var e=this.option("state");(0,H.$K)(e)&&this._dataSource.state(e)},cancelChanges(){var e=this._dataSource;return!e.isLoading()&&(this.option("state",e.state()),!0)},getDataSource(){return this._dataSource},updateDimensions(){this.$element().find(".".concat(K_area_self," .").concat(K_scrollable_self)).dxScrollable("update")},_visibilityChanged(e){e&&Sl&&this.updateDimensions()}});(0,Ue.Z)("dxPivotGridFieldChooser",kt);var ar=L(47455),yl=L(9077),Le="<div>",Ge=et.inherit({ctor(e,a){this.callBase(e),this._area=a},_getAreaName:()=>"fields",_createGroupElement(){return(0,M.Z)(Le).addClass("dx-pivotgrid-fields-area").addClass("dx-area-fields").addClass("dx-pivotgrid-drag-action").attr("group",this._area)},isVisible(){return!!this.option("fieldPanel.visible")&&this.option("fieldPanel.show".concat(Ct(this._area),"Fields"))},_renderButton(e){var a=this,t=(0,M.Z)("<td>").appendTo((0,M.Z)("<tr>").appendTo(e));a.component._createComponent((0,M.Z)(Le).appendTo(t),yl.Z,{text:"Fields",icon:"menu",width:"auto",onClick(){var n=a.tableElement().find(".dx-fields-area-popup").dxPopup("instance");n.option("visible")||n.show()}}).$element().addClass("dx-pivotgrid-fields-area-hamburger")},_getPopupOptions:(e,a)=>({contentTemplate:()=>(0,M.Z)("<table>").addClass("dx-area-field-container").append((0,M.Z)("<thead>").addClass("dx-pivotgrid-fields-area-head").append(e)),height:"auto",width:"auto",position:{at:"left",my:"left",of:a},dragEnabled:!1,animation:{show:{type:"pop",duration:200}},shading:!1,showTitle:!1,hideOnOutsideClick:!0,container:a.parent()}),_renderPopup(e,a){var t=e.find(".dx-button"),r=this._getPopupOptions(a,t),n=this.component.$element().dxPivotGridFieldChooserBase("instance");this._rowPopup&&this._rowPopup.$element().remove(),this._rowPopup=this.component._createComponent((0,M.Z)(Le).appendTo(e),ra.Z,r),this._rowPopup.$element().addClass("dx-fields-area-popup"),this._rowPopup.content().addClass("dx-pivotgrid-fields-container"),this._rowPopup.content().parent().attr("group","row"),n.subscribeToEvents(this._rowPopup.content()),n.renderSortable(this._rowPopup.content())},_shouldCreateButton:()=>!1,_renderTableContent(e,a){var t=this,r=this.groupElement(),n=this.isVisible(),l=t.component.$element().dxPivotGridFieldChooserBase("instance"),h=(0,M.Z)("<thead>").addClass("dx-pivotgrid-fields-area-head").appendTo(e),s=t._area,d=(0,M.Z)("<tr>");r.toggleClass("dx-hidden",!n),e.addClass("dx-area-field-container"),n&&((0,Z.S6)(a,(f,v)=>{if(v.area===s&&!1!==v.visible){var g=(0,M.Z)("<td>").append(l.renderField(v,"row"===v.area)),x=g.find(".dx-column-indicators");x.length&&t._shouldCreateButton()&&x.insertAfter(x.next()),g.appendTo(d),function El(e,a,t,r){t&&t.groupName&&t.groupName===e.groupName&&(0,M.Z)(Le).addClass("dx-group-connector").addClass("dx-group-connector-prev").appendTo(r),a&&a.groupName&&a.groupName===e.groupName&&(0,M.Z)(Le).addClass("dx-group-connector").addClass("dx-group-connector-next").appendTo(r)}(v,a[f+1],a[f-1],g)}}),d.children().length||(0,M.Z)("<td>").append((0,M.Z)(Le).addClass("dx-empty-area-text").text(this.option("fieldPanel.texts.".concat(s,"FieldArea")))).appendTo(d),t._shouldCreateButton()?(t._renderButton(h),t._renderPopup(e,d)):h.append(d))},setGroupWidth(e){(0,ar.cl)(this.groupElement(),e)},setGroupHeight(e){(0,ar.QQ)(this.groupElement(),e)},reset(){this.callBase(),this.groupElement().css("marginTop",0)},_renderVirtualContent:me.ZT}),bl=L(52070),rr="dx-expand-border",nr=!!bl.Z.IS_RENOVATED_WIDGET;function ir(e,a){if(a){var{data:t}=e.data(),{rowIndex:r}=a.parentNode,{cellIndex:n}=a;return t[r]&&t[r][n]&&t[r][n].path}}var lr=et.inherit({ctor(e){this.callBase(e),this._scrollBarWidth=0},_getAreaName:()=>"column",_getAreaClassName:()=>"dx-pivotgrid-horizontal-headers",_createGroupElement(){return(0,M.Z)("<div>").addClass(this._getAreaClassName()).addClass("dx-pivotgrid-area")},_applyCustomStyles(e){var{cssArray:a}=e,{cell:t}=e,{rowsCount:r}=e,{classArray:n}=e;e.cellIndex===e.cellsCount-1&&a.push("".concat(e.rtlEnabled?"border-left:":"border-right:","0px")),(t.rowspan===r-e.rowIndex||e.rowIndex+1===r)&&a.push("border-bottom-width:0px"),("T"===t.type||"GT"===t.type)&&n.push("dx-row-total"),"T"===e.cell.type&&n.push("dx-total"),"GT"===e.cell.type&&n.push("dx-grandtotal"),(0,H.$K)(t.expanded)&&n.push(t.expanded?"dx-pivotgrid-expanded":"dx-pivotgrid-collapsed"),this.callBase(e)},_getMainElementMarkup(){return"<thead class='".concat(this._getAreaClassName(),"'>")},_getCloseMainElementMarkup:()=>"</thead>",setVirtualContentParams(e){this.callBase(e),this._setTableCss({left:e.left,top:0}),this._virtualContentWidth=e.width},hasScroll(){var e=this._virtualContent?this._virtualContentWidth:this._tableWidth,a=this.getGroupWidth();return!(!a||!e)&&e-a>=1},renderScrollable(){this._groupElement.dxScrollable({useNative:!1,useSimulatedScrollbar:!1,showScrollbar:"never",bounceEnabled:!1,direction:"horizontal",rtlEnabled:!!nr&&this.component.option("rtlEnabled"),updateManually:!0})},updateScrollableOptions(e){var{rtlEnabled:a}=e,t=this._getScrollable();nr&&t.option({rtlEnabled:a})},processScrollBarSpacing(e){var a=this.option("rtlEnabled")?"right":"left",t=this.getGroupWidth();t&&this.setGroupWidth(t-e),this._scrollBarWidth&&this._groupElement.next().remove(),this._groupElement.toggleClass("dx-vertical-scroll",e>0),(0,ae.cl)(this._groupElement.css("float",a),this.getGroupHeight()),this._scrollBarWidth=e},getScrollPath(e){var t,a=this.tableElement();return e-=parseInt(a[0].style.left,10)||0,(0,Z.S6)(a.find("td"),(r,n)=>{if(1===n.colSpan&&n.offsetLeft<=e&&n.offsetWidth+n.offsetLeft>e)return t=n,!1}),ir(a,t)},_moveFakeTable(e){this._moveFakeTableHorizontally(e),this.callBase()}}),Hl=lr.inherit({_getAreaClassName:()=>"dx-pivotgrid-vertical-headers",_applyCustomStyles(e){this.callBase(e),e.cellIndex===e.cellsCount-1&&e.classArray.push("dx-last-cell"),e.rowIndex===e.rowsCount-1&&e.cssArray.push("border-bottom: 0px"),e.cell.isWhiteSpace&&e.classArray.push("dx-white-space-column")},_getAreaName:()=>"row",setVirtualContentParams(e){this.callBase(e),this._setTableCss({top:e.top,left:0}),this._virtualContentHeight=e.height},hasScroll(){var e=this._virtualContent?this._virtualContentHeight:this._tableHeight,a=this.getGroupHeight();return!(!a||!e)&&e-a>=1},renderScrollable(){this._groupElement.dxScrollable({useNative:!1,useSimulatedScrollbar:!1,showScrollbar:"never",bounceEnabled:!1,direction:"vertical",updateManually:!0})},processScrollBarSpacing(e){var a=this.getGroupHeight();if(a&&this.setGroupHeight(a-e),this._scrollBarWidth&&this._groupElement.next().remove(),e){var t=(0,M.Z)("<div>");(0,ae.cl)(t,"100%"),(0,ae.QQ)(t,e-1),this._groupElement.after(t)}this._scrollBarWidth=e},getScrollPath(e){var t,a=this.tableElement();return e-=parseInt(a[0].style.top,10)||0,(0,Z.S6)(a.find("tr"),(r,n)=>{var l=n.childNodes[n.childNodes.length-1];if(l&&1===l.rowSpan&&l.offsetTop<=e&&l.offsetHeight+l.offsetTop>e)return t=l,!1}),ir(a,t)},_moveFakeTable(e){this._moveFakeTableTop(e),this.callBase()},_getRowClassNames(e,a,t){0!==e&a.expanded&&!t.includes(rr)&&t.push(rr)},_getMainElementMarkup(){return"<tbody class='".concat(this._getAreaClassName(),"'>")},_getCloseMainElementMarkup:()=>"</tbody>",updateColspans(e){var{rows:a}=this.tableElement()[0],t=0,r=[];if(!(this.getColumnsCount()-e>0))for(var n=0;n<a.length;n+=1)for(var l=0;l<a[n].cells.length;l+=1){var h=a[n].cells[l],{rowSpan:s}=h;r[n]&&(t-=r[n],r[n]=0);var d=e-(t+h.colSpan);l===a[n].cells.length-1&&d>0&&(h.colSpan+=d),r[n+s]=(r[n+s]||0)+h.colSpan,t+=h.colSpan}}});const or={HorizontalHeadersArea:lr,VerticalHeadersArea:Hl};var sr=(0,we.Jj)(),dr="dx-area-data-cell",Ut="dx-area-row-cell",cr="dx-area-column-cell",Wt="dx-area-description-cell",ur="dx-pivotgrid-border",hr="dx-pivotgrid",st="dx-bottom-border",vr="dx-area-fields",gr="dx-incompressible-fields",zt="dx-overflow-hidden",dt="<tr>",Oe="<td>",Pe="<div>",mr=["allowSorting","allowSortingBySummary","allowFiltering","allowExpandAll"];function Jt(e){var a=0;return(0,Z.S6)(e,(t,r)=>{a+=r||0}),a}function Qt(e,a){for(var t=a/e.length,r=0;r<e.length;r+=1)e[r]-=t}function fr(e){e.off("scroll").off("stop")}function _r(e,a){var t="width"===a?["borderLeftWidth","borderRightWidth"]:["borderTopWidth","borderBottomWidth"],r=0;return(0,Z.S6)(e,(n,l)=>{var h=sr.getComputedStyle(l.get(0));t.forEach(s=>{r+=parseFloat(h[s])||0})}),r}var Kt=na.Z.inherit({_getDefaultOptions(){return(0,X.l)(this.callBase(),{scrolling:{timeout:300,renderingThreshold:150,minTimeout:10,mode:"standard",useNative:"auto",removeInvisiblePages:!0,virtualRowHeight:50,virtualColumnWidth:100,loadTwoPagesOnStart:!0},encodeHtml:!0,dataSource:null,activeStateEnabled:!1,fieldChooser:{minWidth:250,minHeight:250,enabled:!0,allowSearch:!1,searchTimeout:500,layout:0,title:ie.Z.format("dxPivotGrid-fieldChooserTitle"),width:600,height:600,applyChangesMode:"instantly"},onContextMenuPreparing:null,allowSorting:!1,allowSortingBySummary:!1,allowFiltering:!1,allowExpandAll:!1,wordWrapEnabled:!0,fieldPanel:{showColumnFields:!0,showFilterFields:!0,showDataFields:!0,showRowFields:!0,allowFieldDragging:!0,visible:!1,texts:{columnFieldArea:ie.Z.format("dxPivotGrid-columnFieldArea"),rowFieldArea:ie.Z.format("dxPivotGrid-rowFieldArea"),filterFieldArea:ie.Z.format("dxPivotGrid-filterFieldArea"),dataFieldArea:ie.Z.format("dxPivotGrid-dataFieldArea")}},dataFieldArea:"column",export:{enabled:!1,fileName:"PivotGrid"},showRowTotals:!0,showRowGrandTotals:!0,showColumnTotals:!0,showColumnGrandTotals:!0,hideEmptySummaryCells:!0,showTotalsPrior:"none",rowHeaderLayout:"standard",loadPanel:{enabled:!0,text:ie.Z.format("Loading"),width:200,height:70,showIndicator:!0,indicatorSrc:"",showPane:!0},texts:{grandTotal:ie.Z.format("dxPivotGrid-grandTotal"),total:ie.Z.getFormatter("dxPivotGrid-total"),noData:ie.Z.format("dxDataGrid-noDataText"),showFieldChooser:ie.Z.format("dxPivotGrid-showFieldChooser"),expandAll:ie.Z.format("dxPivotGrid-expandAll"),collapseAll:ie.Z.format("dxPivotGrid-collapseAll"),sortColumnBySummary:ie.Z.getFormatter("dxPivotGrid-sortColumnBySummary"),sortRowBySummary:ie.Z.getFormatter("dxPivotGrid-sortRowBySummary"),removeAllSorting:ie.Z.format("dxPivotGrid-removeAllSorting"),exportToExcel:ie.Z.format("dxDataGrid-exportToExcel"),dataNotAvailable:ie.Z.format("dxPivotGrid-dataNotAvailable")},onCellClick:null,onCellPrepared:null,showBorders:!1,stateStoring:{enabled:!1,storageKey:null,type:"localStorage",customLoad:null,customSave:null,savingTimeout:2e3},onExpandValueChanging:null,renderCellCountLimit:2e4,onExporting:null,onExported:null,onFileSaving:null,headerFilter:{width:252,height:325,allowSearch:!1,showRelevantValues:!1,searchTimeout:500,texts:{emptyValue:ie.Z.format("dxDataGrid-headerFilterEmptyValue"),ok:ie.Z.format("dxDataGrid-headerFilterOK"),cancel:ie.Z.format("dxDataGrid-headerFilterCancel")}}})},_updateCalculatedOptions(e){var a=this;(0,Z.S6)(e,(t,r)=>{(0,Z.S6)(mr,(n,l)=>{(void 0===r[l]||r._initProperties&&l in r._initProperties&&void 0===r._initProperties[l])&&Ce(r,l,a.option(l))})})},_getDataControllerOptions(){var e=this;return{component:e,dataSource:e.option("dataSource"),texts:e.option("texts"),showRowTotals:e.option("showRowTotals"),showRowGrandTotals:e.option("showRowGrandTotals"),showColumnTotals:e.option("showColumnTotals"),showTotalsPrior:e.option("showTotalsPrior"),showColumnGrandTotals:e.option("showColumnGrandTotals"),dataFieldArea:e.option("dataFieldArea"),rowHeaderLayout:e.option("rowHeaderLayout"),hideEmptySummaryCells:e.option("hideEmptySummaryCells"),onFieldsPrepared(a){e._updateCalculatedOptions(a)}}},_initDataController(){var e=this;e._dataController&&e._dataController.dispose(),e._dataController=new Ji.DataController(e._getDataControllerOptions()),(0,we.Ym)()&&e._dataController.changed.add(()=>{e._render()}),e._dataController.scrollChanged.add(t=>{e._scrollLeft=t.left,e._scrollTop=t.top}),e._dataController.loadingChanged.add(()=>{e._updateLoading()}),e._dataController.progressChanged.add(e._updateLoading.bind(e)),e._dataController.dataSourceChanged.add(()=>{e._trigger("onChanged")});var a=e.option("onExpandValueChanging");a&&e._dataController.expandValueChanging.add(t=>{a(t)})},_init(){this.callBase(),this._initDataController(),this._scrollLeft=this._scrollTop=null,this._initActions()},_initActions(){this._actions={onChanged:this._createActionByOption("onChanged"),onContextMenuPreparing:this._createActionByOption("onContextMenuPreparing"),onCellClick:this._createActionByOption("onCellClick"),onExporting:this._createActionByOption("onExporting"),onExported:this._createActionByOption("onExported"),onFileSaving:this._createActionByOption("onFileSaving"),onCellPrepared:this._createActionByOption("onCellPrepared")}},_trigger(e,a){this._actions[e](a)},_optionChanged(e){if(mr.includes(e.name)){var a=this.getDataSource().fields();this._updateCalculatedOptions(a)}switch(e.name){case"dataSource":case"allowSorting":case"allowFiltering":case"allowExpandAll":case"allowSortingBySummary":case"scrolling":case"stateStoring":this._initDataController(),this._fieldChooserPopup.hide(),this._renderFieldChooser(),this._invalidate();break;case"texts":case"showTotalsPrior":case"showRowTotals":case"showRowGrandTotals":case"showColumnTotals":case"showColumnGrandTotals":case"hideEmptySummaryCells":case"dataFieldArea":this._dataController.updateViewOptions(this._getDataControllerOptions());break;case"useNativeScrolling":case"encodeHtml":case"renderCellCountLimit":case"onExpandValueChanging":break;case"rtlEnabled":this.callBase(e),this._renderFieldChooser(),this._renderContextMenu(),(0,we.Ym)()&&this._renderLoadPanel(this._dataArea.groupElement(),this.$element()),this._invalidate();break;case"export":this._renderDescriptionArea();break;case"onCellClick":case"onContextMenuPreparing":case"onExporting":case"onExported":case"onFileSaving":case"onCellPrepared":this._actions[e.name]=this._createActionByOption(e.name);break;case"fieldChooser":this._renderFieldChooser(),this._renderDescriptionArea();break;case"loadPanel":(0,we.Ym)()&&("loadPanel.enabled"===e.fullName?(clearTimeout(this._hideLoadingTimeoutID),this._renderLoadPanel(this._dataArea.groupElement(),this.$element())):(this._renderLoadPanel(this._dataArea.groupElement(),this.$element()),this._invalidate()));break;case"fieldPanel":this._renderDescriptionArea(),this._invalidate();break;case"headerFilter":this._renderFieldChooser(),this._invalidate();break;case"showBorders":this._tableElement().toggleClass(ur,!!e.value),this.updateDimensions();break;case"wordWrapEnabled":this._tableElement().toggleClass("dx-word-wrap",!!e.value),this.updateDimensions();break;case"rowHeaderLayout":this._tableElement().find(".".concat(Ut)).toggleClass("dx-area-tree-view","tree"===e.value),this._dataController.updateViewOptions(this._getDataControllerOptions());break;case"height":case"width":this._hasHeight=null,this.callBase(e),this.resize();break;default:this.callBase(e)}},_updateScrollPosition(e,a,t){var n,l,r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],h=this._scrollTop||this._scrollLeft;this._scrollUpdating||(this._scrollUpdating=!0,a&&!a.hasScroll()&&this._hasHeight&&(this._scrollTop=null),e&&!e.hasScroll()&&(this._scrollLeft=null),(null!==this._scrollTop||null!==this._scrollLeft||h||this.option("rtlEnabled"))&&(t.scrollTo({left:l=this._scrollLeft||0,top:n=this._scrollTop||0},r),e.scrollTo({left:l},r),a.scrollTo({top:n},r),this._dataController.updateWindowScrollPosition(this._scrollTop)),this._scrollUpdating=!1)},_subscribeToEvents(e,a,t){var r=this;(0,Z.S6)([e,a,t],(n,l)=>{!function $l(e,a){fr(e),e.on("scroll",a).on("stop",a)}(l,h=>function(s,d){var{scrollOffset:f}=s,v=d._getScrollable(),g="vertical"!==v.option("direction")?f.left:r._scrollLeft,x="horizontal"!==v.option("direction")&&r._hasHeight?f.top:r._scrollTop;((r._scrollLeft||0)!==(g||0)||(r._scrollTop||0)!==(x||0))&&(r._scrollLeft=g,r._scrollTop=x,r._updateScrollPosition(e,a,t),"virtual"===r.option("scrolling.mode")&&r._dataController.setViewportPosition(r._scrollLeft,r._scrollTop))}(h,l))}),!r._hasHeight&&r._dataController.subscribeToWindowScrollEvents(t.groupElement())},_clean:me.ZT,_needDelayResizing(e){return e.length*(e.length?e[0].length:0)>this.option("renderCellCountLimit")},_renderFieldChooser(){var e,a=this,t=a._pivotGridContainer,r=a.option("fieldChooser")||{},n="onDemand"===r.applyChangesMode?[{toolbar:"bottom",location:"after",widget:"dxButton",options:{text:ie.Z.format("OK"),onClick(){a._fieldChooserPopup.$content().dxPivotGridFieldChooser("applyChanges"),a._fieldChooserPopup.hide()}}},{toolbar:"bottom",location:"after",widget:"dxButton",options:{text:ie.Z.format("Cancel"),onClick(){a._fieldChooserPopup.hide()}}}]:[],l={layout:r.layout,texts:r.texts||{},dataSource:a.getDataSource(),allowSearch:r.allowSearch,searchTimeout:r.searchTimeout,width:void 0,height:void 0,headerFilter:a.option("headerFilter"),encodeHtml:null!==(e=a.option("fieldChooser.encodeHtml"))&&void 0!==e?e:a.option("encodeHtml"),applyChangesMode:r.applyChangesMode,onContextMenuPreparing(s){a._trigger("onContextMenuPreparing",s)}},h={shading:!1,title:r.title,width:r.width,height:r.height,showCloseButton:!0,resizeEnabled:!0,minWidth:r.minWidth,minHeight:r.minHeight,toolbarItems:n,onResize(s){s.component.$content().dxPivotGridFieldChooser("updateDimensions")},onShown(s){a._createComponent(s.component.content(),kt,l)},onHidden(s){var d=s.component.$content().dxPivotGridFieldChooser("instance");d.resetTreeView(),d.cancelChanges()}};a._fieldChooserPopup?(a._fieldChooserPopup.option(h),a._fieldChooserPopup.$content().dxPivotGridFieldChooser(l)):a._fieldChooserPopup=a._createComponent((0,M.Z)(Pe).addClass("dx-fieldchooser-popup").appendTo(t),ra.Z,h)},_renderContextMenu(){var e=this,a=e._pivotGridContainer;e._contextMenu&&e._contextMenu.$element().remove(),e._contextMenu=e._createComponent((0,M.Z)(Pe).appendTo(a),aa.Z,{onPositioning(t){var{event:r}=t;if(t.cancel=!0,r){var n=r.target.cellIndex>=0?r.target:(0,M.Z)(r.target).closest("td").get(0);if(n){var l=e._createEventArgs(n,r),h=e._getContextMenuItems(l);h&&(t.component.option("items",h),t.cancel=!1)}}},onItemClick(t){t.itemData.onItemClick&&t.itemData.onItemClick(t)},cssClass:hr,target:e.$element()})},_getContextMenuItems(e){var a=this,t=[],r=a.option("texts");if("row"===e.area||"column"===e.area){var n=e["".concat(e.area,"Fields")],l=e["column"===e.area?"rowFields":"columnFields"],h=e.cell.path&&n[e.cell.path.length-1],s=a.getDataSource();if(h&&h.allowExpandAll&&e.cell.path.length<e["".concat(e.area,"Fields")].length&&!s.paginate()&&(t.push({beginGroup:!0,icon:"none",text:r.expandAll,onItemClick(){s.expandAll(h.index)}}),t.push({text:r.collapseAll,icon:"none",onItemClick(){s.collapseAll(h.index)}})),e.cell.isLast&&!s.paginate()){var d=0;(0,Z.S6)(l,(f,v)=>{!v.allowSortingBySummary||(0,Z.S6)(e.dataFields,(g,x)=>{if(!(0,H.$K)(e.cell.dataIndex)||e.cell.dataIndex===g){var A=!(0,H.$K)(e.cell.dataIndex)&&e.dataFields.length>1,_="column"===e.area?r.sortColumnBySummary:r.sortRowBySummary,y=Ee(e.dataFields,v.sortBySummaryField)===g&&(e.cell.path||[]).join("/")===(v.sortBySummaryPath||[]).join("/"),V=(0,de.WU)(_,A?"".concat(v.caption," - ").concat(x.caption):v.caption);t.push({beginGroup:0===d,icon:y?"desc"===v.sortOrder?"sortdowntext":"sortuptext":"none",text:V,onItemClick(){s.field(v.index,{sortBySummaryField:x.name||x.caption||x.dataField,sortBySummaryPath:e.cell.path,sortOrder:"desc"===v.sortOrder?"asc":"desc"}),s.load()}}),d+=1}})}),(0,Z.S6)(l,(f,v)=>{if(v.allowSortingBySummary&&(0,H.$K)(v.sortBySummaryField))return t.push({beginGroup:0===d,icon:"none",text:r.removeAllSorting,onItemClick(){(0,Z.S6)(l,(g,x)=>{s.field(x.index,{sortBySummaryField:void 0,sortBySummaryPath:void 0,sortOrder:void 0})}),s.load()}}),!1})}}if(a.option("fieldChooser.enabled")&&t.push({beginGroup:!0,icon:"columnchooser",text:r.showFieldChooser,onItemClick(){a._fieldChooserPopup.show()}}),a.option("export.enabled")&&t.push({beginGroup:!0,icon:"xlsxfile",text:r.exportToExcel,onItemClick(){a.exportToExcel()}}),e.items=t,a._trigger("onContextMenuPreparing",e),(t=e.items)&&t.length)return t},_createEventArgs(e,a){var t=this.getDataSource(),r={rowFields:t.getAreaFields("row"),columnFields:t.getAreaFields("column"),dataFields:t.getAreaFields("data"),event:a};return function kl(e){return e.closest(".".concat(vr)).length||e.find(".".concat(vr)).length}((0,M.Z)(e))?(0,X.l)(this._createFieldArgs(e),r):(0,X.l)(this._createCellArgs(e),r)},_createFieldArgs(e){var a=(0,M.Z)(e).children().data("field"),t={field:a};return(0,H.$K)(a)?t:{}},_createCellArgs(e){var a=(0,M.Z)(e),t=e.cellIndex,{rowIndex:r}=e.parentElement,n=a.closest("table"),l=n.data("data"),h=l&&l[r]&&l[r][t];return{area:n.data("area"),rowIndex:r,columnIndex:t,cellElement:(0,ta.u)(a),cell:h}},_handleCellClick(e){var a=this,t=a._createEventArgs(e.currentTarget,e),{cell:r}=t;!r||!t.area&&(t.rowIndex||t.columnIndex)||(a._trigger("onCellClick",t),r&&!t.cancel&&(0,H.$K)(r.expanded)&&setTimeout(()=>{a._dataController[r.expanded?"collapseHeaderItem":"expandHeaderItem"](t.area,r.path)}))},_getNoDataText(){return this.option("texts.noData")},_renderNoDataText:gt.Z.renderNoDataText,_renderLoadPanel:gt.Z.renderLoadPanel,_updateLoading(e){var a=this,t=a._dataController.isLoading();if(a._loadPanel){var r=a._loadPanel.option("visible");if(r||(a._startLoadingTime=new Date),t&&(e?new Date-a._startLoadingTime>=1e3&&a._loadPanel.option("message","".concat(Math.floor(100*e),"%")):a._loadPanel.option("message",a.option("loadPanel.text"))),clearTimeout(a._hideLoadingTimeoutID),r&&!t)a._hideLoadingTimeoutID=setTimeout(()=>{a._loadPanel.option("visible",!1),a.$element().removeClass(zt)});else{var n={visible:t};t&&(n.position=gt.Z.calculateLoadPanelPosition(a._dataArea.groupElement())),a._loadPanel.option(n),a.$element().toggleClass(zt,!t)}}},_renderDescriptionArea(){var h,e=this.$element(),a=e.find(".".concat(Wt)),t=(0,M.Z)(Pe).addClass("dx-pivotgrid-toolbar"),r=this.option("fieldPanel"),n=e.find(".dx-filter-header"),l=e.find(".dx-column-header");if(h=r.visible&&r.showFilterFields?n:r.visible&&(r.showDataFields||r.showColumnFields)?l:a,l.toggleClass(st,!(!r.visible||!r.showDataFields&&!r.showColumnFields)),n.toggleClass(st,!(!r.visible||!r.showFilterFields)),a.toggleClass("dx-pivotgrid-background",r.visible&&(r.showDataFields||r.showColumnFields||r.showRowFields)),this.$element().find(".dx-pivotgrid-toolbar").remove(),t.prependTo(h),this.option("fieldChooser.enabled")){var s=(0,M.Z)(Pe).appendTo(t).addClass("dx-pivotgrid-field-chooser-button"),d={icon:"columnchooser",hint:this.option("texts.showFieldChooser"),onClick:()=>{this.getFieldChooserPopup().show()}};this._createComponent(s,"dxButton",d)}if(this.option("export.enabled")){var f=(0,M.Z)(Pe).appendTo(t).addClass("dx-pivotgrid-export-button"),v={icon:"xlsxfile",hint:this.option("texts.exportToExcel"),onClick:()=>{this.exportToExcel()}};this._createComponent(f,"dxButton",v)}},_detectHasContainerHeight(){var e=this.$element();if((0,H.$K)(this._hasHeight)){var a=this.option("height")||this.$element().get(0).style.height;a&&this._hasHeight^"auto"!==a&&(this._hasHeight=null)}if(!(0,H.$K)(this._hasHeight)&&!e.is(":hidden")){this._pivotGridContainer.addClass("dx-hidden");var t=(0,M.Z)(Pe);(0,ae.QQ)(t,66666),e.append(t),this._hasHeight=66666!==(0,ae.Cr)(e),this._pivotGridContainer.removeClass("dx-hidden"),t.remove()}},_renderHeaders(e,a,t,r){var n=this.getDataSource();this._rowFields=this._rowFields||new Ge(this,"row"),this._rowFields.render(e,n.getAreaFields("row")),this._columnFields=this._columnFields||new Ge(this,"column"),this._columnFields.render(a,n.getAreaFields("column")),this._filterFields=this._filterFields||new Ge(this,"filter"),this._filterFields.render(t,n.getAreaFields("filter")),this._dataFields=this._dataFields||new Ge(this,"data"),this._dataFields.render(r,n.getAreaFields("data")),this.$element().dxPivotGridFieldChooserBase("instance").renderSortable()},_createTableElement(){var e=(0,M.Z)("<table>").css({width:"100%"}).toggleClass(ur,!!this.option("showBorders")).toggleClass("dx-word-wrap",!!this.option("wordWrapEnabled"));return Te.Z.on(e,(0,Ze.V4)(vt.u,"dxPivotGrid"),"td",this._handleCellClick.bind(this)),e},_renderDataArea(e){var a=this._dataArea||new ci.DataArea(this);return this._dataArea=a,a.render(e,this._dataController.getCellsInfo()),a},_renderRowsArea(e){var a=this._rowsArea||new or.VerticalHeadersArea(this);return this._rowsArea=a,a.render(e,this._dataController.getRowsInfo()),a},_renderColumnsArea(e){var a=this._columnsArea||new or.HorizontalHeadersArea(this);return this._columnsArea=a,a.render(e,this._dataController.getColumnsInfo()),a},_initMarkup(){this.callBase.apply(this,arguments),this.$element().addClass(hr)},_renderContentImpl(){var e,a,t,r,l,h,s,d,n=!this._pivotGridContainer;(r=!n&&this._tableElement())||(this.$element().addClass("dx-row-lines").addClass("dx-pivotgrid-fields-container"),this._pivotGridContainer=(0,M.Z)(Pe).addClass("dx-pivotgrid-container"),this._renderFieldChooser(),this._renderContextMenu(),e=(0,M.Z)(Oe).addClass(cr),a=(0,M.Z)(Oe).addClass(Ut),t=(0,M.Z)(Oe).addClass(dr),r=this._createTableElement(),d=(0,M.Z)(Oe).addClass("dx-data-header"),s=(0,M.Z)("<td>").attr("colspan","2").addClass("dx-filter-header"),h=(0,M.Z)(Oe).addClass("dx-column-header"),l=(0,M.Z)(Oe).addClass(Wt),(0,M.Z)(dt).append(s).appendTo(r),(0,M.Z)(dt).append(d).append(h).appendTo(r),(0,M.Z)(dt).append(l).append(e).appendTo(r),(0,M.Z)(dt).addClass("dx-bottom-row").append(a).append(t).appendTo(r),this._pivotGridContainer.append(r),this.$element().append(this._pivotGridContainer),"tree"===this.option("rowHeaderLayout")&&a.addClass("dx-area-tree-view")),this.$element().addClass(zt),this._createComponent(this.$element(),ot,{dataSource:this.getDataSource(),encodeHtml:this.option("encodeHtml"),allowFieldDragging:this.option("fieldPanel.allowFieldDragging"),headerFilter:this.option("headerFilter"),visible:this.option("visible"),remoteSort:"virtual"===this.option("scrolling.mode")});var f=this._renderDataArea(t),v=this._renderRowsArea(a),g=this._renderColumnsArea(e);f.tableElement().prepend(g.headElement()),n&&(this._renderLoadPanel(f.groupElement().parent(),this.$element()),this._renderDescriptionArea(),v.renderScrollable(),g.renderScrollable(),f.renderScrollable()),[f,v,g].forEach(x=>{fr(x)}),this._renderHeaders(l,h,s,d),this._update(n)},_update(e){var a=this,t=function(){a.updateDimensions()};a._needDelayResizing(a._dataArea.getData())&&e?setTimeout(t):t()},_fireContentReadyAction(){this._dataController.isLoading()||this.callBase()},getScrollPath(e){return"column"===e?this._columnsArea.getScrollPath(this._scrollLeft):this._rowsArea.getScrollPath(this._scrollTop)},getDataSource(){return this._dataController.getDataSource()},getFieldChooserPopup(){return this._fieldChooserPopup},hasScroll(e){return"column"===e?this._columnsArea.hasScroll():this._rowsArea.hasScroll()},_dimensionChanged(){this.updateDimensions()},_visibilityChanged(e){e&&this.updateDimensions()},_dispose(){clearTimeout(this._hideLoadingTimeoutID),this.callBase.apply(this,arguments),this._dataController&&this._dataController.dispose()},_tableElement(){return this.$element().find("table").first()},addWidgetPrefix:e=>"dx-pivotgrid-".concat(e),resize(){this.updateDimensions()},isReady(){return this.callBase()&&!this._dataController.isLoading()},updateDimensions(){var a,r,s,d,e=this,t=e._tableElement(),n=0,l=0,h=0,f=t.find(".".concat(dr)),v=t.find(".".concat(Ut)),g=t.find(".".concat(cr)),x=t.find(".".concat(Wt)),A=t.find(".dx-filter-header"),_=t.find(".dx-column-header"),y=e._rowFields,V=new J.BH;if((0,we.Ym)()){var N=y.isVisible()&&"tree"!==e.option("rowHeaderLayout");e._detectHasContainerHeight(),e._dataArea.headElement().length||e._dataArea.tableElement().prepend(e._columnsArea.headElement()),N&&(e._rowsArea.updateColspans(y.getColumnsCount()),e._rowsArea.tableElement().prepend(y.headElement())),t.addClass(gr),e._dataArea.reset(),e._rowsArea.reset(),e._columnsArea.reset(),y.reset();var U=(o,u)=>u-o>=1,C=(o,u,c,p,w)=>c?o:u+(p?w:0);return(0,me.Su)(()=>{var o=e._rowsArea.getRowsHeight(),u=(0,ae.zp)(x[0],!0)+(N?o[0]:0),c=0,p=0;if(e._hasHeight){c=(0,ae.Cr)(A);var w=t.find(".dx-data-header"),S=(0,ae.Cr)(w);r=_r([g,f,t,_,A],"height"),p=(0,ae.Cr)(e.$element())-c-S-(Math.max((0,ae.Cr)(e._dataArea.headElement()),(0,ae.Cr)(g),u)+r)}var E=e._dataArea.getScrollbarWidth(),T=U(p,(0,ae.Cr)(e._dataArea.tableElement()));e._dataArea.tableElement().css({width:e._hasHeight&&T&&E?"calc(100% - ".concat(E,"px)"):"100%"});var b=e._dataArea.getColumnsWidth(),I=N?o.slice(1):o,P=e._dataArea.getRowsHeight(),m=e._dataController.getColumnsInfo().length,D=sa(I,P.slice(m)),F=P.slice(0,m),O=Jt(F),R=e._rowsArea.getColumnsWidth();if(n=(0,ae.dz)(e._dataArea.tableElement()),l=Jt(D),n&&l){h=Jt(R);var B=(0,ae.dz)(e.$element());r=_r([v,f,t],"width");var z=n-(a=(a=B-h-r)>0?a:n);z>=0&&z<=2&&(Qt(b,z),n=a),s=e._hasHeight&&U(p,l),d=U(a,n);var $=C(p,l,s,d,E);(0,me.g0)(()=>{if(e._columnsArea.tableElement().append(e._dataArea.headElement()),y.tableElement().append(e._rowsArea.headElement()),u>O&&(Qt(F,O-u),e._columnsArea.setRowsHeight(F)),t.removeClass(gr),_.children().css("maxWidth",a),e._columnsArea.setGroupWidth(a),e._columnsArea.processScrollBarSpacing(s?E:0),e._columnsArea.setColumnsWidth(b),e._rowsArea.setGroupHeight(e._hasHeight?$:"auto"),e._rowsArea.processScrollBarSpacing(d?E:0),e._rowsArea.setColumnsWidth(R),e._rowsArea.setRowsHeight(D),e._dataArea.setColumnsWidth(b),e._dataArea.setRowsHeight(D),e._dataArea.setGroupWidth(a),e._dataArea.setGroupHeight(e._hasHeight?$:"auto"),N&&y.setColumnsWidth(R),f.toggleClass(st,!s),v.toggleClass(st,!s),!e._hasHeight&&B!==(0,ae.dz)(e.$element())){var W=B-(0,ae.dz)(e.$element());d||(Qt(b,W),e._columnsArea.setColumnsWidth(b),e._dataArea.setColumnsWidth(b)),e._dataArea.setGroupWidth(a-W),e._columnsArea.setGroupWidth(a-W)}if(e._hasHeight&&e._filterFields.isVisible()&&(0,ae.Cr)(A)!==c){var Q=(0,ae.Cr)(A)-c;if(Q>0){s=U(p-Q,l);var k=C(p-Q,l,s,d,E);e._dataArea.setGroupHeight(k),e._rowsArea.setGroupHeight(k)}}var q=e.option("scrolling");"virtual"===q.mode&&e._setVirtualContentParams(q,b,D,a,$,e._hasHeight,h);var ee=[];e._dataArea.updateScrollableOptions({direction:e._dataArea.getScrollableDirection(d,s),rtlEnabled:e.option("rtlEnabled")}),e._columnsArea.updateScrollableOptions({rtlEnabled:e.option("rtlEnabled")}),(0,Z.S6)([e._columnsArea,e._rowsArea,e._dataArea],(Y,j)=>{ee.push(j&&j.updateScrollable())}),e._updateLoading(),e._renderNoDataText(f),J.gx.apply(M.Z,ee).done(()=>{e._updateScrollPosition(e._columnsArea,e._rowsArea,e._dataArea,!0),e._subscribeToEvents(e._columnsArea,e._rowsArea,e._dataArea),V.resolve()})})}else V.resolve()}),V}},_setVirtualContentParams(e,a,t,r,n,l,h){var s=this._dataController.calculateVirtualContentParams({virtualRowHeight:e.virtualRowHeight,virtualColumnWidth:e.virtualColumnWidth,itemWidths:a,itemHeights:t,rowCount:t.length,columnCount:a.length,viewportWidth:r,viewportHeight:l?n:(0,ae.zp)(sr)});this._dataArea.setVirtualContentParams({top:s.contentTop,left:s.contentLeft,width:s.width,height:s.height}),this._rowsArea.setVirtualContentParams({top:s.contentTop,width:h,height:s.height}),this._columnsArea.setVirtualContentParams({left:s.contentLeft,width:s.width,height:(0,ae.Cr)(this._columnsArea.groupElement())})},applyPartialDataSource(e,a,t){this._dataController.applyPartialDataSource(e,a,t)}}).inherit(Wa).include(pa);(0,Ue.Z)("dxPivotGrid",Kt);let Wl=(()=>{class e{}return e.\u0275fac=function(t){return new(t||e)},e.\u0275mod=i.oAB({type:e}),e.\u0275inj=i.cJS({imports:[[te.sd1,te.RbS,te.FZZ,te.HaS,te.jq_,te.lj9,te.SUG,te.mtS,te.UCw,te.nD3,te.WyY,te.YOZ,pe.Lz,pe.ie,ea.Cu],te.sd1,te.RbS,te.FZZ,te.HaS,te.jq_,te.lj9,te.SUG,te.mtS,te.UCw,te.nD3,te.WyY,te.YOZ,pe.ie]}),e})();var zl=L(10915);let Jl=(()=>{class e{}return e.\u0275fac=function(t){return new(t||e)},e.\u0275mod=i.oAB({type:e}),e.\u0275inj=i.cJS({providers:[ht.L],imports:[ut.ez,Hn,Sr.t,wr.Z,yr.m,Bn.k,Nn._,Gn.O,$e.x,zl.K,Wl]}),e})()}}]);