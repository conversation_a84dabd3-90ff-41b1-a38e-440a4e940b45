trigger:
  branches:
    include:
    - main
    - develop
  paths:
    include:
    - wheres-my-order/*

variables:
  - group: kraken-variables

stages:
- stage: Build
  displayName: Build and Test
  jobs:
  - job: Build
    displayName: Build
    pool:
      vmImage: ubuntu-latest
    steps:
    - task: NodeTool@0
      displayName: 'Use Node.js 16.x'
      inputs:
        versionSpec: '16.x'

    - task: Npm@1
      displayName: 'Install dependencies'
      inputs:
        command: 'ci'
        workingDir: 'wheres-my-order'

    - task: Npm@1
      displayName: 'Run tests'
      inputs:
        command: 'custom'
        customCommand: 'run test:ci'
        workingDir: 'wheres-my-order'

    - task: Npm@1
      displayName: 'Build application'
      inputs:
        command: 'custom'
        customCommand: 'run build:prod'
        workingDir: 'wheres-my-order'

    - task: PublishBuildArtifacts@1
      displayName: 'Publish build artifacts'
      inputs:
        PathtoPublish: 'wheres-my-order/dist'
        ArtifactName: 'frontend-build'

- stage: DeployDev
  displayName: Deploy to Development
  dependsOn: Build
  condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/develop'))
  jobs:
  - deployment: DeployDev
    displayName: Deploy to Development
    pool:
      vmImage: ubuntu-latest
    environment: 'kraken-dev'
    strategy:
      runOnce:
        deploy:
          steps:
          - task: DownloadBuildArtifacts@0
            inputs:
              buildType: 'current'
              downloadType: 'single'
              artifactName: 'frontend-build'
              downloadPath: '$(System.ArtifactsDirectory)'

          - task: AzureCLI@2
            displayName: 'Deploy to Storage Account'
            inputs:
              azureSubscription: 'azure-service-connection'
              scriptType: 'bash'
              scriptLocation: 'inlineScript'
              inlineScript: |
                # Update environment configuration
                API_URL=$(az containerapp show --name "ca-kraken-api-dev" --resource-group "rg-kraken-dev-001" --query "properties.configuration.ingress.fqdn" -o tsv)
                
                # Replace placeholders in built files
                find $(System.ArtifactsDirectory)/frontend-build -name "*.js" -exec sed -i "s|___API_URL___|https://${API_URL}/api|g" {} \;
                
                # Upload to storage
                az storage blob upload-batch \
                  --account-name "stakrakendev001" \
                  --destination '$web' \
                  --source "$(System.ArtifactsDirectory)/frontend-build/wheres-my-order" \
                  --overwrite

- stage: DeployProd
  displayName: Deploy to Production
  dependsOn: Build
  condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/main'))
  jobs:
  - deployment: DeployProd
    displayName: Deploy to Production
    pool:
      vmImage: ubuntu-latest
    environment: 'kraken-prod'
    strategy:
      runOnce:
        deploy:
          steps:
          - task: DownloadBuildArtifacts@0
            inputs:
              buildType: 'current'
              downloadType: 'single'
              artifactName: 'frontend-build'
              downloadPath: '$(System.ArtifactsDirectory)'

          - task: AzureCLI@2
            displayName: 'Deploy to Storage Account'
            inputs:
              azureSubscription: 'azure-service-connection'
              scriptType: 'bash'
              scriptLocation: 'inlineScript'
              inlineScript: |
                # Update environment configuration
                API_URL=$(az containerapp show --name "ca-kraken-api-prod" --resource-group "rg-kraken-prod-001" --query "properties.configuration.ingress.fqdn" -o tsv)
                
                # Replace placeholders in built files
                find $(System.ArtifactsDirectory)/frontend-build -name "*.js" -exec sed -i "s|___API_URL___|https://${API_URL}/api|g" {} \;
                
                # Upload to storage
                az storage blob upload-batch \
                  --account-name "stakrakenprod001" \
                  --destination '$web' \
                  --source "$(System.ArtifactsDirectory)/frontend-build/wheres-my-order" \
                  --overwrite
