<div class="user-panel"
     (click)="onUserPanelClick($event)">
    <div class="user-info">
        <div class="image-container">
            <img src="assets/images/defaultAvatarSmall.png"
                 alt="">
        </div>
        <div class="user-name">
            {{displayName}}</div>
    </div>
    <div class="user-button"></div> <!-- Target div for dx-context-menu -->
    <dx-context-menu *ngIf="menuMode === 'context'"
                     [items]="menuItems"
                     target=".user-button"
                     [showEvent]="isFromOverview ? '' : 'dxclick'"
                     [position]="{ my: 'top center', at: 'bottom center' }"
                     cssClass="user-menu">
    </dx-context-menu>
    <dx-list *ngIf="menuMode === 'list'"
             class="dx-toolbar-menu-action"
             [items]="menuItems">
    </dx-list>
</div>