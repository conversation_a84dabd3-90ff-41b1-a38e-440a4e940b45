<div *ngIf="!isLoading" >
<div class="dx-card content-block responsive-paddings">
    <dx-range-selector *ngIf="valueStart && valueEnd && rangeStart && rangeEnd"
                       id="range-selector"
                       [value]="[valueStart, valueEnd]"
                       (onInitialized)="onRangeSelectorInitialized($event)"
                       (onValueChanged)="onRangeSelectorValueChanged($event)">
        <dxo-scale [startValue]="rangeStart"
                   [endValue]="rangeEnd">
        </dxo-scale>
        <dxo-slider-marker format="monthAndDay"></dxo-slider-marker>
    </dx-range-selector>

    <dx-data-grid #historyGrid
                  [dataSource]="selected"
                  (onToolbarPreparing)="onToolbarPreparing($event)">
        <dxo-selection mode="multiple"></dxo-selection>
        <dxo-search-panel [visible]="true"
                          placeholder="Search..."></dxo-search-panel>
        <dxo-filter-panel [visible]="true"></dxo-filter-panel>

        <dxi-column caption="User Id"
                    [allowSorting]="true"
                    [allowSearch]="true"
                    [calculateCellValue]="calculateUserId"
                    dataType="string"></dxi-column>
        <dxi-column dataField="createdAt"
                    dataType="datetime"></dxi-column>

        <dxi-column dataField="type"
                    dataType="string"></dxi-column>

        <dxi-column caption="Object Updated"
                    [allowSorting]="true"
                    [allowSearch]="true"
                    dataType="string"
                    [calculateCellValue]="calculateId"></dxi-column>

        <dxi-column type="buttons"
                    [width]="110">
            <dxi-button icon="showpanel"
                        hint="diff"
                        [onClick]="diffClicked"></dxi-button>
        </dxi-column>
    </dx-data-grid>

</div>

<dx-popup [(visible)]="showDiff"
          [showCloseButton]="true"
          [hideOnOutsideClick]="true"
          title="Changes">
    <div *dxTemplate="let data of 'content'">
        <dx-scroll-view>
            <p>
                Change made by: <span>{{currentChange?.user?.email ?
                    currentChange.user.email : currentChange.user.id}}</span>

            </p>
            <p>
                Change made at:
                <span>{{currentChange.createdAt | date:'medium'}}</span>
            </p>

            <div style="width: 100%; display: flex;">
                <div style="flex-grow: 1; width: 50%;">
                    <h4>Old</h4>
                    <pre>{{currentChange.old | json}}</pre>
                </div>
                <div style="flex-grow: 1">
                    <h4>New</h4>
                    <pre>{{currentChange.new !== null ? (currentChange.new | json) : 'User has been deleted'}}</pre>
                </div>
            </div>

            <div *ngIf="currentChange | diff as detailedDiff">
                <div *ngFor="let entry of detailedDiff | keyvalue">
                    <h4>{{entry.key}}</h4>
                    <ul>
                        <li *ngFor="let change of entry.value | keyvalue">
                            <strong>{{change.key}}</strong>:
                            <span>{{change.value | json}}</span>
                        </li>
                    </ul>
                </div>
            </div>
        </dx-scroll-view>
    </div>
</dx-popup>
</div>
<div *ngIf="isLoading"
         style="width: 100%; height: 100%;">
        <dx-load-indicator class="centered"
                           id="large-indicator"
                           height="300"
                           width="300"></dx-load-indicator>
    </div>