{"BlobStorage": {"APMStorageAccountName": "stakrakendev001", "APMBlobContainerName": "apm-dev", "APMWOStorageAccountName": "stakrakendev001", "APMWOBlobContainerName": "apm-workorders-dev", "KeyVaultName": "kv-kraken-dev-001"}, "KeyVault": {"VaultName": "kv-kraken-dev-001"}, "ApplicationInsights": {"ConnectionString": "InstrumentationKey=test-key;IngestionEndpoint=https://eastus-8.in.applicationinsights.azure.com/"}, "AzureAd": {"Instance": "https://login.microsoftonline.com/", "TenantId": "test-tenant-id", "ClientId": "test-client-id"}, "Connections": {"KeyVaultName": "kv-kraken-dev-001", "ResourceGroupName": "rg-kraken-dev", "SubscriptionId": "test-subscription-id", "DatabaseName": "cosmos-clientportalapi-dev-001", "AnteaAttachmentsBlobContainer": "antea-attachments-dev", "AnteaSubmissionsBlobContainer": "antea-submissions-dev"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}}