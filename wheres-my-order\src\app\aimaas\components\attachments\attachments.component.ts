import { Component, Input, OnInit } from '@angular/core';
import {
    ToastNotificationService,
    ToastType
} from '../../../shared/services/toast-notification.service';

import { CredoSoftService } from '../../services';

@Component({
    selector: 'app-attachments',
    templateUrl: './attachments.component.html',
    styleUrls: ['./attachments.component.scss']
})
export class AttachmentsComponent implements OnInit {
    //@Input()
    attachments: any[];
    @Input() selectedAssetId: any;

    constructor(
        //private readonly _fileShare: FileShareService,
        private readonly credoService: CredoSoftService,
        private readonly _toasts: ToastNotificationService
    ) {}

    ngOnInit(): void {
        if (this.selectedAssetId) {
            this.credoService
                .getAllAssetAttachments(this.selectedAssetId)
                .subscribe((data) => {
                    this.attachments = data;
                    this.sortAttachments();
                });
        }
    }
    sortAttachments() {
        this.attachments.sort((a, b) => {
            const nameA = a.filename.toLowerCase();
            const nameB = b.filename.toLowerCase();
            return nameA.localeCompare(nameB); // Using localeCompare for simplicity
        });
    }
    download(data: any) {
        if (data) {
            let location: string = data?.key.filelink;
            const newLocation = location.replace(/\\/g, '/');
            let parts = newLocation.replace(/\/\//g, '/').split('/');
            let fileName = parts[parts.length - 1];
            const cleanedFilename = fileName.replace(/\d+$/, '');
            this.credoService
                .downloadAttachment(newLocation.replace(/\/\//g, '/'))
                .subscribe(
                    (file: any) => {
                        this.downloadBase64File(
                            file.data,
                            file.filetype,
                            cleanedFilename
                        );
                    },
                    (err) => {
                        this._toasts.show(
                            ' ',
                            err.error.error,
                            ToastType.warning,
                            3000
                        );
                        console.log(
                            err.error.error,
                            'Error while retrieving the file'
                        );
                    }
                );
        }
    }

    downloadBase64File(
        base64String: string,
        filetype: string,
        fileName: string
    ) {
        const byteCharacters = atob(base64String);
        const byteNumbers = new Array(byteCharacters.length);
        for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);
        const blob = new Blob([byteArray], {
            type: filetype
        });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
    }
    formatFileType(data) {
        switch (data.filetype) {
            case 'application/pdf':
                return 'PDF';
            case 'image/png':
                return 'PNG';
            case 'text/plain':
                return 'TEXT';
            case 'image/jpeg':
                return 'JPEG';
            case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
            case 'application/kswps':
                return 'WORD';
            case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
            case 'application/kset':
                return 'EXCEL';
            case 'image/tiff':
            case 'image/bmp':
            case 'image/vnd.dwg':
                return 'IMAGE';
            case 'application/vnd.ms-outlook':
                return 'OUTLOOK MAIL';
            case 'video/mpeg':
            case 'video/x-ms-wmv':
                return 'VIDEO';
            case 'application/zip':
                return 'ZIP';
            default:
                return data.filetype;
        }
    }
}
