{"ConnectionStrings": {"RemoteMonitoring": "Server=localhost;Database=RemoteMonitoring;Integrated Security=true;"}, "Logging": {"LogLevel": {"Default": "Debug", "System": "Information", "Microsoft": "Warning"}}, "Connections": {"ProjectId": "oneinsight-dev-1279", "FirestoreDb": "fs-clientportalapi-dev-usw1", "Database": "ClientPortalDevTestStaging", "Endpoint": "https://client-portal.documents.azure.com:443/", "AuthKey": "***REMOVED***"}}