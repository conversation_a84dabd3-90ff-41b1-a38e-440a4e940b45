#!/bin/bash

# Azure Backend Deployment Script
# Usage: ./deploy-backend.sh <environment> <subscription-id>

set -e

ENVIRONMENT=${1:-dev}
SUBSCRIPTION_ID=${2}
RESOURCE_GROUP="rg-kraken-${ENVIRONMENT}-001"
CONTAINER_REGISTRY="krakenacr"
APP_NAME="kraken-api"
IMAGE_TAG="latest"

echo "🚀 Starting deployment to ${ENVIRONMENT} environment..."

# Login and set subscription
az login --service-principal -u $AZURE_CLIENT_ID -p $AZURE_CLIENT_SECRET --tenant $AZURE_TENANT_ID
az account set --subscription $SUBSCRIPTION_ID

# Build and push Docker image
echo "📦 Building Docker image..."
docker build -t ${CONTAINER_REGISTRY}.azurecr.io/${APP_NAME}:${IMAGE_TAG} \
  -f api/OrderTracking.API/OrderTracking.API/Dockerfile \
  api/OrderTracking.API/

echo "📤 Pushing image to registry..."
az acr login --name $CONTAINER_REGISTRY
docker push ${CONTAINER_REGISTRY}.azurecr.io/${APP_NAME}:${IMAGE_TAG}

# Get infrastructure outputs
echo "🔍 Retrieving infrastructure information..."
COSMOS_ENDPOINT=$(az cosmosdb show --name "opt-${ENVIRONMENT}-cosmosdb-001" --resource-group $RESOURCE_GROUP --query "documentEndpoint" -o tsv)
COSMOS_KEY=$(az cosmosdb keys list --name "opt-${ENVIRONMENT}-cosmosdb-001" --resource-group $RESOURCE_GROUP --query "primaryMasterKey" -o tsv)
STORAGE_ACCOUNT=$(az storage account show --name "stakraken${ENVIRONMENT}001" --resource-group $RESOURCE_GROUP --query "name" -o tsv)
KEY_VAULT_NAME=$(az keyvault list --resource-group $RESOURCE_GROUP --query "[0].name" -o tsv)
APP_INSIGHTS_KEY=$(az monitor app-insights component show --app "ai-kraken-${ENVIRONMENT}" --resource-group $RESOURCE_GROUP --query "connectionString" -o tsv)

# Update Key Vault secrets
echo "🔐 Updating Key Vault secrets..."
az keyvault secret set --vault-name $KEY_VAULT_NAME --name "CosmosDbEndpoint" --value $COSMOS_ENDPOINT
az keyvault secret set --vault-name $KEY_VAULT_NAME --name "CosmosDbPrimaryKey" --value $COSMOS_KEY
az keyvault secret set --vault-name $KEY_VAULT_NAME --name "ApplicationInsightsConnectionString" --value "$APP_INSIGHTS_KEY"

# Deploy Container App
echo "🚢 Deploying Container App..."
az containerapp create \
  --name "ca-${APP_NAME}-${ENVIRONMENT}" \
  --resource-group $RESOURCE_GROUP \
  --environment "cae-kraken-${ENVIRONMENT}" \
  --image "${CONTAINER_REGISTRY}.azurecr.io/${APP_NAME}:${IMAGE_TAG}" \
  --target-port 80 \
  --ingress external \
  --min-replicas 1 \
  --max-replicas 10 \
  --cpu 1.0 \
  --memory 2.0Gi \
  --env-vars \
    "ASPNETCORE_ENVIRONMENT=${ENVIRONMENT}" \
    "Connections__Endpoint=${COSMOS_ENDPOINT}" \
    "Connections__DatabaseName=cosmos-clientportalapi-${ENVIRONMENT}-001" \
    "BlobStorage__AccountName=${STORAGE_ACCOUNT}" \
    "BlobStorage__KeyVaultName=${KEY_VAULT_NAME}" \
    "ApplicationInsights__ConnectionString=@Microsoft.KeyVault(SecretUri=https://${KEY_VAULT_NAME}.vault.azure.net/secrets/ApplicationInsightsConnectionString/)" \
  --registry-server "${CONTAINER_REGISTRY}.azurecr.io" \
  --system-assigned

echo "✅ Backend deployment completed successfully!"
echo "🌐 Application URL: https://ca-${APP_NAME}-${ENVIRONMENT}.${LOCATION}.azurecontainerapps.io"
