import { detailedDiff } from 'deep-object-diff';

export class CRDAuditEntry {
    private _jsonData: ChangeInfo;
    private _diff: object;

    eventId: number;
    insertedDate: string;
    jsonData: string;
    eventType: string;
    user: string;
    recordId: number;
    chemicalTitle?: string;

    get diff() {
        return this._diff;
    }

    get changeInfo() {
        return this._jsonData;
    }

    constructor(options?: Partial<CRDAuditEntry>) {
        if (options) {
            for (const [key, value] of Object.entries(options)) {
                this[key] = value;
            }
        }

        this._jsonData = JSON.parse(this.jsonData);
        this.chemicalTitle = this._jsonData.Target.New
            ? this._jsonData.Target.New.Title
            : this._jsonData.Target.Old.Title;
        if (this.eventType.toLowerCase().includes('update')) {
        }
        this._diff = detailedDiff(
            this._jsonData.Target.Old,
            this._jsonData.Target.New
        );
    }
}

export interface AuditJsonData {
    eventId: number;
    insertedDate: string;
    jsonData: string;
    eventType: string;
    user: string;
    recordId: number;
    _changeInfo: ChangeInfo;
}
export interface ChangeInfo {
    Environment: Environment;
    EventType: string;
    Target: Target;
    StartDate: string;
    EndDate: string;
    Duration: number;
    User: string;
    RecordId: number;
}
export interface Environment {
    UserName: string;
    MachineName: string;
    DomainName: string;
    CallingMethodName: string;
    AssemblyName: string;
    Culture: string;
}
export interface Target {
    Type: string;
    Old: OldOrNew;
    New: OldOrNew;
}
export interface OldOrNew {
    ID: number;
    Title: string;
    NewNo: string;
    CRDNo: string;
    CCNo: string;
    Formula: string;
    PhysicalProperties: string;
    LethalServiceReactive: string;
    TEAMCritical: string;
    Carcinogen: string;
    MedicalSurveillance: string;
    PSM: string;
    FlashPoint: string;
    AIT: string;
    FlamabilityNote: string;
    BP: string;
    VD: string;
    FrzTemp: string;
    EightHRTWA: string;
    STEL: string;
    IDLH: string;
    PurgeMedia: string;
    VapourIsHeavierThanAir: string;
    NonSparkToolingRequired: string;
    RiskOfExplosionThroughShockFriction: string;
    SCCCarbonSteelFasteners: string;
    SCCStainlessSteelFasteners: string;
    PWHTRecommended: string;
    GroundingCableRequired: string;
    Purge: string;
    OxygenChlorineProcedures: string;
    ReactsWithSteamWaterMoisture: string;
    SpecialProcedures: string;
    Toxicity: string;
    FirstAid: string;
    ButylAcidSuit: string;
    ARButylGloves: string;
    CRNitrileGloves: string;
    ThirdManRS: string;
    APRFilters: string;
    CASNo: string;
    MSDSNo: string;
    NoExposure: string;
    DripOrSeep: string;
    DripOrSeepNotes: string;
    MediumLeak: string;
    MediumLeakNotes: string;
    SevereLeak: string;
    SevereLeakNotes: string;
    OneB: string;
    OneHalfA: string;
    TwoA: string;
    TwoAS: string;
    TwoB: string;
    TwoD: string;
    TwoF: string;
    TwoG: string;
    ThreeA: string;
    ThreeC: string;
    FourA: string;
    FiveA: string;
    FiveB: string;
    FiveD: string;
    FiveE: string;
    FiveC: string;
    FiveO: string;
    FiveX: string;
    SixB: string;
    SixC: string;
    SevenC: string;
    NineA: string;
    NineC: string;
    ElevenA: string;
    ThirteenA: string;
    ThirteenB: string;
    FourteenA: string;
    FourteenB: string;
    FourteenC: string;
    FourteenD: string;
    FourteenE: string;
    TenB: string;
    TenC: string;
    TenD: string;
    TenE: string;
    TwelveF: string;
    TwelveS: string;
    TwelveA: string;
    TwelveL: string;
    TwelveW: string;
    FifteenF: string;
    FifteenS: string;
    FiftyNineD: string;
    OneX: string;
    TwoX: string;
    TwoXH: string;
    NumberFour: string;
    NumberSix: string;
    SixHT: string;
    NumberTen: string;
    NumberEleven: string;
    OneHundredFiftyFiveK: string;
    SixteenX: string;
    EighteenX: string;
    NineteenX: string;
    VPAX: string;
    VPB: string;
    VPER: string;
    VPEPX: string;
    VPF: string;
    VPGX: string;
    VPLX: string;
    VPOX: string;
    BulkX: string;
    FTen: string;
    FElevenF: string;
    FFourteen: string;
    GFiber: string;
    DBTwentyTwo: string;
    DBTwentyThree: string;
    ESOne: string;
    TCFour: string;
    XThirtySix: string;
    RFThreeHundred: string;
    BUNA: string;
    HNBR: string;
    EPDM: string;
    VITON: string;
    AFLAS: string;
    NEOPRENE: string;
    SILICONE: string;
    FCXSixHundredFour: string;
    ElevenS: string;
    CarbonSteel: string;
    StainlessSteel: string;
    ChromeMoly: string;
    PTFE: string;
    CarbonGraphite: string;
    ARAMID: string;
    IMPRES: string;
    BrassWire: string;
    CopperTubing: string;
    StainlessTubing: string;
}
