<div class="responsive-paddings">
    <div *ngIf="isLoading"
         style="width: 100%; height: 100%;">
        <dx-load-indicator class="centered"
                           id="large-indicator"
                           height="300"
                           width="300"></dx-load-indicator>
    </div>
    <div class="parent-container">
        <div class="dx-card content-block"
             *ngIf="!isLoading">

            <div class="dropdown-container"
                 style="
         display: flex;
         justify-content: flex-start;
         align-items: center;
         gap: 10px;
         padding: 10px;
         width: 100%;
         flex-wrap: wrap;">
                <!-- District Dropdown Template -->
                <ng-template #districtDropdown>
                    <label class="dx-field-item-label">District:</label>
                    <dx-tag-box id="districtSelectBox"
                                (onOpened)="onDistrictBoxOpened()"
                                [(value)]="selectedDistricts"
                                [items]="filteredDistrictOptions"
                                displayExpr="name"
                                valueExpr="id"
                                [showSelectionControls]="true"
                                placeholder="Select District"
                                [searchEnabled]="true"
                                [width]="300"
                                [multiline]="false"
                                [maxDisplayedTags]="100"
                                [showMultiTagOnly]="false"
                                (onValueChanged)="onDistrictChange($event.value)">
                        <dxo-drop-down-options
                                               [closeOnTargetScroll]="false"></dxo-drop-down-options>
                    </dx-tag-box>
                </ng-template>

                <!-- Customer Dropdown Template -->
                <ng-template #oisClientDropdown>
                    <label class="dx-field-item-label">Company:</label>
                    <dx-tag-box id="clientSelectBox"
                                [(value)]="selectedClient"
                                [items]="filteredClientValues"
                                displayExpr="name"
                                valueExpr="id"
                                [showSelectionControls]="true"
                                placeholder="Select Company"
                                [searchEnabled]="true"
                                [width]="300"
                                [multiline]="false"
                                [maxDisplayedTags]="100"
                                [showMultiTagOnly]="false"
                                (onValueChanged)="onClientChange($event.value)">
                        <dxo-drop-down-options
                                               [closeOnTargetScroll]="false"></dxo-drop-down-options>
                    </dx-tag-box>
                </ng-template>

                <!-- Render dropdowns based on role -->
                <ng-container
                              *ngIf="roles?.length === 1 && roles[0] === 'aimaas:client'; else districtFirstOrder">
                    <ng-container
                                  *ngTemplateOutlet="oisClientDropdown"></ng-container>
                    <ng-container
                                  *ngTemplateOutlet="districtDropdown"></ng-container>
                </ng-container>

                <ng-template #districtFirstOrder>
                    <ng-container
                                  *ngTemplateOutlet="districtDropdown"></ng-container>
                    <ng-container
                                  *ngTemplateOutlet="oisClientDropdown"></ng-container>
                </ng-template>

                <!-- Buttons -->
                <dx-button text="Display Records"
                           (onClick)="ondisplayResults()"
                           stylingMode="contained"
                           type="normal">
                </dx-button>
                <dx-button text="Clear dropdowns"
                           (onClick)="onclearDropdowns()"
                           stylingMode="contained"
                           type="normal">
                </dx-button>
            </div>

            <div class="table-container">
                <dx-data-grid [dataSource]="tableData"
                              (onOptionChanged)="onPageChange($event)"
                              [showBorders]="true"
                              [allowColumnResizing]="true"
                              [allowColumnReordering]="true"
                              [columnAutoWidth]="true"
                              [selectedRowKeys]="[]"
                              [columnResizingMode]="'widget'"
                              [filterRow]="{ visible: false }"
                              width="100%"
                              (onRowClick)="onRowClick($event)">

                    <dxo-export [enabled]="true"></dxo-export>
                    <dxo-toolbar>
                        <dxi-item name="groupPanel"></dxi-item>
                        <dxi-item widget="dxButton"
                                  location="after"
                                  [options]="{icon: 'fa fa-undo', hint: 'Restore Grid Defaults', onClick: restoreAssetsDefaultsClicked}">
                        </dxi-item>
                        <dxi-item name="columnChooserButton"></dxi-item>
                        <dxi-item name="exportButton"></dxi-item>
                    </dxo-toolbar>
                    <dxo-sorting mode="multiple"></dxo-sorting>
                    <!-- <dxo-filter-row [visible]="true"></dxo-filter-row> -->
                    <dxo-header-filter [visible]="false"></dxo-header-filter>
                    <dxo-selection mode="single"></dxo-selection>
                    <dxo-filter-panel [visible]="false"></dxo-filter-panel>
                    <dxo-scrolling mode="standard"></dxo-scrolling>
                    <dxo-load-panel [enabled]="true"></dxo-load-panel>

                    <!-- Pagination settings -->
                    <dxo-paging [enabled]="true"
                                [pageSize]="15"></dxo-paging>
                    <dxo-pager [visible]="true"
                               [allowedPageSizes]="[5, 10, 15]"
                               [showPageSizeSelector]="true"
                               [showNavigationButtons]="true"
                               [showInfo]="true"
                               [pageSizes]="[5, 10, 15]"
                               [pageIndex]="1"
                               [pageSize]="15"
                               [allowedPageSizes]="[15, 20, 25]"
                               [showPageSizeSelector]="true"
                               [showNavigationButtons]="true"
                               [showInfo]="true"
                               (onOptionChanged)="onRecordsPerPageChange($event)">
                    </dxo-pager>


                    <dxi-column caption="Location Details"
                                alignment="center">
                        <dxi-column dataField="location"
                                    caption="Location"
                                    sortOrder="asc"
                                    [sortIndex]="0"
                                    dataType="string"
                                    width="20%"></dxi-column>
                        <dxi-column dataField="totalAssets"
                                    caption="Total Assets"
                                    alignment="center"
                                    dataType="string"
                                    width="8%"></dxi-column>
                    </dxi-column>

                    <dxi-column caption="Equipment Counts (%)"
                                alignment="center">
                        <dxi-column dataField="currentInspections"
                                    caption="Current Inspections"
                                    alignment="center"
                                    dataType="string"
                                    [calculateCellValue]="formatCurrentInspections"
                                    width="8%">
                        </dxi-column>

                        <dxi-column dataField="overdueInspections"
                                    caption="Overdue Inspections"
                                    alignment="center"
                                    dataType="string"
                                    [calculateCellValue]="formatOverdueInspections"
                                    width="
                                8%"></dxi-column>
                        <dxi-column dataField="noInspections"
                                    caption="No Inspections"
                                    alignment="center"
                                    dataType="string"
                                    [calculateCellValue]="formatNoInspections"
                                    width="
                                8%"></dxi-column>
                    </dxi-column>

                    <dxi-column caption="Inspections"
                                alignment="center">
                        <dxi-column dataField="overdue"
                                    caption="Overdue"
                                    alignment="center"
                                    dataType="string"
                                    width="8%"></dxi-column>
                        <dxi-column dataField="dueThisYear"
                                    caption="Due This Year"
                                    alignment="center"
                                    dataType="string"
                                    width="8%"></dxi-column>
                        <dxi-column dataField="dueNextYear"
                                    caption="Due Next Year"
                                    alignment="center"
                                    dataType="string"
                                    width="8%"></dxi-column>
                    </dxi-column>

                    <dxi-column caption="Recommendations"
                                alignment="center">
                        <dxi-column dataField="openMajors"
                                    caption="Open Majors (P6)"
                                    alignment="center"
                                    dataType="string"
                                    width="8%"></dxi-column>
                        <dxi-column dataField="openMinors"
                                    caption="Open Minors (P5)"
                                    alignment="center"
                                    dataType="string"
                                    width="8%"></dxi-column>
                        <dxi-column dataField="openMonitors"
                                    caption="Open Monitors (P4)"
                                    alignment="center"
                                    dataType="string"
                                    width="8%"></dxi-column>
                    </dxi-column>

                    <!-- Total Row Configuration -->
                    <dxo-summary
                                 [calculateCustomSummary]="onPageSummaryCalculation">
                        <dxi-total-item column="location"
                                        summaryType="custom"
                                        displayFormat="Total"
                                        name="totalAssetsSummary">
                        </dxi-total-item>
                        <dxi-total-item column="totalAssets"
                                        summaryType="custom"
                                        displayFormat="{0}"
                                        name="totalAssetsSummary">
                        </dxi-total-item>

                        <dxi-total-item column="currentInspections"
                                        summaryType="custom"
                                        displayFormat="{0}"
                                        name="currentInspectionsSummary">
                        </dxi-total-item>

                        <dxi-total-item column="overdueInspections"
                                        summaryType="custom"
                                        displayFormat="{0}"
                                        name="overdueInspectionsSummary">
                        </dxi-total-item>

                        <dxi-total-item column="noInspections"
                                        summaryType="custom"
                                        displayFormat="{0}"
                                        name="noInspectionsSummary">
                        </dxi-total-item>

                        <dxi-total-item column="overdue"
                                        summaryType="custom"
                                        displayFormat="{0}"
                                        name="overdueSummary">
                        </dxi-total-item>

                        <dxi-total-item column="dueThisYear"
                                        summaryType="custom"
                                        displayFormat="{0}"
                                        name="dueThisYearSummary">
                        </dxi-total-item>

                        <dxi-total-item column="dueNextYear"
                                        summaryType="custom"
                                        displayFormat="{0}"
                                        name="dueNextYearSummary">
                        </dxi-total-item>

                        <dxi-total-item column="openMajors"
                                        summaryType="custom"
                                        displayFormat="{0}"
                                        name="openMajorsSummary">
                        </dxi-total-item>

                        <dxi-total-item column="openMinors"
                                        summaryType="custom"
                                        displayFormat="{0}"
                                        name="openMinorsSummary">
                        </dxi-total-item>

                        <dxi-total-item column="openMonitors"
                                        summaryType="custom"
                                        displayFormat="{0}"
                                        name="openMonitorsSummary">
                        </dxi-total-item>
                    </dxo-summary>

                </dx-data-grid>
            </div>

            <div *ngFor="let locationData of dynamicLocationData"
                 class="dx-card content-block full-width-card">
                <h3 *ngIf="locationData?.location"
                    class="location-title bold-text">
                    {{ locationData.location }}
                </h3>

                <div class="dashboard-container">
                    <!-- Compliance Score (Gauge) -->
                    <div class="gauge-container">
                        <dx-circular-gauge *ngIf="locationData.complianceScore !== undefined"
                                           class="custom-gauge"
                                           [value]="locationData.complianceScore"
                                           (click)="gaugeClicked(locationData)">
                            <dxo-title text="Asset Compliance Score"
                                       alignment="left"></dxo-title>
                            <dxo-geometry [startAngle]="180"
                                          [endAngle]="0"></dxo-geometry>
                            <dxo-tooltip [enabled]="true"
                                         [customizeTooltip]="customizeGaugeTooltip"></dxo-tooltip>
                            <dxo-scale [startValue]="0"
                                       [endValue]="100"
                                       [tickInterval]="10">
                                <dxo-tick [visible]="true"
                                          [length]="5"
                                          [width]="1"
                                          [shift]="35"
                                          color="#333"></dxo-tick>
                                <dxo-label [visible]="true"
                                           [font]="{ size: 13, color: '#333' }"
                                           [indentFromTick]="45"></dxo-label>
                            </dxo-scale>
                            <dxo-range-container [width]="30"
                                                 [backgroundColor]="'#d9d9d9'">
                                <dxi-range *ngFor="let range of gaugeRanges"
                                           [startValue]="range.startValue"
                                           [endValue]="range.endValue"
                                           [color]="range.color"></dxi-range>
                            </dxo-range-container>
                        </dx-circular-gauge>
                            <div class="gauge-value">
                                {{ locationData.complianceScore }}%
                            </div>
                    </div>

                    <!-- Inspections Chart -->
                    <div class="chart-container">
                        <dx-chart *ngIf="locationData.inspectionChartData && locationData.inspectionChartData.length>0"
                                  id="chart1"
                                  [dataSource]="locationData.inspectionChartData"
                                  [rotated]="false"
                                  (onPointClick)="navigateToDrilldown($event)">
                            <dxo-title text="Inspections"
                                       alignment="center"></dxo-title>
                            <dxi-series valueField="Vessels"
                                        name="Vessels"
                                        type="bar"
                                        color="#BBDEFB"></dxi-series>
                            <dxi-series valueField="Piping"
                                        name="Piping"
                                        type="bar"
                                        color="#005377"></dxi-series>
                            <dxi-series valueField="Tanks"
                                        name="Tanks"
                                        type="bar"
                                        color="#2196F3"></dxi-series>
                            <dxo-common-series-settings [barPadding]="0"
                                                        argumentField="type"
                                                        type="bar"></dxo-common-series-settings>
                            <dxo-legend verticalAlignment="bottom"
                                        horizontalAlignment="center"
                                        itemTextPosition="right"></dxo-legend>
                            <dxo-tooltip [enabled]="true"></dxo-tooltip>
                        </dx-chart>
                    </div>

                    <!-- Open Recommendations Chart -->
                    <div class="chart-container">
                        <dx-chart *ngIf="locationData.recommendationChartData && locationData.recommendationChartData.length"
                                  id="chart2"
                                  [dataSource]="locationData.recommendationChartData"
                                  [rotated]="false"
                                  (onPointClick)="navigateToAnomalyDrilldown($event)">
                            <dxo-title text="Open Recommendations"
                                       alignment="center"></dxo-title>
                            <dxi-series valueField="value"
                                        name="Open Recommendations"
                                        argumentField="category"
                                        type="bar"
                                        color="#4ca3dd"></dxi-series>
                            <!-- <dxo-label [visible]="true"></dxo-label> -->
                            <dxo-common-series-settings [barPadding]="0.4"
                                                        argumentField="type"
                                                        type="fullStackedBar"></dxo-common-series-settings>
                            <!-- <dxo-legend [visible]="false"></dxo-legend> -->\
                            <dxo-legend verticalAlignment="bottom"
                                        horizontalAlignment="center"
                                        itemTextPosition="right"></dxo-legend>

                            <dxo-tooltip [enabled]="true"></dxo-tooltip>
                        </dx-chart>
                    </div>
                </div>
            </div>


        </div>
    </div>
</div>