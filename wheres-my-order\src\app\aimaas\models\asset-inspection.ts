export class AssetInspection {
    clientid: string;
    clientname: string;
    locationname: string;
    locationid: string;
    assetid: string;
    assetdescription: string;
    assetidname: string;
    linenumber: string;
    operationorschedule: string;
    inspectionassetcategory: string;
    area: string;
    //law: string;
    assetstatus: string;
    riskclass: string;
    inspectiontype: string;
    completedinspectiondue: string;
    inspectiondate: string;
    result: string;
    inspectionstate: string;
    nextinspectiondue: string;
    scheduletype: string;
    frequency: string;
    schedulestatus: string;
    inspectionstatus: string;
    notes: string;
    performer: string;
    assetmanagementcategory: string;
    scheduleid: string;
    planoperationid: string;
    inspectionid: string;
    lastdate: string;
    nextdate: string;
    inspectiondue: string;
    date: string;
    operationtype: string;
    nextduedatenotes: string;
    costCentreId: any;
    priority: string;
    // operationid: string;
    // corrosionanalysis: string;
    // assetcategory: string;
    // alY_OM_ACC_TYPE: string;
    // alY_OM_APRX_VOL_FEET: number;
    // alY_OM_APSC_AREA_ID: string;
    // alY_OM_APSC_BOARD_NO: string;
    // alY_OM_APSC_JURIS_NO: string;
    // alY_OM_APSC_REGION: string;
    // alY_OM_BLR_1_CAP: string;
    // alY_OM_BLR_2_CAP: string;
    // alY_OM_BLR_3_CAP: string;
    // alY_OM_BUILDING: string;
    // alY_OM_CONF_ENT: string;
    // alY_OM_CRITICALITY: string;
    // alY_OM_EST_CONS: string;
    // alY_OM_EST_INTERR: string;
    // alY_OM_EXPOSURE: string;
    // alY_OM_EXT_ACC_NOTES: string;
    // alY_OM_EXT_INS_RMV: string;
    // alY_OM_EXT_SCAFF: string;
    // alY_OM_EXT_TIER: string;
    // alY_OM_INT_ACC_NOTES: string;
    // alY_OM_INT_INS_RMV: string;
    // alY_OM_INT_SCAFF: string;
    // alY_OM_INT_TIER: string;
    // alY_OM_LOC_DESC: string;
    // alY_OM_MANWAY_DET: string;
    // alY_OM_NDE_INSP_DUE: Date;
    // alY_OM_NDE_INSP_LAST: Date;
    // alY_OM_PV_CAP: string;
    // alY_OM_REDUNANCY: string;
    // alY_OM_RISK_NOTES: string;
    // alY_OM_SPEC_LOC: string;
    // alY_OM_STATE_CRT_EXP: Date;
    // alY_OM_STATE_CRT_INT: number;
    // alY_OM_STATE_LOCID: string;
    // alY_OM_VERT_HORIZ: string;
    // diM_DIAMOUTSIDE: number;
    // diM_DIAMUNITS: string;
    // diM_LENGTH: number;
    // diM_LENGTHUNIT: string;
    // eQ_IS_PWHT: number;
    // ehactiontakeN01: string;
    // ehactiontakeN02: string;
    // ehinstructioN1: string;
    // ehinstructioN2: string;
    // ehobservatioN1: string;
    // ehobservatioN2: string;
    // ehrecommendatioN1: string;
    // ehrecommendatioN2: string;
    // emdatecomplete: Date;
    // emdateplandue: Date;
    // emdateraised: Date;
    // emid: number;
    // emstatus: string;
    // emtypecode: string;
    // eqdesigncode: string;
    // eqinspcode: string;
    // eqliningext: string;
    // eqliningint: string;
    // eqmanufacturer: string;
    // eqmanufdate: Date;
    // eqserialnum: string;
    // eventcomment: string;
    // eventdescription: string;
    // eventname: string;
    // eventpriority: string;
    // objcomment: string;
    // objcommission: Date;
    // objcorrcirc: string;
    // objdesc: string;
    // objgroupid: string;
    // objid: number;
    // objname: string;
    // objriskcode: string;
    // objservice: string;
    // objstatus: string;
    // objsyS_SORT_1: string;
    // objsyS_SORT_2: string;
    // objtypecode: string;
    // objuniqueid: string;
    // pressdesmax: number;
    // pressopernorm: number;
    // pressunits: string;
    // rsitE_NAME: string;
    // rsitE_RID: number;
    // rstreaM_NAME: string;
    // tM_PV_CERT_HOLDER: string;
    // tM_PV_DATE_ALTERED: Date;
    // tM_PV_DATE_REPAIRED: Date;
    // tM_PV_ORIENTATION: string;
    // tM_PV_RLF_DEV_PRESS: number;
    // tM_PV_RMT_VIS_INSP_TYP: string;
    // tM_PV_ROPE_ACCESS: string;
    // tM_PV_R_CERT_NO: string;
    // tM_PV_SPEC_PPE_REQ: string;
    // tempdesmax: number;
    // tempopernorm: number;
    // tempunits: string;
    // xemplayer: string;

    constructor(options?: Partial<AssetInspection>) {
        if (options) {
            for (const [key, value] of Object.entries(options)) {
                this[key] = value;
            }
        }
    }

    isComplete(): boolean {
        if (
            this.completedinspectiondue === null ||
            this.completedinspectiondue === undefined ||
            (this.completedinspectiondue as unknown) === '' ||
            this.scheduletype === null
        )
            return false;
        const completeDate = new Date(this.completedinspectiondue);
        const isDate = completeDate instanceof Date;
        const isString = typeof this.completedinspectiondue === 'string';
        if (isDate) {
            return !isNaN(completeDate.getTime());
        } else if (isString) {
            return !isNaN(new Date(this.completedinspectiondue).getTime());
        }
        return false;
    }

    isDueNextYear(): boolean {
        if (
            this.nextinspectiondue === null ||
            this.nextinspectiondue === undefined ||
            (this.nextinspectiondue as unknown) === '' ||
            this.scheduletype === null
        )
            return false;
        const currentYear = new Date().getFullYear();
        const dueDate = new Date(this.nextinspectiondue);

        return (
            dueDate < new Date(currentYear + 2, 0, 1) &&
            dueDate >= new Date(currentYear + 1, 0, 1)
        );
    }

    isDueThisYear(): boolean {
        if (
            this.nextinspectiondue === null ||
            this.nextinspectiondue === undefined ||
            (this.nextinspectiondue as unknown) === '' ||
            this.scheduletype === null
        )
            return false;
        const currentYear = new Date().getFullYear();
        const currentMonth = new Date().getMonth();
        const currentDate = new Date().getDate();
        const dueDate = new Date(this.nextinspectiondue);
        const now = new Date();
        return (
            dueDate < new Date(currentYear + 1, 0, 1) &&
            dueDate >= new Date(currentYear, 0, 1) &&
            dueDate >= new Date(currentYear, currentMonth, currentDate)
        );
    }

    isOverdue(): boolean {
        if (
            this.nextinspectiondue === null ||
            this.nextinspectiondue === undefined ||
            (this.nextinspectiondue as unknown) === '' ||
            this.scheduletype === null
        )
            return false;
        const dueDate = new Date(this.nextinspectiondue);
        const now = new Date();
        dueDate.setHours(0, 0, 0, 0);
        now.setHours(0, 0, 0, 0);
        return dueDate < now;
    }
    isUpcoming(): boolean {
        if (
            this.nextinspectiondue === null ||
            this.nextinspectiondue === undefined ||
            (this.nextinspectiondue as unknown) === '' ||
            this.scheduletype === null
        )
            return false;
        const dueDate = new Date(this.nextinspectiondue);
        const now = new Date();
        dueDate.setHours(0, 0, 0, 0);
        now.setHours(0, 0, 0, 0);
        return dueDate > now;
    }
}
