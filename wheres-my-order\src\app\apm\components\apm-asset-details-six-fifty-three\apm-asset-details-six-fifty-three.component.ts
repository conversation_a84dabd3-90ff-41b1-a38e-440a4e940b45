import {
    ChangeDetectionStrategy,
    Component,
    EventEmitter,
    Input,
    Output,
    ViewChild
} from '@angular/core';
import cloneDeep from 'clone-deep';
import {
    DxFormComponent,
    DxGalleryComponent,
    DxPopupComponent,
    DxTextAreaComponent
} from 'devextreme-angular';
import { confirm } from 'devextreme/ui/dialog';
import { FieldDataChangedEvent } from 'devextreme/ui/form';
import { ValidationCallbackData } from 'devextreme/ui/validation_rules';
import { saveAs } from 'file-saver';
import { ToastrService } from 'ngx-toastr';
import { BehaviorSubject, firstValueFrom } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { get, isNullOrUndefined } from '../../../shared/helpers';
import {
    AssetDetailsPhoto,
    AssetDetailsPhotoTransport,
    AssetPath,
    SixFiftyThreeAssetDetails
} from '../../models';
import { ApmService } from '../../services';

@Component({
    selector: 'app-apm-asset-details-six-fifty-three',
    templateUrl: './apm-asset-details-six-fifty-three.component.html',
    styleUrls: ['./apm-asset-details-six-fifty-three.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class ApmAssetDetailsSixFiftyThreeComponent {
    private _changes: Partial<SixFiftyThreeAssetDetails> = {};
    private _original: SixFiftyThreeAssetDetails;
    private _assetDetails: SixFiftyThreeAssetDetails;
    private _originalPhotoDescription: string | undefined;
    private _isEditing = new BehaviorSubject<boolean>(false);
    private _isEditingPhotoDescription = new BehaviorSubject<boolean>(false);
    private _isSaving = new BehaviorSubject<boolean>(false);

    assetPathLoadingCompleted = false;
    assetPathsArray: AssetPath = {
        allPhotos: {},
        frontPhotos: {},
        backPhotos: {},
        leftPhotos: {},
        rightPhotos: {}
    };

    @Output() photoDelete = new EventEmitter<AssetDetailsPhotoTransport>();
    @Output() photoDescriptionUpdate =
        new EventEmitter<AssetDetailsPhotoTransport>();

    @Input()
    set assetDetails(value: SixFiftyThreeAssetDetails) {
        this._isEditing.next(false);
        this._assetDetails = value;
        this._original = cloneDeep(this._assetDetails);
        this.updateAssetPaths();
        this._changes = {};
        if (this._assetDetails) this.allPhotos = this._assetDetails.allPhotos;
    }
    get assetDetails() {
        return this._assetDetails;
    }

    @Input() allowEditing: boolean;
    showPhotoPopup = false;
    get geoOther() {
        return this.selectedGeometry.toLowerCase() === 'other';
    }
    selectedGeometry = '';
    multiDiameter = false;
    dataPlateAttached = false;
    repairPlateAttached = false;
    inspectionCodes = ['API 510', 'API 570', 'API 653'];
    pos = ['Vertical', 'Horizontal'];
    sideOptions = ['Shell Side', 'Tube Side'];
    geometryOptions = [
        { display: '2:1 Ellipsoidal', value: 'TwoToOneEllipsoidal' },
        { display: 'Conical', value: 'Conical' },
        { display: 'Cylindrical', value: 'Cylindrical' },
        { display: 'Ellipsoidal', value: 'Ellipsoidal' },
        { display: 'Flat Unstayed Circular', value: 'FaltUnstayedCircular' },
        { display: 'Spherical', value: 'Spherical' },
        { display: 'Torispherical', value: 'Torispherical' },
        {
            display: 'Torispherical (L/r=16 2/3)',
            value: 'Torispherical_16And2Thirds'
        },
        { display: 'Other', value: 'other' }
    ];
    rtOptions = ['Full', 'Spot', 'None'];
    diamterMeasurementOptions = ['ID', 'OD'];
    operationStatusOptions = ['In-Service', 'Out-Of-Service', 'Standby'];
    overallLocationOptions = ['On-Plot (Facility)', 'Off-Plot (Field)'];
    locationOptions = ['Top', 'Bottom', 'North', 'South', 'East', 'West'];
    lowestFlangeItems = ['150', '300', '400', '600', '900', '1500', '2500'];
    constructionTypeItems = [
        'Welded',
        'Pressure Welded',
        'Brazed',
        'Resistance Welded'
    ];
    constructionMethods = [
        'Welded',
        'Bolted',
        'Riveted',
        'Fiberglass',
        'Plastic',
        'Wood'
    ];
    pipeSizeItems = [
        '0.5',
        '0.75',
        '1',
        '1.25',
        '1.5',
        '2',
        '2.5',
        '3',
        '3.5',
        '4',
        '4.5',
        '5',
        '6',
        '8',
        '10',
        '12',
        '14',
        '16',
        '18',
        '20',
        '24',
        '30',
        '36',
        '42'
    ];
    flangeRatingItems = ['150', '300', '400', '600', '900', '1500', '2500'];
    pipeScheduleItems = [
        '5',
        '10',
        '20',
        '30',
        '40',
        '50',
        '60',
        '70',
        '80',
        '100',
        '120',
        '140',
        '160',
        'STD',
        'EH',
        'DBL.EH'
    ];
    operationStatus = ['In-Service', 'Out-Of-Service', 'Temp OOS for Insp'];
    tankTypes = ['Fixed', 'Internal Floating', 'External Floating'];
    yesNoUnknownItems = ['Yes', 'No', 'Unknown'];
    yesNoItems = ['Yes', 'No'];
    yesNoNaItems = ['Yes', 'No', 'N/A'];
    popupGallerySelectedIndex = 0;
    allPhotos: AssetDetailsPhoto[];

    readonly isEditing$ = this._isEditing.asObservable();
    readonly isEditingPhotoDescription$ =
        this._isEditingPhotoDescription.asObservable();
    readonly isSaving$ = this._isSaving.asObservable();

    @ViewChild(DxFormComponent) form: DxFormComponent;

    validateGIS = (e: ValidationCallbackData) => {
        const lat = this.form.instance.getEditor('latitude').option('value');
        const long = this.form.instance.getEditor('longitude').option('value');
        if (isNullOrUndefined(lat) && isNullOrUndefined(long)) return true;
        if (isNullOrUndefined(lat) && !isNullOrUndefined(long)) return false;
        if (!isNullOrUndefined(lat) && isNullOrUndefined(long)) return false;
        return true;
    };

    positiveLatitude = () => 90;

    negativeLatitude = () => -90;

    positiveLongitude = () => 180;

    negativeLongitude = () => -180;

    constructor(
        private readonly _apm: ApmService,
        private readonly _toasts: ToastrService
    ) {}

    onFieldDataChanged(e: FieldDataChangedEvent) {
        if (e.dataField === 'latitude' || e.dataField === 'longitude') {
            e.component.validate();
        }

        let value;
        if (e.value === true) value = 'Yes';
        else if (e.value === false) value = 'No';
        else value = e.value;

        if (
            [
                'inspectionOpenings',
                'repairs',
                'regulatoryRequirements',
                'shellCourses',
                'tankRoofs',
                'nozzles'
            ].includes(e.dataField)
        )
            return;

        if (e.dataField.includes(']') && e.dataField.includes('[')) {
            const split = e.dataField.split('[');
            const elementAttributeName = e.dataField.split('.').reverse()[0];
            const arrayAttributeName = split[0];
            const arrayIndex = split[1][0];
            if (!this._changes[arrayAttributeName]) {
                this._changes[arrayAttributeName] = new Array(
                    this.assetDetails[arrayAttributeName].length
                );
            }
            this._changes[arrayAttributeName][arrayIndex] = {
                ...this._changes[arrayAttributeName][arrayIndex],
                [elementAttributeName]: { value }
            };
            const component = get(this.assetDetails, e.dataField.split('.')[0]);
            this._changes[arrayAttributeName][arrayIndex].databaseId =
                component.databaseId;
        } else {
            this._changes[e.dataField] = { value };
        }
    }

    onSaveClicked(e) {
        const result = this.form.instance.validate();
        if (!result.isValid) return;
        this._isSaving.next(true);
        this._apm
            .updateSixFiftyThreeAssetDetails({
                ...this._changes,
                workOrderId: this.assetDetails.workOrderId,
                projectId: this.assetDetails.projectId
            })
            .pipe(finalize(() => this._isSaving.next(false)))
            .subscribe(() => {
                this._isEditing.next(false);
                this._toasts.success(
                    'Asset task data updated successfully',
                    'Update successful'
                );
                this._changes = {};
                this._original = cloneDeep(this.assetDetails);
            });
    }

    onEditClicked(e) {
        this._isEditing.next(true);
    }

    onCancelClicked(e, form: DxFormComponent) {
        this._isEditing.next(false);
        form.instance.option('formData', cloneDeep(this._original));
        this._changes = {};
    }

    onPopupGalleryContentReady(
        e,
        popup: DxPopupComponent,
        gallery: DxGalleryComponent
    ) {
        const title = gallery.selectedItem?.section;
        if (title && popup.title !== title) {
            popup.instance.option('title', title);
        }
    }

    onPopupGallerySelectionChanged(
        e,
        popup: DxPopupComponent,
        gallery: DxGalleryComponent
    ) {
        const title = gallery.selectedItem?.section;
        if (title && popup.title !== title) {
            popup.instance.option('title', title);
        }
    }

    onItemClick(e) {
        this.showPhotoPopup = true;
        this.popupGallerySelectedIndex = this.allPhotos.findIndex(
            (somePhoto) => somePhoto.photo.databaseId === e.itemData.databaseId
        );
    }

    onEditDescriptionClicked(e, currentValue: string) {
        this._isEditingPhotoDescription.next(true);
        this._originalPhotoDescription = currentValue;
    }

    onDescriptionSave(e, photoInfo: AssetDetailsPhoto, description: string) {
        const update: AssetDetailsPhotoTransport = {
            assetType: '653',
            projectId: this._assetDetails.projectId,
            workOrderId: this._assetDetails.workOrderId,
            photoDatabaseId: photoInfo.photo.databaseId,
            taskId: photoInfo.taskId,
            section: photoInfo.section,
            description
        };
        this.photoDescriptionUpdate.next(update);
        this._isEditingPhotoDescription.next(false);
    }

    onDescriptionCancel(e, editor: DxTextAreaComponent) {
        this._isEditingPhotoDescription.next(false);
        editor.instance.option('value', this._originalPhotoDescription);
    }

    async onDeletePhotoClicked(e, photoInfo: AssetDetailsPhoto) {
        const photoTransport: AssetDetailsPhotoTransport = {
            projectId: this._assetDetails.projectId,
            workOrderId: this._assetDetails.workOrderId,
            photoDatabaseId: photoInfo.photo.databaseId,
            taskId: photoInfo.taskId,
            section: photoInfo.section,
            assetType: '653'
        };
        const result = await confirm(
            'Are you sure you want to remove this photo?',
            'Are you sure?'
        );
        if (result) this.photoDelete.next(photoTransport);
    }

    async onDownloadClicked(fileName: string) {
        const url = await firstValueFrom(this._apm.getSignedUrl(fileName));
        const blob = await this._apm.downloadFileFromUrl(url).toPromise();
        saveAs(blob, fileName);
    }

    getAssetImage(type, blobPath) {
        return this.assetPathsArray[type][blobPath]
            ? this.assetPathsArray[type][blobPath]
            : '';
    }
    async updateAssetPaths() {
        const _assets = this._assetDetails;
        if (_assets?.frontPhotos) {
            _assets.frontPhotos.forEach(async (photo) => {
                let assetPath;
                try {
                    assetPath = await firstValueFrom(
                        this._apm.getSignedUrl(photo.blobName)
                    );
                } catch (error) {
                    assetPath = '';
                }
                this.assetPathsArray.frontPhotos[photo.blobName] = assetPath;
                this.assetPathsArray.allPhotos[photo.blobName] = assetPath;
            });
        }
        if (_assets?.backPhotos) {
            _assets.backPhotos.forEach(async (photo) => {
                let assetPath;
                try {
                    assetPath = await firstValueFrom(
                        this._apm.getSignedUrl(photo.blobName)
                    );
                } catch (error) {
                    assetPath = '';
                }
                this.assetPathsArray.backPhotos[photo.blobName] = assetPath;
                this.assetPathsArray.allPhotos[photo.blobName] = assetPath;
            });
        }

        if (_assets?.leftPhotos) {
            _assets.leftPhotos.forEach(async (photo) => {
                let assetPath;
                try {
                    assetPath = await firstValueFrom(
                        this._apm.getSignedUrl(photo.blobName)
                    );
                } catch (error) {
                    assetPath = '';
                }
                this.assetPathsArray.leftPhotos[photo.blobName] = assetPath;
                this.assetPathsArray.allPhotos[photo.blobName] = assetPath;
            });
        }

        if (_assets?.rightPhotos) {
            _assets.rightPhotos.forEach(async (photo) => {
                let assetPath;
                try {
                    assetPath = await firstValueFrom(
                        this._apm.getSignedUrl(photo.blobName)
                    );
                } catch (error) {
                    assetPath = '';
                }
                this.assetPathsArray.rightPhotos[photo.blobName] = assetPath;
                this.assetPathsArray.allPhotos[photo.blobName] = assetPath;
            });
        }
        this.assetPathLoadingCompleted = true;
    }
}
