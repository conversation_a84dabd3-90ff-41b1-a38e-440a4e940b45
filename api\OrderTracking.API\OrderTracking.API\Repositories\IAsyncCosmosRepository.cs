using System.Threading.Tasks;
using ClientPortal.Shared.Models;

namespace OrderTracking.API.Repositories
{
    /// <summary>
    /// Standard CRUD methods for Azure Cosmos DB Entities where a Partition Key is needed.
    /// This interface is for methods that require PartitionKey and the implementor
    /// knows how to assign this appropriately.
    /// </summary>
    public interface IAsyncCosmosRepository<TEntity, in TKey>
        : IAsyncRepository<TEntity, TKey> where TEntity : ICosmosEntity<TKey>
    {
        #region Public Methods

        /// <summary>
        /// Get document with id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        new Task<TEntity> GetAsync(TKey id);

        /// <summary>
        /// Get document with id and partition key
        /// </summary>
        /// <param name="id"></param>
        /// <param name="partitionKey"></param>
        /// <returns></returns>
        Task<TEntity> GetAsync(TKey id, string partitionKey);

        #endregion
    }
}
