# Azure DevOps Pipeline for Frontend (Where's My Order)
# Replaces Google Cloud Build for frontend deployment
 
trigger:
  branches:
    include:
      - main
      - develop
      - migration/master
  paths:
    include:
      - wheres-my-order/*
 
pool:
  vmImage: 'ubuntu-latest'
 
variables:
  buildConfiguration: 'Release'
  imageRepository: 'cpa-frontend'
  #containerRegistry: 'krakenacr.azurecr.io'
  dockerfilePath: 'wheres-my-order/Dockerfile'
  tag: '$(Build.BuildId)'
  #azureSubscription: 'Kraken-Azure-Connection'
  # resourceGroupName: 'rg-kraken-$(Environment.Name)'
  # containerAppName: 'ca-cpa-frontend-$(Environment.Name)'
 
stages:
- stage: Build
  displayName: 'Build and Push Docker Image'
  jobs:
  - job: Build
    displayName: 'Build Angular Application'
    steps:
    - task: NodeTool@0
      displayName: 'Install Node.js'
      inputs:
        versionSpec: '18.x'
 
    - script: |
        cd wheres-my-order
        npm ci
        npm run build-prod
      displayName: 'Install dependencies and build'
 
    - task: Docker@2
      displayName: 'Build and push Docker image'
      inputs:
        command: 'buildAndPush'
        repository: '$(imageRepository)'
        dockerfile: '$(Build.SourcesDirectory)/wheres-my-order/Dockerfile'
        buildContext: '$(Build.SourcesDirectory)'
        containerRegistry: 'test_image'
        tags: |
          $(tag)
          latest
      
- stage: Deploy
  displayName: 'Deploy to Container Apps'
  jobs:
  - job: Deploy

    steps:
      - task: Bash@3
        inputs:
          targetType: 'inline'
          script: |
            # Write your commands here
            
            curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash
            az --version
      - task: AzureCLI@2
        inputs:
          azureSubscription: 'TEAM-CLAND-01 (8de261da-54e3-4334-9076-284334fc2305)'
          scriptType: 'bash'
          scriptLocation: 'inlineScript'
          inlineScript: 'az containerapp update --name testfrontend001 --resource-group rg-digital-poc  --image containertestapp001.azurecr.io/cpa-frontend:latest'
        displayName: 'Build Angular Application'
              
# - stage: Deploy_Dev
#   displayName: 'Deploy to Dev'
#   dependsOn: Build
#   jobs:
#   - job: DeployDev
#     displayName: 'Deploy to Dev Container App'
#     steps:
#     - task: AzureCLI@2
#       inputs:
#           azureSubscription: 'TEAM-CLAND-01 (8de261da-54e3-4334-9076-284334fc2305)'
#           scriptType: 'bash'
#           scriptLocation: 'inlineScript'
#           inlineScript: 'az containerapp update --name testfrontend001 --resource-group rg-digital-poc  --image containertestapp001.azurecr.io/cpa-frontend:latest'
#       displayName: 'Build Angular Application'

# # ----------------- DEPLOY TO UAT STAGE -----------------
# - stage: Deploy_UAT
#   displayName: 'Deploy to UAT'
#   dependsOn: Deploy_Dev
#   condition: succeeded()
#   jobs:
#   - job: DeployUAT
#     displayName: 'Deploy to UAT Container App'
#     steps:
#     - task: AzureCLI@2
#       inputs:
#           azureSubscription: 'TEAM-CLAND-01 (8de261da-54e3-4334-9076-284334fc2305)'
#           scriptType: 'bash'
#           scriptLocation: 'inlineScript'
#           inlineScript: 'az containerapp update --name testfrontend001 --resource-group rg-digital-poc  --image containertestapp001.azurecr.io/cpa-frontend:latest'
#       displayName: 'Build Angular Application'

 
# # ----------------- DEPLOY TO PROD STAGE -----------------
# - stage: Deploy_Prod
#   displayName: 'Deploy to Prod'
#   dependsOn: Deploy_UAT
#   condition: succeeded()
#   jobs:
#   - job: DeployProd
#     displayName: 'Deploy to Prod Container App'
#     steps:
#     - task: AzureCLI@2
#       inputs:
#           azureSubscription: 'TEAM-CLAND-01 (8de261da-54e3-4334-9076-284334fc2305)'
#           scriptType: 'bash'
#           scriptLocation: 'inlineScript'
#           inlineScript: 'az containerapp update --name testfrontend001 --resource-group rg-digital-poc  --image containertestapp001.azurecr.io/cpa-frontend:latest'
#       displayName: 'Build Angular Application'
