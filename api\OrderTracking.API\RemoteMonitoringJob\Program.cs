using System;
using System.Threading.Tasks;
using ClientPortal.Shared.Models.MOS;
using ClientPortal.Shared.Services;
using FluentMigrator.Runner;
// Migrated from Google Cloud Diagnostics to Azure Application Insights
// using Google.Cloud.Diagnostics.AspNetCore3;
using Microsoft.ApplicationInsights.WorkerService;
// Removed Microsoft.AspNetCore.Builder - not needed for Worker Service
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using RemoteMonitoringJob.Migrations;
using RemoteMonitoringJob.Services;

namespace RemoteMonitoringJob
{
    public class Program
    {
        public static async Task Main(string[] args)
        {
            // Use Host.CreateDefaultBuilder for Worker Service instead of WebApplication
            var hostBuilder = Host.CreateDefaultBuilder(args);
            var host = hostBuilder.Build();

            var serviceCollection = new ServiceCollection()
                .AddLogging(b => b.AddConsole())
                .AddSingleton<IConfiguration>(host.Services.GetRequiredService<IConfiguration>())
                .AddSingleton<IWorkerService, WorkerService>()
                .AddLogging(lb => lb.AddFluentMigratorConsole())
                // Migrated from Google Cloud Diagnostics to Azure Application Insights
                .AddApplicationInsightsTelemetryWorkerService(host.Services.GetRequiredService<IConfiguration>())
                .AddSingleton<ISensorReadingsSQLService, SensorReadingsSQLService>()
                .AddSingleton<ISmartpimsScraperService, SmartpimsScraperService>()
                .AddFluentMigratorCore()
                        .ConfigureRunner(rb => rb
                            // Add Sql Server support to FluentMigrator
                            .AddSqlServer()
                            // Set the connection string
                            .WithGlobalConnectionString(
                                host.Services.GetRequiredService<IConfiguration>().GetConnectionString("RemoteMonitoring"))
                            // Define the assembly containing the migrations
                            .ScanIn(typeof(AddSensorReadingsTable).Assembly).For.Migrations())
                .BuildServiceProvider();

            var logger = serviceCollection.GetService<ILoggerFactory>().CreateLogger<Program>();
            logger.LogDebug("Starting WorkerService Execution..");
            var workerService = serviceCollection.GetService<IWorkerService>();
            await workerService.DoAsync();
        }
    }
}