<app-header class="layout-header"
            [menuToggleEnabled]="true"
            (menuToggle)="menuOpened = !menuOpened"
            [title]="title">
</app-header>


<dx-drawer class="layout-body"
           position="before"
           [closeOnOutsideClick]="closeOnOutsideClick"
           [openedStateMode]="menuMode"
           [revealMode]="menuRevealMode"
           [minSize]="minMenuSize"
           [shading]="shaderEnabled"
           [(opened)]="menuOpened">
    <app-side-navigation-menu #sideNavMenu
                              [items]="menuItems"
                              [compactMode]="!menuOpened"
                              [selectedItem]="selectedRoute$ | async"
                              class="dx-swatch-additional"
                              *dxTemplate="let dataMenu of 'panel'"
                              (selectedItemChanged)="navigationChanged($event)"
                              (menuItemClicked)="navigationClick()">

    </app-side-navigation-menu>

    <div *ngIf="(onlineOfflineStatus$ | async) === false"
         class="offline-banner">
        <span>No Internet. Reconnect to access OneInsight.</span>
    </div>

    <dx-scroll-view class="full-height-scrollable"
                    #scrollview>
        <div class="content"
             style="display: flex;">
            <ng-content></ng-content>
        </div>

        <div class="content-block">
            <ng-content select="app-footer"></ng-content>
        </div>
    </dx-scroll-view>
</dx-drawer>