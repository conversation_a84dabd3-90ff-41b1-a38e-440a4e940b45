export class Asset {
    id:number;
    assetid:string;
    riskclass:string;
    description:string;
    service:string;
    equipcategory:string;
    assettype:string;
    orientation:string;
    constructioncode:string;
    inspectioncode: string;
    manufacturer: string;
    image: string;
    link: string;
    constructionyear: number;
    dimensions: string;
    serialnumber: number;
    nationalboard:string;
    localjuridictional:string;
    pid:string;
    clientname:string;
    locationname:string;
    locationid: string;
    chambers:string;
    cmms:string;
    areaid:string;
    areaname:string;
    assetcategory:string;
    sclass : string;
    // alY_OM_ACC_TYPE: string;
  //  alY_OM_APRX_VOL_FEET: number;
    /** Used for Area ID */
 //   objsyS_SORT_1: string;
    /** Used for Maintenance Region */
  /*  objsyS_SORT_2: string;
    alY_OM_APSC_BOARD_NO: string;
    alY_OM_APSC_JURIS_NO: string;
    alY_OM_BLR_1_CAP: string;
    alY_OM_BLR_2_CAP: string;
    alY_OM_BLR_3_CAP: string;
    alY_OM_BUILDING: string;
    alY_OM_CONF_ENT: string;
    alY_OM_CRITICALITY: string;
    alY_OM_EST_CONS: string;
    alY_OM_EST_INTERR: string;
    alY_OM_EXPOSURE: string;
    alY_OM_EXT_ACC_NOTES: string;
    alY_OM_EXT_INS_RMV: string;
    alY_OM_EXT_SCAFF: string;
    alY_OM_EXT_TIER: string;
    alY_OM_INT_ACC_NOTES: string;
    alY_OM_INT_INS_RMV: string;
    alY_OM_INT_SCAFF: string;
    alY_OM_INT_TIER: string;
    alY_OM_LOC_DESC: string;
    alY_OM_MANWAY_DET: string;
    alY_OM_NDE_INSP_DUE: Date;
    alY_OM_NDE_INSP_LAST: Date;
    alY_OM_PV_CAP: string;
    alY_OM_REDUNANCY: string;
    alY_OM_RISK_NOTES: string;
    alY_OM_SPEC_LOC: string;
    alY_OM_STATE_CRT_EXP: Date;
    alY_OM_STATE_CRT_INT: number;
    alY_OM_STATE_LOCID: string;
    alY_OM_VERT_HORIZ: string;
    diM_DIAMOUTSIDE: number;
    diM_DIAMUNITS: string;
    diM_LENGTH: number;
    diM_LENGTHUNIT: string;
    eQ_IS_PWHT: number;
    eqdesigncode: string;
    eqinspcodE_CUSTOM: string;
    eqinspcode: string;
    eqliningext: string;
    eqliningint: string;
    eqmanufacturer: string;
    eqmanufdate: Date;
    eqserialnum: string;
    objcomment: string;
    objcommission: Date;
    objcorrcirc: string;
    objdesc: string;
    objgroupid: string;
    objid: number;
    objname: string;
    objriskcode: string;
    objservice: string;
    objstatus: string;
    objtypecode: string;
    objuniqueid: string;
    pressdesmax: number;
    pressopernorm: number;
    pressunits: string;
    rsitE_NAME: string;
    rsitE_RID: number;
    objcat: string;
    rstreaM_NAME: string;
    tM_PV_CERT_HOLDER: string;
    tM_PV_DATE_ALTERED: Date;
    tM_PV_DATE_REPAIRED: Date;
    tM_PV_NAT_BOARD_NO: string;
    tM_PV_ORIENTATION: string;
    tM_PV_RLF_DEV_PRESS: number;
    tM_PV_RMT_VIS_INSP_TYP: string;
    tM_PV_ROPE_ACCESS: string;
    tM_PV_R_CERT_NO: string;
    tM_PV_SPEC_GRAVITY: string;
    tM_PV_SPEC_PPE_REQ: string;
    tempdesmax: number;
    tempopernorm: number;
    tempunits: string;*/

    constructor(options?: Partial<Asset>) {
        if (options) {
            for (const [key, value] of Object.entries(options)) {
                this[key] = value;
            }
        }
    }
}
