import { Component } from '@angular/core';
import CustomStore from 'devextreme/data/custom_store';
import dxDataGrid, { InitNewRowEvent } from 'devextreme/ui/data_grid';
import { ToastrService } from 'ngx-toastr';
import { v4 as uuidv4 } from 'uuid';
import { UsersService } from '../../../shared/services';
import { Client } from '../../models';
import { PipelineInspectionService } from '../../services';

@Component({
    selector: 'app-clients',
    templateUrl: './clients.component.html',
    styleUrls: ['./clients.component.scss']
})
export class ClientsComponent {
    dataSource: CustomStore;
    currentUser$ = this._users.currentProfile$;

    private _oldClient: Client;
    private _newClient: Client;
    private _clientsCache: Client[];

    constructor(
        private readonly _pia: PipelineInspectionService,
        private readonly _toasts: ToastrService,
        private readonly _users: UsersService
    ) {
        this.dataSource = new CustomStore({
            key: 'id',
            byKey: (key) => {
                return new Promise((resolve) =>
                    resolve(
                        this._clientsCache
                            ? this._clientsCache.find((c) => c.id === key)
                            : null
                    )
                );
            },
            load: async () => {
                const clients = await this._pia.getClients().toPromise();
                this._clientsCache = clients;
                return clients;
            },
            insert: async (values) => {
                values.id = uuidv4();
                const response = await this._pia
                    .createClient(values)
                    .toPromise();

                return response;
            },
            update: async (key, values) => {
                const response = await this._pia
                    .updateClient(this._oldClient, this._newClient)
                    .toPromise();

                if (response?.error) {
                    throw new Error(response.error);
                }

                return response;
            },
            remove: async (key) => {
                const dataRow = await this.dataSource.byKey(key);
                const response = await this._pia
                    .updateClient(dataRow, { ...dataRow, isActive: false })
                    .toPromise();

                if (response?.error) {
                    throw new Error(response.error);
                }
            }
        });
    }

    onInitNewRow(e: InitNewRowEvent) {
        // Set this to true by default
        e.data.isActive = true;
    }

    customizeIsActiveText = (arg) => {
        return arg.value ? 'Active' : 'Inactive';
    };

    mustBeTrueOrFalse = (options) => {
        if (options.value === undefined || options.value === null) {
            return false;
        } else {
            return true;
        }
    };

    onToolbarPreparing(e) {
        const addBtn = e.toolbarOptions.items.find(
            (i) => i.name === 'addRowButton'
        );

        if (!addBtn) return;

        addBtn.showText = 'always';
        addBtn.options = {
            ...addBtn.options,
            icon: null,
            text: 'Create',
            type: 'success',
            stylingMode: 'contained'
        };
    }

    onRowUpdating(event: {
        cancel: boolean;
        component: dxDataGrid;
        element: HTMLElement;
        key: string;
        newData: Partial<Client>;
        oldData: Client;
    }) {
        this._oldClient = event.oldData;
        this._newClient = { ...this._oldClient, ...event.newData };
    }

    allowDeleting(event: {
        component: dxDataGrid;
        row: { data: { isActive: boolean } };
    }) {
        return event.row.data.isActive;
    }

    onCellPrepared(event: {
        cellElement: HTMLElement;
        column: {};
        columnIndex: number;
        component: dxDataGrid;
        data: Client;
        displayValue: any;
        element: HTMLElement;
        isExpanded: boolean;
        isNewRow: boolean;
        isSelected: boolean;
        key: any;
        model: object;
        oldValue: any;
        row: {};
        rowIndex: number;
        rowType: string;
        text: string;
        value: any;
        watch: (getter, handler) => {};
    }) {
        if (event.rowType === 'data' && !event.data.isActive) {
            event.cellElement.style.color = '#6c757d';
            event.cellElement.style.textDecoration = 'line-through';
        }
    }

    logEvent(event: any, eventName: string) {
        // TODO: Review how to handle the editing events in the grid.  RowInserting vs. RowInserted is so that we
        // handle the actual saving to the server part in the RowInserting, and prevent RowInserted from firing
        // if something doesn't go right.  Please review here: https://js.devexpress.com/Documentation/ApiReference/UI_Components/dxDataGrid/Configuration/#onRowInserting
        if (eventName === 'RowInserted') {
            this._toasts.success('Client added successfully');
        } else if (eventName === 'RowUpdated') {
            this._toasts.success('Client updated successfully');
        }
        console.log(eventName, event);
    }
}
