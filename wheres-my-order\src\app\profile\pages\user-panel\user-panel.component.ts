import { Component, Input, OnInit } from '@angular/core';

import { AuthService } from '../../../shared/services';

@Component({
    selector: 'app-user-panel',
    templateUrl: 'user-panel.component.html',
    styleUrls: ['./user-panel.component.scss'],
})
export class UserPanelComponent implements OnInit {
    @Input()
    menuItems: any;

    @Input()
    menuMode: string;

    displayName: string;

    constructor(private readonly _auth: AuthService) {}

    ngOnInit() {
        const claims = this._auth.user.idTokenClaims as {
            family_name: string;
            emails: string[];
            given_name: string;
        };
        this.displayName =
            claims.family_name === ''
                ? claims.emails[0]
                : `${claims.family_name}, ${claims.given_name}`;
    }
}
