import { Component, Input, OnInit } from '@angular/core';

import { AuthService, ToastNotificationService, ToastType } from '../../../shared/services';

@Component({
    selector: 'app-user-panel',
    templateUrl: 'user-panel.component.html',
    styleUrls: ['./user-panel.component.scss'],
})
export class UserPanelComponent implements OnInit {
    @Input()
    menuItems: any;

    @Input()
    menuMode: string;

    displayName: string;

    isFromOverview: boolean = false;

    constructor(private readonly _auth: AuthService, private readonly _toasts: ToastNotificationService) { }

    ngOnInit() {
        const claims = this._auth.user.idTokenClaims as {
            family_name: string;
            emails: string[];
            given_name: string;
        };
        this.displayName =
            claims.family_name === ''
                ? claims.emails[0]
                : `${claims.family_name}, ${claims.given_name}`;
    }

    onUserPanelClick(event: MouseEvent): void {
        if (window.location.hash.includes('fromOverview')) {
            this.isFromOverview = true;
            this._toasts.show(
                'Warning',
                'Please navigate to the Parent tab to access the Left Menu.',
                ToastType.warning,
                3000
            );
            event.stopPropagation();
            event.preventDefault();
        }
    }
}
