export class ClientCostCenter {
    CLIENTID: string;
    CLIENTNAME: string;
    LOCATIONNAME: string;
    locationid: number;
    assetid: string;
    COSTCENTERID: string;
    COSTCENTERNAME: string;

    constructor(options?: Partial<ClientCostCenter>) {
        if (options) {
            for (const [key, value] of Object.entries(options)) {
                this[key] = value;
            }
        }
    }
}
