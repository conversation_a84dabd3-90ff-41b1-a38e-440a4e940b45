import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { combineLatest, Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { UserProfile } from '../../profile/models';
import { UsersService } from './users.service';

export type District = {
    name: string;
    number: string;
};

export namespace District {
    export function displayExpr(district: District): string {
        return district ? `${district.number} - ${district.name}` : '';
    }

    export function valueExpr(district: District): string | null {
        return district ? district.number : null;
    }
}

@Injectable({
    providedIn: 'root'
})
export class DistrictService {
    constructor(
        private readonly _http: HttpClient,
        private readonly _users: UsersService
    ) {}

    getDistrictNumbers(filterByPermissions = true): Observable<string[]> {
        return this.getDistricts(filterByPermissions).pipe(
            map((districts) =>
                districts.map(
                    (district) => `${district.number} - ${district.name}`
                )
            )
        );
    }

    getDistricts(filterByPermissions = true): Observable<District[]> {
        return combineLatest([
            // Go get our districts from the assets folder
            this._http.get<District[]>('assets/data/districts.json'),
            // Grab the current user profile that is signed in
            this._users.currentProfile$
        ]).pipe(
            // Map districts and current user to the districts that the current user has access to.
            map(([districts, currentUser]) =>
                filterByPermissions
                    ? this.filterDistrictsForUser(districts, currentUser)
                    : districts
            )
        );
    }

    private filterDistrictsForUser(
        districts: District[],
        currentUser: UserProfile
    ): { name: string; number: string }[] {
        return districts.filter(
            (district) =>
                currentUser.roles
                    .map((r) => r.toLowerCase().trim())
                    .some((r) => r === 'app:admin' || r === 'wmo:assigner' || r === 'aimaas:admin') ||
                currentUser.districtIds.includes(district.number)
        );
    }
}
