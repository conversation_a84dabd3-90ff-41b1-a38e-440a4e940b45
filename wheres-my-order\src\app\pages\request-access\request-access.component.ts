import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { DxButtonComponent } from 'devextreme-angular/ui/button';
import { alert } from 'devextreme/ui/dialog';
import { ToastrService } from 'ngx-toastr';
import { Observable } from 'rxjs';
import { finalize, map } from 'rxjs/operators';
import {
    RequestAccessModule,
    RequestAccessService,
    WMOSpecialAccess
} from '../../core/services';
import { AccessRequest } from '../../models';
import { UserProfile } from '../../profile/models';
import { DistrictService, UsersService } from '../../shared/services';
import { ClientService } from '../../shared/services/client.service';

@Component({
    selector: 'app-request-access',
    templateUrl: './request-access.component.html',
    styleUrls: ['./request-access.component.scss']
})
export class RequestAccessComponent implements OnInit {
    modules: RequestAccessModule[] = [
        // "Where's My Order",
        // 'Equipment Demand Request',
        // 'Asset Integrity Management as a Service (AIMaaS)',
        // 'Work Management - Pipeline',
        // 'Bolting Calculator',
        'Remote Asset Monitoring',
        'Asset Performance Management (APM)',
        'Field Service Management (FSM)',
        'Asset Integrity Hub'
    ];

    wmoSpecialAccessItems: WMOSpecialAccess[] = [
        'None',
        'Manufacturing User',
        'Engineering User',
        'District Manager'
    ];

    districts$: Observable<{ value: string; label: string }[]>;
    clients$: Observable<string[]>;
    currentUser$: Observable<UserProfile>;
    companyNameAndSite: string;
    teamContact: string;
    selectedModules: RequestAccessModule[];
    selectedWMOSpecialAccess: WMOSpecialAccess;
    selectedDistricts: string[];
    selectedCompanies: string[];
    managementSites: string;
    sendingRequest: boolean;

    constructor(
        private readonly _districts: DistrictService,
        private readonly _customerAccounts: ClientService,
        private readonly _users: UsersService,
        private readonly _requestAccess: RequestAccessService,
        private readonly _router: Router,
        private readonly _toasts: ToastrService
    ) { }

    ngOnInit(): void {
        this.currentUser$ = this._users.currentProfile$;
        this.clients$ = this._customerAccounts.getClientNames();
        this.districts$ = this._districts.getDistrictNumbers().pipe(
            map((districts: string[]) =>
                districts.map(district => {
                    const districtWithCode_Name = district.split(' - ');
                    const value = districtWithCode_Name[0];
                    const label = `${districtWithCode_Name[0]} - ${districtWithCode_Name[2]}`;
                    return { value, label: label };
                })
            )
        );
    }

    async requestAccess(button: DxButtonComponent): Promise<void> {
        if (!this.selectedModules || this.selectedModules.length <= 0) {
            await alert(
                'You must select at least one feature module to request access',
                'Please select a module'
            );
            return;
        }
        this.beginRequestVisuals(button);
        this._requestAccess
            .request(this.buildAccessRequest())
            .pipe(finalize(() => this.endRequestVisuals(button)))
            .subscribe(
                async () => {
                    await alert(
                        'You will receive a notification when your request has been fulfilled.',
                        'Successfully Submitted Access Request'
                    );
                    this._router.navigate(['/']);
                },
                async (error) =>
                    await alert(
                        'Request did not submit successfully.  Please try again.',
                        'Unknown error'
                    )
            );
    }

    private buildAccessRequest(): any {
        return new AccessRequest({
            modules: this.selectedModules,
            districts: this.selectedDistricts,
            companies: this.selectedCompanies,
            //Descope module
            // Don't include wmo special access if they end up not requesting WMO
            // wmoSpecialAccess: this.selectedModules.includes("Where's My Order")
            //     ? this.selectedWMOSpecialAccess
            //     : null,
            wmoSpecialAccess: null,
            companyNameAndSite: this.companyNameAndSite,
            teamContact: this.teamContact,
            //Descope module
            // managementSites: this.selectedModules.includes(
            //     'Asset Integrity Management as a Service (AIMaaS)'
            // )
            //     ? this.managementSites
            //     : null
            managementSites: null
        });
    }

    private beginRequestVisuals(button: DxButtonComponent) {
        this.sendingRequest = true;
        button.instance.option('icon', 'fa fa-circle-o-notch fa-spin');
        button.instance.option('text', 'Sending...');
    }

    private endRequestVisuals(button: DxButtonComponent) {
        this.sendingRequest = false;
        button.instance.option('icon', null);
        button.instance.option('text', 'Request Access');
    }
}
