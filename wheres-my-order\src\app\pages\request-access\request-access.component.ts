import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { DxButtonComponent } from 'devextreme-angular/ui/button';
import { alert } from 'devextreme/ui/dialog';
import { Observable } from 'rxjs';
import { finalize } from 'rxjs/operators';
import {
    RequestAccessModule,
    RequestAccessService,
    WMOSpecialAccess
} from '../../core/services';
import { AccessRequest } from '../../models';
import { UserProfile } from '../../profile/models';
import { DistrictService, UsersService } from '../../shared/services';

@Component({
    selector: 'app-request-access',
    templateUrl: './request-access.component.html',
    styleUrls: ['./request-access.component.scss']
})
export class RequestAccessComponent implements OnInit {
    modules: RequestAccessModule[] = [
        // "Where's My Order",
        // 'Equipment Demand Request',
        // 'Asset Integrity Management as a Service (AIMaaS)',
        // 'Work Management - Pipeline',
        // 'Bolting Calculator',
        'Remote Asset Monitoring',
        'Asset Performance Management (APM)'
    ];

    wmoSpecialAccessItems: WMOSpecialAccess[] = [
        'None',
        'Manufacturing User',
        'Engineering User',
        'District Manager'
    ];

    districts$: Observable<string[]>;
    currentUser$: Observable<UserProfile>;
    companyNameAndSite: string;
    teamContact: string;
    selectedModules: RequestAccessModule[];
    selectedWMOSpecialAccess: WMOSpecialAccess;
    selectedDistricts: string[];
    managementSites: string;
    sendingRequest: boolean;

    constructor(
        private readonly _districts: DistrictService,
        private readonly _users: UsersService,
        private readonly _requestAccess: RequestAccessService,
        private readonly _router: Router
    ) {}

    ngOnInit(): void {
        this.currentUser$ = this._users.currentProfile$;
        this.districts$ = this._districts.getDistrictNumbers(false);
    }

    async requestAccess(button: DxButtonComponent): Promise<void> {
        if (!this.selectedModules || this.selectedModules.length <= 0) {
            await alert(
                'You must select at least one feature module to request access',
                'Please select a module'
            );
            return;
        }

        this.beginRequestVisuals(button);
        this._requestAccess
            .request(this.buildAccessRequest())
            .pipe(finalize(() => this.endRequestVisuals(button)))
            .subscribe(
                async () => {
                    await alert(
                        'You will receive a notification when your request has been fulfilled.',
                        'Successfully Submitted Access Request'
                    );
                    this._router.navigate(['/']);
                },
                async (error) =>
                    await alert(
                        'Request did not submit successfully.  Please try again.',
                        'Unknown error'
                    )
            );
    }

    private buildAccessRequest(): any {
        return new AccessRequest({
            modules: this.selectedModules,
            districts: this.selectedDistricts,
            //Descope module
            // Don't include wmo special access if they end up not requesting WMO
            // wmoSpecialAccess: this.selectedModules.includes("Where's My Order")
            //     ? this.selectedWMOSpecialAccess
            //     : null,
            wmoSpecialAccess: null,
            companyNameAndSite: this.companyNameAndSite,
            teamContact: this.teamContact,
            //Descope module
            // managementSites: this.selectedModules.includes(
            //     'Asset Integrity Management as a Service (AIMaaS)'
            // )
            //     ? this.managementSites
            //     : null
            managementSites: null
        });
    }

    private beginRequestVisuals(button: DxButtonComponent) {
        this.sendingRequest = true;
        button.instance.option('icon', 'fa fa-circle-o-notch fa-spin');
        button.instance.option('text', 'Sending...');
    }

    private endRequestVisuals(button: DxButtonComponent) {
        this.sendingRequest = false;
        button.instance.option('icon', null);
        button.instance.option('text', 'Request Access');
    }
}
