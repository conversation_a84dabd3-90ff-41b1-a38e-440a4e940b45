import { Injectable } from '@angular/core';
import {
    ActivatedRouteSnapshot,
    CanActivate,
    CanActivateChild,
    CanLoad,
    Route,
    RouterStateSnapshot,
    UrlSegment
} from '@angular/router';
import { combineLatest, Observable, of } from 'rxjs';
import { map, switchMap } from 'rxjs/operators';
import { isAccessAllowedBasedOnRestrictedRoles } from '../../aimaas/services/utils.service';
import { UsersService } from '../../shared/services';
import { routeRoleRestrictions } from '../../shared/services/route-role-restrictions';
import { userHasRoleGroup, userIsAppAdmin } from '../operators';

@Injectable({
    providedIn: 'root'
})
export class AIMaaSGuard implements CanActivate, CanActivateChild, CanLoad {
    constructor(private readonly _users: UsersService) { }

    canActivate(
        next: ActivatedRouteSnapshot,
        state: RouterStateSnapshot
    ): Observable<boolean> {
        return this.isAllowedToAccessCREDO(state.url);
    }

    canActivateChild(
        next: ActivatedRouteSnapshot,
        state: RouterStateSnapshot
    ): Observable<boolean> {
        return this.isAllowedToAccessCREDO(state.url);
    }

    canLoad(route: Route, segments: UrlSegment[]): Observable<boolean> {
        const fullPath = '/' + segments.map(s => s.path).join('/');
        return this.isAllowedToAccessCREDO(fullPath);
    }

    private isAllowedToAccessCREDO(currentUrl: string): Observable<boolean> {
        // const hasCredo = this._users.currentProfile$.pipe(
        //     userHasRoleGroup('aimaas')
        // );
        // const isAppAdmin = this._users.currentProfile$.pipe(userIsAppAdmin());
        // return combineLatest([hasCredo, isAppAdmin]).pipe(
        //     map(([credo, appAdmin]) => credo || appAdmin)
        // );
        return this._users.currentProfile$.pipe(
            switchMap((profile): Observable<boolean> => {
                const roles: string[] = profile?.roles || [];
                const restrictedRoles = routeRoleRestrictions[currentUrl];
                if (restrictedRoles && restrictedRoles.length >= 0) {
                    const hasAllowedRole = isAccessAllowedBasedOnRestrictedRoles(roles, restrictedRoles);
                    if (!hasAllowedRole) {
                        return of(false);
                    }
                }

                const hasCredo$ = of(profile).pipe(userHasRoleGroup('aimaas'));
                const isAdmin$ = of(profile).pipe(userIsAppAdmin());

                return combineLatest([hasCredo$, isAdmin$]).pipe(
                    map(([hasCredo, isAdmin]) => hasCredo || isAdmin)
                );
            })
        );
    }
}
