import { Injectable } from '@angular/core';
import {
    ActivatedRouteSnapshot,
    CanActivate,
    CanActivateChild,
    CanLoad,
    Route,
    RouterStateSnapshot,
    UrlSegment
} from '@angular/router';
import { combineLatest, Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { UsersService } from '../../shared/services';
import { userHasRoleGroup, userIsAppAdmin } from '../operators';

@Injectable({
    providedIn: 'root'
})
export class AIMaaSGuard implements CanActivate, CanActivateChild, CanLoad {
    constructor(private readonly _users: UsersService) {}

    canActivate(
        next: ActivatedRouteSnapshot,
        state: RouterStateSnapshot
    ): Observable<boolean> {
        return this.isAllowedToAccessCREDO();
    }

    canActivateChild(
        next: ActivatedRouteSnapshot,
        state: RouterStateSnapshot
    ): Observable<boolean> {
        return this.isAllowedToAccessCREDO();
    }

    canLoad(route: Route, segments: UrlSegment[]): Observable<boolean> {
        return this.isAllowedToAccessCREDO();
    }

    private isAllowedToAccessCREDO(): Observable<boolean> {
        const hasCredo = this._users.currentProfile$.pipe(
            userHasRoleGroup('aimaas')
        );
        const isAppAdmin = this._users.currentProfile$.pipe(userIsAppAdmin());
        return combineLatest([hasCredo, isAppAdmin]).pipe(
            map(([credo, appAdmin]) => credo || appAdmin)
        );
    }
}
