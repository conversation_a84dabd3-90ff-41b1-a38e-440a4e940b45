import { Component, EventEmitter, Input, Output } from '@angular/core';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { uaResult } from '../../../core/browser-detection';
import { AuthService } from '../../../shared/services';

@Component({
    selector: 'app-header',
    templateUrl: 'header.component.html',
    styleUrls: ['./header.component.scss']
})
export class HeaderComponent {
    @Output()
    menuToggle = new EventEmitter();

    @Input()
    menuToggleEnabled = false;

    @Input()
    title: string;

    isProdEnvironment: boolean;
    environment: string;

    get isLoggedIn(): boolean {
        return this._auth.isLoggedIn;
    }

    userMenuItems = [
        {
            text: 'Profile',
            icon: 'user',
            onClick: () => {
                this._router.navigate(['/profile/user']);
            }
        },
        {
            text: 'About',
            icon: 'info',
            onClick: () => this._router.navigate(['/about'])
        },
        {
            text: 'Help and Feedback',
            icon: 'help',
            onClick: () => this._router.navigate(['/help-and-feedback'])
        },
        {
            text: 'Logout',
            icon: 'runner',
            onClick: () => {
                this._auth.logout();
            }
        }
    ];

    supportedBrowser: boolean;
    loggingIn: boolean;

    constructor(
        private readonly _router: Router,
        private readonly _auth: AuthService,
        private readonly _toasts: ToastrService
    ) {
        const href = window.location.href;
        this.isProdEnvironment =
            href.toLocaleLowerCase().includes('digital.') ||
            href.toLocaleLowerCase().includes('run-clientportal-frontend-prod');

        if (
            href.toLocaleLowerCase().includes('digitaldev') ||
            href.toLocaleLowerCase().includes('run-clientportal-frontend-dev')
        ) {
            this.environment = 'Development';
        } else if (
            href.toLocaleLowerCase().includes('digitalstaging') ||
            href.toLocaleLowerCase().includes('run-clientportal-frontend-stg')
        ) {
            this.environment = 'Staging';
        } else if (href.toLocaleLowerCase().includes('digitaltest')) {
            this.environment = 'Test';
        } else if (href.toLocaleLowerCase().includes('localhost')) {
            this.environment = 'Local';
        }
        //|| uaResult.browser.name.includes('Firefox')
        if (
            uaResult.browser.name.includes('Chrome')  ||
            (uaResult.browser.name.includes('Edge') &&
                uaResult.engine.name !== 'EdgeHTML')
        ) {
            this.supportedBrowser = true;
        } else {
            this.supportedBrowser = false;
        }
    }

    login = () => {
        if (!window.navigator.onLine) {
            this.showOfflineToast();
            return;
        }
        this.loggingIn = true;
        this._auth.login();
    };

    forgotTEAMPassword = () => {
        this.forgotPasswordClicked(true);
    };

    forgotClientPassword = () => {
        this.forgotPasswordClicked(false);
    };

    toggleMenu = () => {
        this.menuToggle.emit();
    };

    private forgotPasswordClicked(teamEmployee = false) {
        if (!window.navigator.onLine) {
            this.showOfflineToast();
            return;
        }
        if (teamEmployee) {
            window.location.href = 'https://reset.teaminc.com/';
        } else {
            this._auth.forgotPassword();
        }
    }

    private showOfflineToast() {
        this._toasts.error(
            'No network connection detected. Please connect to a network.',
            'Offline'
        );
    }
}
