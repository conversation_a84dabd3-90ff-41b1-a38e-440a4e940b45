import { Location } from '@angular/common';
import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { DxDataGridComponent } from 'devextreme-angular/ui/data-grid';
import ArrayStore from 'devextreme/data/array_store';
import DataSource from 'devextreme/data/data_source';
import { exportDataGrid } from 'devextreme/excel_exporter';
import dxDataGrid from 'devextreme/ui/data_grid';
import { Workbook } from 'exceljs';
import { saveAs } from 'file-saver';
import { combineLatest } from 'rxjs';
import { UserProfile } from '../../../profile/models/user-profile';
import { Breadcrumb } from '../../../shared/components';
import { DataGridService, UsersService } from '../../../shared/services';
import {
    AnomaliesRecommendations,
    Asset,
    AssetAttachment,
    AssetComponent,
    AssetInspection,
    AssetManagementSite,
    AssetSubmissions,
    InspectionAttachment,
    RowExpandingHandler
} from '../../models';
import { GeneralAnalysis } from '../../models/general-analysis';
import { SiteLabelPipe } from '../../pipes';
import { CredoSoftService } from '../../services';
import { IndexedDbService } from '../../services/indexed-db.service';
import { UtilsService } from '../../services/utils.service';

@Component({
    selector: 'app-anomaly-drill-down',
    templateUrl: './anomaly-drill-down.component.html',
    styleUrls: ['./anomaly-drill-down.component.scss']
})
export class AnomalyDrillDownComponent implements OnInit, AfterViewInit {
    // constructor() { }

    isLoading: boolean;
    selectedAssetId: any;
    storageKey: string = 'datagrid-state';
    popupVisible = false;
    components: any[];
    inspections: any[];
    anomalyDataSource: DataSource = new DataSource({ store: [] });
    assetComponentMap: AssetComponent[];
    generalAnalysis: any[];
    assetGeneralAnalysisMap: GeneralAnalysis[];
    assetInspectionMap: any;
    currentFilter: any = [];
    filterValue: Array<any>;
    assetAttachments: AssetAttachment[] = [];
    inspectionAttachments: InspectionAttachment[] = [];
    currentAssetDetails: Asset;
    inspectionSchedule: any[];
    submissionsSchedule: any[];
    visibleAssetAttachments: AssetAttachment[];
    componentAttachments: AssetAttachment[] = [];
    submissions: any[];
    visibleInspectionAttachments: InspectionAttachment[] = [];
    anomaliesData: AnomaliesRecommendations[] = [];
    submissionPopupVisible: boolean = false;
    initialAnomaly: AnomaliesRecommendations = null;
    submissionPopupTitle: string = 'Anomaly Update';
    availableSites: AssetManagementSite[];
    selectedSite: AssetManagementSite;
    userId: string;
    breaCrumbLabel: string = 'Recommendation List';
    isDataLoaded: boolean = false;
    currentAssetId: string;
    isHideButton: boolean = false;
    currentUser: UserProfile;
    assetDetailsPopupVisible: boolean = false;
    equipmentPopupTitle: string = ' ';
    inspectionPopupTitle: string = ' ';
    assets: Asset[] = [];
    visibleInspectionAnamolies: AnomaliesRecommendations[];
    generalInspectionDetails: AssetInspection[];
    selectedOperationId: any;
    selectedLocationId: any;
    isFromOverview: boolean = false;
    hideBreadcrumbs: boolean = false;

    addUserPreferences = (
        useri: string,
        storageKey: string,
        values: string
    ) => {
        if (this.credoService) {
            this.credoService
                .addPreference(useri, storageKey, values)
                .subscribe(
                    (response) => {
                        console.log('Preference added successfully:', response);
                    },
                    (error) => {
                        //    console.error('Error adding preference:', error);
                    }
                );
        } else {
            console.error('CredoService is undefined!');
        }
    };
    saveState = (state) => {
        this._users.currentProfile$.subscribe((user: any) => {
            this.userId = user.id;
        });
        this.addUserPreferences(
            this.userId,
            'equipment',
            JSON.stringify(state)
        );
    };
    loadState = async () => {
        try {
            const response = await this.getUserPreference();
            if (response && response.equipment) {
                const equipmentValue = JSON.parse(response.equipment);
                equipmentValue.filterValue = this.currentFilter;
                return equipmentValue;
            } else {
                console.error('No anomaly preference found in response.');
                return null;
            }
        } catch (error) {
            console.error('Error loading preference:', error);
            return null;
        }
    };
    @ViewChild(DxDataGridComponent)
    grid: DxDataGridComponent;
    @ViewChild(DxDataGridComponent, { static: false })
    dataGrid!: DxDataGridComponent;
    costCentreOptions: { id: string; name: string }[];
    districtvalues: { id: string; name: string }[];
    locationOptions: { id: string; name: string }[];
    districtToClientMap: Map<string, any[]>;
    clientToDistrictMap: Map<string, any[]>;
    districtToSiteMap: Map<string, any[]>;
    filteredDistrictOptions: { id: string; name: string }[];
    filteredClientOptions: { id: string; name: string }[];
    selectedCostCentres: any;
    selectedCompany: any;
    clientcostcentre: any[];
    filteredValue: any = null;
    clientLocationData: any[];

    constructor(
        private readonly credoService: CredoSoftService,
        private readonly _sitePipe: SiteLabelPipe,
        private readonly _users: UsersService,
        private readonly _grid: DataGridService,
        private location: Location,
        private route: ActivatedRoute,
        private router: Router,
        private indexedDbService: IndexedDbService,
        private utilsService: UtilsService
    ) { }

    crumbs: Breadcrumb[];

    ngAfterViewInit(): void {
        // Only refresh grid if dataGrid and anomalyDataSource are ready
        setTimeout(() => {
            if (this.dataGrid && this.anomalyDataSource) {
                this.refreshGrid();
            }
        }, 0);
    }

    getUserPreference(): Promise<any> {
        this._users.currentProfile$.subscribe((user: any) => {
            this.userId = user.id;
        });
        return new Promise((resolve, reject) => {
            this.credoService.getPreference(this.userId, 'anomaly').subscribe(
                (response) => {
                    if (response && response.equipment) {
                        resolve(response);
                    } else {
                        reject('No equipment preference found in response.');
                    }
                },
                (error) => {
                    reject('Error fetching preference: ' + error);
                }
            );
        });
    }

    addUserPreference(useri: string, storageKey: string, values: string): void {
        if (this.credoService) {
            this.credoService
                .addPreference(useri, storageKey, values)
                .subscribe(
                    (response) => {
                        // console.log('Preference added successfully:', response);
                    },
                    (error) => {
                        // console.error('Error adding preference:', error);
                    }
                );
        } else {
            console.error('CredoService is undefined!');
        }
    }
    async ngOnInit(): Promise<void> {
        if (window.location.hash.includes('fromOverview')) {
            const url = window.location.href;
            const siteLocationId = decodeURIComponent(
                url.split('fromOverview=')[1]
            ).split('locationId=')[1];
            try {
                this.breaCrumbLabel = decodeURIComponent(
                    url.split('fromOverview=')[1]
                ).split('?')[0];
                const userProfile$ = this._users.currentProfile$;
                this.hideBreadcrumbs = true;
                userProfile$.subscribe((user) => {
                    this.currentUser = user;
                });
                const userId = JSON.parse(JSON.stringify(this.currentUser?.id));
                const anomalies = await this.indexedDbService.getItem<any[]>(
                    `anomalies_${userId}`
                );
                const assets = await this.indexedDbService.getItem<any[]>(
                    `assets_${userId}`
                );
                const inspections = await this.indexedDbService.getItem<any[]>(
                    `inspections_${userId}`
                );
                const sites = await this.indexedDbService.getItem<any[]>(
                    `assetManagementSites_${userId}`
                );
                const clientLocationData = await this.indexedDbService.getItem<
                    any[]
                >(`clientLocationData_${userId}`);
                this.assets = assets || [];
                this.inspections = inspections || [];
                this.availableSites = sites || [];
                this.clientLocationData = clientLocationData || [];
                this.clientcostcentre = clientLocationData || [];
                this.processLocations(this.clientcostcentre);
                const dataSource = new DataSource({
                    store: new ArrayStore({
                        data: anomalies || []
                    })
                });
                this.anomalyDataSource = dataSource;

                const roles = this.currentUser.roles.map((role) =>
                    role.toLowerCase()
                );

                if (roles) {
                    if (roles.includes('aimaas:demo')) {
                        this.availableSites = this.availableSites.filter(
                            (site) =>
<<<<<<< HEAD
                                site.locationid ==
                                Number(localStorage.getItem('selectedSite'))
                        ) ?? this.availableSites[0];

                    if (history?.state?.data?.assetObjIds) {
                        //  console.log('inside asset object idss');
                        if (history.state.data.assetObjIds.length <= 0) {
                            this.currentFilter = [
                                'assetid',
                                'noneof',
                                (
                                    await this.anomalyDataSource.store().load() as any[]
                                )?.map((item) => item.assetid)
                            ];
                        } else {
                            this.currentFilter = [
                                'assetid',
                                'anyof',
                                history.state.data.assetObjIds
                            ];
                        }
=======
                                site.locationid == Number('635140707384299520')
                        );
>>>>>>> migration/base
                    }
                }

                this.selectedSite =
                    this.availableSites.find(
                        (site) =>
                            site.locationid ==
                            Number(
                                localStorage.getItem(
                                    `selectedSite${siteLocationId}`
                                )
                            )
                    ) ?? this.availableSites[0];
                this.filterValue =
                    JSON.parse(sessionStorage.getItem('currentFilter')) || [];
                // Add location filter condition
                setTimeout(() => {
                    this.anomalyDataSource.filter(
                        (anomaly) =>
                            anomaly.locationid === this.selectedSite.locationid
                    );
                    this.anomalyDataSource.load();
                }, 0);
                this.currentFilter = this.filterValue;
                this.isFromOverview = this.checkIfFromOverview();
                // Apply the filter and refresh the grid
                if (this.dataGrid) {
                    this.dataGrid.instance.filter(this.currentFilter);
                    await this.dataGrid.instance.refresh(); // Ensure refresh completes
                }
            } catch (error) {
                console.error('Error loading data source:', error);
            }
            const storedSiteId = localStorage.getItem(
                `selectedSite${siteLocationId}`
            );
            if (storedSiteId) {
                this.selectedSite = this.availableSites?.find(
                    (site) => site.locationid == Number(storedSiteId)
                );
            }
            this.isLoading = false;
            this.isDataLoaded = true;
        } else {
            combineLatest([
                this.credoService.getAllAnomaliesAsDataSource(),
                this.credoService.inspections$,
                this.credoService.assetManagementSites$,
                this._users.currentProfile$,
                this.credoService.anomalies$,
                this.credoService.assets$,
                this.credoService.clientLocationData$
            ])
                .pipe()
                .subscribe(
                    async ([
                        ds,
                        inspections,
                        sites,
                        currentUser,
                        anomaliesData,
                        assets,
                        clientLocationData
                    ]: [
                            DataSource,
                            AssetInspection[],
                            AssetManagementSite[],
                            UserProfile,
                            AnomaliesRecommendations[],
                            Asset[],
                            any
                        ]) => {
                        this.anomalyDataSource = ds;
                        this.inspections = inspections;
                        this.currentUser = currentUser;
                        this.anomaliesData = anomaliesData;
                        this.clientcostcentre = clientLocationData;
                        this.clientLocationData = clientLocationData || [];
                        const state = history.state;
                        if (state && state.data && state.data.currentFilter) {
                            this.filterValue = state.data.currentFilter;
                        } else if (
                            window.location.hash.includes('fromOverview')
                        ) {
                            this.filterValue = JSON.parse(
                                sessionStorage.getItem('currentFilter')
                            );
                        }

                        this.availableSites = sites;
                        this.assets = assets;
                        this.userId = currentUser.email;

                        const roles = currentUser.roles.map((role) =>
                            role.toLowerCase()
                        );
                        if (roles) {
                            if (roles.includes('aimaas:demo')) {
                                this.availableSites =
                                    this.availableSites.filter(
                                        (site) =>
                                            site.locationid ==
                                            Number('635140707384299520')
                                    );
                                this.clientcostcentre =
                                    clientLocationData?.filter(
                                        (site) =>
                                            site?.locationid ==
                                            Number('635140707384299520')
                                    );
                            } else if (
                                !roles.includes('app:admin') &&
                                !roles.includes('aimaas:admin') &&
                                !roles.includes('aimaas:all') &&
                                !roles.includes('aimaas:district') &&
                                !roles.includes('aimaas:client') &&
                                currentUser.assetManagementSiteIds
                            ) {
                                this.availableSites =
                                    this.availableSites.filter((site) =>
                                        currentUser.assetManagementSiteIds.includes(
                                            site.locationid
                                        )
                                    );
                                this.clientcostcentre =
                                    clientLocationData.filter((site) =>
                                        currentUser.assetManagementSiteIds.includes(
                                            site?.locationid
                                        )
                                    );
                            }
                        }
                        this.processLocations(this.clientcostcentre);
                        let savedCostCentre = localStorage.getItem('selectedDistrict');
                        this.selectedCostCentres = savedCostCentre ?? null;
                        // Always get districts for the selected cost centre
                        let clientsForDistrict = this.utilsService.getUniqueBy(this.districtToClientMap.get(savedCostCentre) || [], 'id');
                        this.districtToClientMap.get(savedCostCentre ?? null) || [];
                        if (
                            !clientsForDistrict.length &&
                            this.districtvalues.length
                        ) {
                            // Assign all districts if none for cost centre
                            clientsForDistrict = this.utilsService.getUniqueBy(this.districtvalues, 'id');
                        }
                        this.filteredClientOptions = [{ id: '', name: '' }, ...clientsForDistrict.sort((a, b) => a.name.localeCompare(b.name))];

                        // District
                        let savedClient = localStorage.getItem('selectedClient');
                        this.selectedCompany = savedClient ?? null;

                        // Sites
                        let filteredLocations =
                            this.districtToSiteMap.get(savedClient ?? null) || [];
                        if (filteredLocations.length === 0) {
                            // Assign all sites
                            filteredLocations = Array.from(
                                this.districtToSiteMap.values()
                            ).flat();
                        }
                        this.availableSites = filteredLocations.map((site) => ({
                            locationid: site.locationid,
                            locationname: site.locationname,
                            clientid: site.clientid ? Number(site.clientid) : 0,
                            clientname: site.clientname || ''
                        }));

                        // Site
                        let savedSite = localStorage.getItem('selectedSite');
                        let selectedSiteObj = this.availableSites.find(
                            (site) => site.locationid == Number(savedSite)
                        );
                        if (!selectedSiteObj && this.availableSites.length > 0) {
                            selectedSiteObj = this.availableSites[0];
                            localStorage.setItem(
                                'selectedSite',
                                String(selectedSiteObj.locationid)
                            );
                        }
                        this.selectedSite = selectedSiteObj;

                        if (history?.state?.data?.assetObjIds) {
                            if (history.state.data.assetObjIds.length <= 0) {
                                this.currentFilter = [
                                    'assetid',
                                    'noneof',
                                    (
                                        await this.anomalyDataSource
                                            .store()
                                            .load()
                                    )?.map((item) => item.assetid)
                                ];
                            } else {
                                this.currentFilter = [
                                    'assetid',
                                    'anyof',
                                    history.state.data.assetObjIds
                                ];
                            }
                        }
                        this.isLoading = false;
                        this.isDataLoaded = true;
                    }
                );
        }
        if (
            window.location.hash.includes('fromOverview') ||
            history?.state?.source === 'overview'
        )
            this.isHideButton = true;
        this.isFromOverview = this.checkIfFromOverview();
        setTimeout(() => {
            if (history.state?.data?.currentFilter) {
                this.currentFilter =
                    history.state.data.currentFilter;
            }
        }, 0);
        this.updateBreadcrumbs();
    }

    refreshGrid() {
        if (this.dataGrid && this.anomalyDataSource && Array.isArray(this.anomalyDataSource.items?.())) {
            this.dataGrid.instance.clearFilter();
            if (this.currentFilter) {
                this.dataGrid.instance.filter(this.currentFilter);
            }
            this.dataGrid.instance.refresh();
        }
    }
    checkIfFromOverview(): boolean {
        const hash = window.location.hash;
        const queryString = hash.split('?')[1] || '';
        const urlParams = new URLSearchParams(queryString);
        return (
            urlParams.has('fromOverview') ||
            history.state?.source === 'overview'
        );
    }

    processLocations(data: any[]) {
        if (!data || !Array.isArray(data) || data.length === 0) {
            return;
        }

        const costCentreMap = new Map<string, string>();
        const clientMap = new Map<string, string>();
        const locationMap = new Map<string, string>(); // Add this for locations
        const districtToSiteMap = new Map<string, Set<any>>(); // New map for district -> locations
        const filteredClientMap = new Map<string, string>();
        const CostCentresToDistrictMap = new Map<string, Set<any>>();
        const DistrictsToCostCentreMap = new Map<string, Set<any>>();

        // Process the input data
        data.forEach((item) => {
            // Process cost centre information
            if (item?.costcenterid && item?.costcentername) {
                costCentreMap.set(
                    item.costcenterid,
                    item.costcentername.trim()
                );
            }

            // Process client information
            if (item?.clientid && item?.clientname) {
                clientMap.set(item.clientid, item.clientname.trim());
            }

            // Process location information
            if (item?.locationid && item?.locationname) {
                locationMap.set(item.locationid, item.locationname.trim());
            }

            // Cost Centre → Districts (Ensure uniqueness)
            if (item?.costcenterid && item?.clientid && item?.clientname) {
                if (!CostCentresToDistrictMap.has(item.costcenterid)) {
                    CostCentresToDistrictMap.set(item.costcenterid, new Set());
                }

                // Use a Map for uniqueness based on ID
                let uniqueDistricts = CostCentresToDistrictMap.get(
                    item.costcenterid
                );
                if (
                    !Array.from(uniqueDistricts).some(
                        (d) => d.id === item.clientid
                    )
                ) {
                    uniqueDistricts.add({
                        id: item.clientid,
                        name: item.clientname.trim()
                    });
                }
            }

            // District → Cost Centres
            if (item?.clientid && item?.costcenterid && item?.costcentername) {
                if (!DistrictsToCostCentreMap.has(item.clientid)) {
                    DistrictsToCostCentreMap.set(item.clientid, new Set());
                }
                DistrictsToCostCentreMap.get(item.clientid)?.add({
                    id: item.costcenterid,
                    name: item.costcentername.trim()
                });
            }
            if (item?.clientid && item?.locationid && item?.locationname) {
                if (!districtToSiteMap.has(item.clientid)) {
                    districtToSiteMap.set(item.clientid, new Set());
                }

                let uniqueLocations = districtToSiteMap.get(item.clientid);
                uniqueLocations.add({
                    locationid: item.locationid, // ✅ Ensure 'locationid' is used
                    locationname: item.locationname.trim()
                });
            }
        });

        // Transform Map data into sorted arrays for the dropdown options
        this.costCentreOptions = Array.from(costCentreMap.entries())
            .map(([id, name]) => ({ id, name }))
            .sort((a, b) => a.name.localeCompare(b.name));
        CostCentresToDistrictMap.get(this.selectedCostCentres)?.forEach(
            ({ id, name }) => {
                filteredClientMap.set(id, name);
            }
        );
        this.districtvalues = Array.from(clientMap.entries())
            .map(([id, name]) => ({ id, name }))
            .sort((a, b) => a.name.localeCompare(b.name));

        this.locationOptions = Array.from(locationMap.entries())
            .map(([id, name]) => ({ id, name }))
            .sort((a, b) => a.name.localeCompare(b.name));

        if (!this.districtToClientMap) {
            this.districtToClientMap = new Map<string, any[]>();
        }

        CostCentresToDistrictMap.forEach((districtSet, costCentreId) => {
            this.districtToClientMap.set(
                costCentreId,
                Array.from(districtSet)
            );
        });

        this.districtToSiteMap = new Map<string, any[]>();
        districtToSiteMap.forEach((locationSet, districtId) => {
            this.districtToSiteMap.set(districtId, Array.from(locationSet));
        });

        if (!this.clientToDistrictMap) {
            this.clientToDistrictMap = new Map<string, any[]>();
        }

        DistrictsToCostCentreMap.forEach((costCentreId, districtSet) => {
            this.clientToDistrictMap.set(
                districtSet,
                Array.from(costCentreId)
            );
        });

        // Convert Set to Array for mappings

        this.filteredDistrictOptions = [{ id: '', name: '' }, ...this.utilsService.getUniqueBy(this.costCentreOptions.sort((a, b) => a.name.localeCompare(b.name)), 'id')];
        this.filteredClientOptions = [{ id: '', name: '' }, ...this.utilsService.getUniqueBy(this.districtvalues.sort((a, b) => a.name.localeCompare(b.name)), 'id')];
        const savedCostCentreId = localStorage.getItem('selectedDistrict') || '';
        const savedClientId = localStorage.getItem('selectedClient') || '';

        // Filter clientOptions based on savedCostCentreId (District)
        let clientOptions: any[] = [];
        if (savedCostCentreId && this.districtToClientMap.has(savedCostCentreId)) {
            clientOptions = this.utilsService.getUniqueBy(this.districtToClientMap.get(savedCostCentreId) || [], 'id');
        } else {
            clientOptions = this.utilsService.getUniqueBy(this.districtvalues, 'id');
        }
        this.filteredClientOptions = [
            { id: '', name: '' },
            ...clientOptions.sort((a, b) => a.name.localeCompare(b.name))
        ];

        // Filter districtOptions based on savedClientId (Client)
        let districtOptions: any[] = [];
        if (savedClientId && this.clientToDistrictMap.has(savedClientId)) {
            districtOptions = this.utilsService.getUniqueBy(this.clientToDistrictMap.get(savedClientId) || [], 'id');
        } else {
            districtOptions = this.utilsService.getUniqueBy(this.filteredDistrictOptions.filter(opt => opt.id !== '') // remove the empty option
                .sort((a, b) => a.name.localeCompare(b.name)),
                'id'
            );
        }
        this.filteredDistrictOptions = [
            { id: '', name: '' },
            ...this.utilsService.getUniqueBy(districtOptions.sort((a, b) => a.name.localeCompare(b.name)), 'id')
        ];
    }

    onDistrictChange(event: any) {
        const selectedDistrict = event.value;
        this.selectedCostCentres = selectedDistrict;
        localStorage.setItem('selectedDistrict', selectedDistrict);

        if (selectedDistrict) {
            const uniqueClients = this.utilsService.getUniqueBy(
                Array.from(new Set(this.districtToClientMap.get(selectedDistrict) || [])),
                'id'
            );
            this.filteredClientOptions = [{ id: '', name: '' }, ...uniqueClients.sort((a, b) => a.name.localeCompare(b.name))];

            setTimeout(() => {
                if (!this.selectedCompany) {
                    this.selectedCompany = this.filteredClientOptions[0]?.id || null;
                }
            });
        } else {
            const allClients = this.utilsService.getUniqueBy(
                Array.from(this.districtToClientMap.values()).flat(),
                'id'
            );
            this.filteredClientOptions = [{ id: '', name: '' }, ...allClients.sort((a, b) => a.name.localeCompare(b.name))];
        }

        localStorage.setItem('selectedClient', this.selectedCompany);

        this.filterSites();
    }

    onClientChange(event: any) {
        const selectedClient = event.value;
        this.selectedCompany = selectedClient;
        localStorage.setItem('selectedClient', selectedClient);

        if (selectedClient) {
            const districts = this.utilsService.getUniqueBy(
                Array.from(new Set(this.clientToDistrictMap.get(selectedClient) || [])),
                'id'
            );
            this.filteredDistrictOptions = [{ id: '', name: '' }, ...districts.sort((a, b) => a.name.localeCompare(b.name))];
        } else {
            const allDistricts = this.utilsService.getUniqueBy(
                Array.from(this.clientToDistrictMap.values()).flat(),
                'id'
            );
            this.filteredDistrictOptions = [{ id: '', name: '' }, ...allDistricts.sort((a, b) => a.name.localeCompare(b.name))];
        }

        this.filterSites();
    }

    filterSites() {
        const isValid = (val: any) =>
            val !== null && val !== undefined && val !== '' && val !== 'null' && val !== 'undefined';

        const district = isValid(this.selectedCostCentres);
        const client = isValid(this.selectedCompany);

        if (district && client) {
            this.updateAvailableSites(this.getFilteredSitesByBoth(this.selectedCostCentres, this.selectedCompany));
        } else if (district) {
            this.updateAvailableSites(this.getFilteredSitesByDistrict(this.selectedCostCentres));
        } else if (client) {
            this.updateAvailableSites(this.getFilteredSitesByClient(this.selectedCompany));
        } else {
            this.updateAvailableSites(Array.from(this.districtToSiteMap.values()).flat());
        }
    }

    updateAvailableSites(filteredLocations: any[]) {
        this.availableSites = filteredLocations;
        this.selectedSite = this.availableSites[0] || null;
        if (this.selectedSite) {
            localStorage.setItem('selectedSite', String(this.selectedSite.locationid));
        }
    }

    getFilteredSitesByDistrict(districtId: string): any[] {
        const clientIds = (this.districtToClientMap.get(districtId) || []).map(client => client.id);
        return clientIds.flatMap(id => this.districtToSiteMap.get(id) || []);
    }

    getFilteredSitesByClient(clientId: string): any[] {
        return this.districtToSiteMap.get(clientId) || [];
    }

    getFilteredSitesByBoth(districtId: string, clientId: string): any[] {
        const allSites = this.districtToSiteMap.get(clientId) || [];
        const validLocationIds = this.clientLocationData
            .filter(item => item.costcenterid === districtId && item.clientid == clientId)
            .map(item => item.locationid);
        return allSites.filter(loc => validLocationIds.includes(loc.locationid));
    }

    updateBreadcrumbs() {
        let sourceLabel = 'KPI Dashboards'; // Default label
        let sourceRoute = '/aimaas/dashboards';

        // ✅ Read source from history.state first
        let source = history.state?.source;

        // ✅ If history.state is empty, fallback to URL query parameters
        if (!source) {
            const hash = window.location.hash;
            const queryString = hash.split('?')[1] || '';
            const urlParams = new URLSearchParams(queryString);
            if (urlParams.has('fromOverview')) {
                sourceLabel = 'Overview Dashboard';
                sourceRoute = '/aimaas/overview-dashboard'; // ✅ Set Overview Dashboard when navigating from overview
            }
        }

        // ✅ Set Overview Dashboard when navigating from overview
        if (source === 'overview') {
            sourceLabel = 'Overview Dashboard';
            sourceRoute = '/aimaas/overview-dashboard';
        }

        // ✅ Ensure breadcrumb label is set correctly
        this.breaCrumbLabel =
            history.state?.breadCrumbLabel ?? 'Recommendations List'; // Default label for Equipments

        if (window.location.hash.includes('fromOverview')) {
            const breadcrumbLabel = sessionStorage.getItem('breadcrumbLabel');
            if (breadcrumbLabel) {
                this.breaCrumbLabel = breadcrumbLabel;
            }
        }

        // ✅ Prevent duplicate breadcrumbs
        this.crumbs = [
            { label: sourceLabel, route: sourceRoute },
            { label: this.breaCrumbLabel, route: '/aimaas/anomaly-drilldown' }
        ];
    }
    restoreAssetsDefaultsClicked = async (e) => {
        const result = await this._grid.resetGridState(this.grid);
        if (result) {
            this.addUserPreferences(this.userId, 'anomaly', JSON.stringify(''));
        }
    };

    clientSubmissionTitleValueChange(e: any) {
        this.submissionPopupTitle = e;
    }
    clientDataFormSubmitted(e: any) {
        this.submissionPopupVisible = false;
    }
    clientSubmitDataOnclick(e: string) {
        if (e === 'frombuttonclick') {
            this.initialAnomaly = null;
        }
        this.submissionPopupVisible = !this.submissionPopupVisible;
    }
    anomalyCellTemplate = (cellElement, cellInfo) => {
        const anomalyNumber = cellInfo.value ? cellInfo.value : '';
        const link = document.createElement('a');
        link.href = '#';
        link.innerText = anomalyNumber;
        link.onclick = (event) => {
            event.preventDefault();
            this.onAnomalyClick(cellInfo.data);
        };
        cellElement.appendChild(link);
    };
    onAnomalyClick(data: AnomaliesRecommendations) {
        this.initialAnomaly = data;
        this.clientSubmitDataOnclick('fromanomaly');
    }

    onContentReady(event) {
        this.grid?.instance?.endCustomLoading();
    }
    onCellPrepared(event) {
        if (event.rowType == 'data' || this.isDataLoaded) {
            this.isLoading = false;
        } else {
            this.isLoading = true;
        }
    }
    getComponentsForAsset(id: string): AssetComponent[] {
        if (!this.components) {
            return [];
        }
        return this.components.filter((item) => item.assetid === id);
    }
    getGeneralAnalysisForAsset(id: string): GeneralAnalysis[] {
        if (!this.generalAnalysis) {
            return [];
        }
        return this.generalAnalysis.filter((item) => item.assetid === id);
    }
    getInspectionsForAsset(id: string): AssetInspection[] {
        if (!this.inspections) {
            return [];
        }
        return this.inspections?.filter((item) => item.assetid === id);
    }
    getSubmissionForAsset(id: string): AssetSubmissions[] {
        if (!this.submissions) {
            return [];
        }
        return this.submissions?.filter((item) => item.assetId === id);
    }
    assetSelectionChanged(data) {
        const anomaly = data?.row?.data;
        if (!window.location.hash.includes('fromOverview')) {
            this.location.replaceState(
                `/aimaas/drilldown?assetid=${anomaly?.assetid}`
            );
        }
        const asset = this.assets?.find(
            (asset) => asset.id == anomaly?.assetid
        );
        this.assetComponentMap = [];
        var comp = this.assetComponentMap;
        this.currentAssetId = String(asset.id);
        this.credoService.getAllComponents(asset.id).subscribe((data) => {
            this.assetComponentMap = data;
            comp = this.assetComponentMap.sort((a, b) => {
                const clientCompare = a.componentname.localeCompare(
                    b.componentname
                );
                if (clientCompare !== 0) {
                    return clientCompare;
                }
            });
        });
        this.currentAssetDetails = asset;
        this.inspectionSchedule = this.getInspectionsForAsset(String(asset.id));
        this.equipmentPopupTitle = `EQUIPMENT - ${asset.assetid}`;
        this.assetDetailsPopupVisible = true;
    }

    closePopup() {
        this.popupVisible = false;
        this.assetDetailsPopupVisible = false;
        this.equipmentPopupTitle = ' ';
        this.inspectionPopupTitle = ' ';
        if (!window.location.hash.includes('fromOverview')) {
            this.location.replaceState('/aimaas/inspection-drilldown');
        }
    }
    inspectionSelectionChanged(data) {
        const anomaly = data?.row?.data;
        let inspection;
        inspection = this.inspections?.find(
            (ins) => ins?.planoperationid == anomaly?.operationid
        );
        if (inspection?.planoperationid !== null) {
            if (!window.location.hash.includes('fromOverview')) {
                this.location.replaceState(
                    `/aimaas/inspection-drilldown?op=${inspection?.planoperationid}`
                );
            }

            this.popupVisible = true;
        }
        this.inspectionPopupTitle =
            inspection.operationtype !== null
                ? `INSPECTION - ${inspection?.assetidname} - ${inspection?.operationtype
                } - ${inspection?.date.replace(/-/g, '/')} `
                : `SCHEDULE - ${inspection?.assetidname} - ${inspection?.scheduletype
                } - ${inspection?.nextinspectiondue
                    ? inspection.nextinspectiondue.replace(/-/g, '/')
                    : ''
                }`;
        this.visibleInspectionAnamolies = this.anomaliesData.filter(
            (anomaly) => anomaly.operationid == inspection.planoperationid
        );
        this.generalInspectionDetails = inspection;
        this.selectedOperationId = inspection.planoperationid;
    }
    formatPID(data) {
        if (data.pid == null) {
            return ' ';
        }
        const doc = new DOMParser().parseFromString(data.pid, 'text/html');
        return doc.documentElement.textContent ?? ' ';
    }
    formatLocalJudictional(data) {
        if (data.localjuridictional == null) {
            return ' ';
        }
        const doc = new DOMParser().parseFromString(
            data.localjuridictional,
            'text/html'
        );
        return doc.documentElement.textContent ?? ' ';
    }
    onRowExpanding(e: {
        cancel: boolean;
        component: dxDataGrid;
        element: HTMLElement;
        key: any;
        model: any;
    }) {
        RowExpandingHandler.handle(e);
    }

    customDisplayExpr = (site: any): string => {
        if (site) return this._sitePipe.transform(site);
    };

    changeSite(selectedSite) {
        let _selectedSite = selectedSite.selectedItem;
        if (_selectedSite == null) {
            setTimeout(() => {
                this.selectedSite = this.availableSites[0];
                localStorage.setItem(
                    'selectedSite',
                    String(this.selectedSite.locationid)
                );
                localStorage.setItem(
                    'selectedClient',
                    String(this.selectedCompany)
                );
                localStorage.setItem(
                    'selectedDistrict',
                    String(this.selectedCostCentres)
                );
            }, 1);
        } else {
            this.selectedSite = selectedSite.selectedItem;
            localStorage.setItem(
                'selectedSite',
                String(this.selectedSite.locationid)
            );
            localStorage.setItem(
                'selectedClient',
                String(this.selectedCompany)
            );
            localStorage.setItem(
                'selectedDistrict',
                String(this.selectedCostCentres)
            );
        }
        setTimeout(() => {
            this.anomalyDataSource.filter(
                (anomaly) => anomaly.locationid === this.selectedSite.locationid
            );
            this.anomalyDataSource.load();
        }, 0);
    }
    convertHtmlToText(html: string): string {
        if (html == null) {
            return ' ';
        }
        const doc = new DOMParser().parseFromString(html, 'text/html');
        return doc.documentElement.textContent ?? ' ';
    }
    formatAnomalyDescription(data) {
        if (data.anomalydescription == null) {
            return ' ';
        }
        const doc = new DOMParser().parseFromString(
            data.anomalydescription,
            'text/html'
        );
        return doc.documentElement.textContent ?? ' ';
    }
    formatAnomalyProposedRecom(data) {
        if (data.proposedrecommemendation == null) {
            return ' ';
        }
        const doc = new DOMParser().parseFromString(
            data.proposedrecommemendation,
            'text/html'
        );
        return doc.documentElement.textContent ?? ' ';
    }
    async onExporting(event) {
        const workbook = new Workbook();
        const worksheet = workbook.addWorksheet('Anomalies');
        await exportDataGrid({
            component: event.component,
            worksheet
        });
        const buffer: BlobPart = await workbook.xlsx.writeBuffer();
        saveAs(
            new Blob([buffer], { type: 'application/octet-stream' }),
            'Anomalies.xlsx'
        );
    }
    onCloseTab(): void {
        window.close();
    }
}
