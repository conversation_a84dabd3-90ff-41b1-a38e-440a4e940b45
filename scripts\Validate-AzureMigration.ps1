# Azure Migration Validation Script
# This script validates that all Azure services are properly configured and accessible

param(
    [Parameter(Mandatory=$true)]
    [string]$Environment = "dev",
    
    [Parameter(Mandatory=$true)]
    [string]$ResourceGroupName = "rg-kraken-dev",
    
    [Parameter(Mandatory=$false)]
    [string]$SubscriptionId
)

# Set error action preference
$ErrorActionPreference = "Stop"

Write-Host "Starting Azure Migration Validation for Environment: $Environment" -ForegroundColor Green

# Function to write test results
function Write-TestResult {
    param(
        [string]$TestName,
        [bool]$Success,
        [string]$Message = ""
    )
    
    if ($Success) {
        Write-Host "✅ $TestName - PASSED" -ForegroundColor Green
        if ($Message) { Write-Host "   $Message" -ForegroundColor Gray }
    } else {
        Write-Host "❌ $TestName - FAILED" -ForegroundColor Red
        if ($Message) { Write-Host "   $Message" -ForegroundColor Red }
    }
}

# Set subscription if provided
if ($SubscriptionId) {
    try {
        az account set --subscription $SubscriptionId
        Write-TestResult "Azure Subscription Set" $true "Using subscription: $SubscriptionId"
    } catch {
        Write-TestResult "Azure Subscription Set" $false "Failed to set subscription: $($_.Exception.Message)"
        exit 1
    }
}

# Test 1: Verify Azure CLI is logged in
Write-Host "`n1. Testing Azure CLI Authentication..." -ForegroundColor Yellow
try {
    $account = az account show --query "name" -o tsv
    Write-TestResult "Azure CLI Authentication" $true "Logged in as: $account"
} catch {
    Write-TestResult "Azure CLI Authentication" $false "Not logged in to Azure CLI"
    exit 1
}

# Test 2: Verify Resource Group exists
Write-Host "`n2. Testing Resource Group..." -ForegroundColor Yellow
try {
    $rg = az group show --name $ResourceGroupName --query "name" -o tsv
    Write-TestResult "Resource Group Exists" $true "Resource Group: $rg"
} catch {
    Write-TestResult "Resource Group Exists" $false "Resource Group '$ResourceGroupName' not found"
    exit 1
}

# Test 3: Verify Container Registry
Write-Host "`n3. Testing Container Registry..." -ForegroundColor Yellow
try {
    $acrName = "krakenacr"
    $acr = az acr show --name $acrName --query "name" -o tsv
    Write-TestResult "Container Registry Exists" $true "ACR: $acr"
    
    # Test ACR login
    az acr login --name $acrName
    Write-TestResult "Container Registry Login" $true "Successfully logged into ACR"
} catch {
    Write-TestResult "Container Registry" $false "Failed to access Container Registry: $($_.Exception.Message)"
}

# Test 4: Verify Key Vault
Write-Host "`n4. Testing Key Vault..." -ForegroundColor Yellow
try {
    $kvName = "kv-kraken-$Environment-001"
    $kv = az keyvault show --name $kvName --query "name" -o tsv
    Write-TestResult "Key Vault Exists" $true "Key Vault: $kv"
    
    # Test secret access
    $secrets = az keyvault secret list --vault-name $kvName --query "length(@)" -o tsv
    Write-TestResult "Key Vault Access" $true "Found $secrets secrets in Key Vault"
} catch {
    Write-TestResult "Key Vault" $false "Failed to access Key Vault: $($_.Exception.Message)"
}

# Test 5: Verify Storage Account
Write-Host "`n5. Testing Storage Account..." -ForegroundColor Yellow
try {
    $storageAccountName = "stakraken$Environment" + "001"
    $storage = az storage account show --name $storageAccountName --resource-group $ResourceGroupName --query "name" -o tsv
    Write-TestResult "Storage Account Exists" $true "Storage Account: $storage"
    
    # Test container access
    $containers = az storage container list --account-name $storageAccountName --auth-mode login --query "length(@)" -o tsv
    Write-TestResult "Storage Container Access" $true "Found $containers containers"
} catch {
    Write-TestResult "Storage Account" $false "Failed to access Storage Account: $($_.Exception.Message)"
}

# Test 6: Verify Container Apps Environment
Write-Host "`n6. Testing Container Apps Environment..." -ForegroundColor Yellow
try {
    $caeName = "cae-kraken-$Environment"
    $cae = az containerapp env show --name $caeName --resource-group $ResourceGroupName --query "name" -o tsv
    Write-TestResult "Container Apps Environment" $true "Environment: $cae"
} catch {
    Write-TestResult "Container Apps Environment" $false "Failed to access Container Apps Environment: $($_.Exception.Message)"
}

# Test 7: Verify Container Apps
Write-Host "`n7. Testing Container Apps..." -ForegroundColor Yellow
try {
    $backendAppName = "ca-cpa-backend-$Environment"
    $frontendAppName = "ca-cpa-frontend-$Environment"
    
    $backendApp = az containerapp show --name $backendAppName --resource-group $ResourceGroupName --query "name" -o tsv
    Write-TestResult "Backend Container App" $true "App: $backendApp"
    
    $frontendApp = az containerapp show --name $frontendAppName --resource-group $ResourceGroupName --query "name" -o tsv
    Write-TestResult "Frontend Container App" $true "App: $frontendApp"
    
    # Test app status
    $backendStatus = az containerapp show --name $backendAppName --resource-group $ResourceGroupName --query "properties.provisioningState" -o tsv
    $frontendStatus = az containerapp show --name $frontendAppName --resource-group $ResourceGroupName --query "properties.provisioningState" -o tsv
    
    Write-TestResult "Backend App Status" ($backendStatus -eq "Succeeded") "Status: $backendStatus"
    Write-TestResult "Frontend App Status" ($frontendStatus -eq "Succeeded") "Status: $frontendStatus"
} catch {
    Write-TestResult "Container Apps" $false "Failed to access Container Apps: $($_.Exception.Message)"
}

# Test 8: Verify Managed Identity
Write-Host "`n8. Testing Managed Identity..." -ForegroundColor Yellow
try {
    $miName = "mi-kraken-$Environment"
    $mi = az identity show --name $miName --resource-group $ResourceGroupName --query "name" -o tsv
    Write-TestResult "Managed Identity Exists" $true "Identity: $mi"
    
    $clientId = az identity show --name $miName --resource-group $ResourceGroupName --query "clientId" -o tsv
    Write-TestResult "Managed Identity Client ID" $true "Client ID: $clientId"
} catch {
    Write-TestResult "Managed Identity" $false "Failed to access Managed Identity: $($_.Exception.Message)"
}

# Test 9: Verify Application Insights
Write-Host "`n9. Testing Application Insights..." -ForegroundColor Yellow
try {
    $aiName = "ai-kraken-$Environment"
    $ai = az monitor app-insights component show --app $aiName --resource-group $ResourceGroupName --query "name" -o tsv
    Write-TestResult "Application Insights Exists" $true "App Insights: $ai"
    
    $connectionString = az monitor app-insights component show --app $aiName --resource-group $ResourceGroupName --query "connectionString" -o tsv
    Write-TestResult "Application Insights Connection" ($connectionString -ne $null) "Connection string configured"
} catch {
    Write-TestResult "Application Insights" $false "Failed to access Application Insights: $($_.Exception.Message)"
}

# Test 10: Test Application Endpoints (if apps are running)
Write-Host "`n10. Testing Application Endpoints..." -ForegroundColor Yellow
try {
    $backendUrl = az containerapp show --name "ca-cpa-backend-$Environment" --resource-group $ResourceGroupName --query "properties.configuration.ingress.fqdn" -o tsv
    $frontendUrl = az containerapp show --name "ca-cpa-frontend-$Environment" --resource-group $ResourceGroupName --query "properties.configuration.ingress.fqdn" -o tsv
    
    if ($backendUrl) {
        $backendResponse = Invoke-WebRequest -Uri "https://$backendUrl/health" -Method GET -TimeoutSec 30
        Write-TestResult "Backend Health Check" ($backendResponse.StatusCode -eq 200) "Status Code: $($backendResponse.StatusCode)"
    }
    
    if ($frontendUrl) {
        $frontendResponse = Invoke-WebRequest -Uri "https://$frontendUrl" -Method GET -TimeoutSec 30
        Write-TestResult "Frontend Health Check" ($frontendResponse.StatusCode -eq 200) "Status Code: $($frontendResponse.StatusCode)"
    }
} catch {
    Write-TestResult "Application Endpoints" $false "Failed to test endpoints: $($_.Exception.Message)"
}

Write-Host "`n🎉 Azure Migration Validation Complete!" -ForegroundColor Green
Write-Host "Review the results above to ensure all components are properly configured." -ForegroundColor Yellow

# Generate summary report
$timestamp = Get-Date -Format "yyyy-MM-dd_HH-mm-ss"
$reportPath = "validation-report-$Environment-$timestamp.txt"

@"
Azure Migration Validation Report
Generated: $(Get-Date)
Environment: $Environment
Resource Group: $ResourceGroupName

This report contains the validation results for the Azure migration.
Please review all test results to ensure proper configuration.

For detailed logs, refer to the console output during script execution.
"@ | Out-File -FilePath $reportPath

Write-Host "`nValidation report saved to: $reportPath" -ForegroundColor Cyan
