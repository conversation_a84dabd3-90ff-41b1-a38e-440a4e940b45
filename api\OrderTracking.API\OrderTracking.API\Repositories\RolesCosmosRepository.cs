using ClientPortal.Shared.Models;
using Microsoft.Azure.Cosmos;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace OrderTracking.API.Repositories
{
    /// <summary>
    /// Azure Cosmos DB repository for Role entities (migrated from Firebase)
    /// </summary>
    public class RolesCosmosRepository : BaseCosmosRepository<Role, string>, IRolesRepository
    {
        #region Constructors

        public RolesCosmosRepository(IContainerFactory containerFactory, IConfiguration configuration) 
            : base(containerFactory.CreateCollection<RolesCosmosRepository>(out var partitionKeyPath), partitionKeyPath)
        {
        }

        public RolesCosmosRepository(Container container, string partitionKeyPath)
            : base(container, partitionKeyPath)
        {
        }

        #endregion

        #region Interface Implementation

        public override async Task<Role> GetAsync(string entityId) => await GetAsync(entityId, entityId);

        public override async Task<Role> UpdateAsync(Role entity, string originalId)
        {
            try
            {
                // If the ID changed, we need to delete the old item and create a new one
                if (entity.Id != originalId)
                {
                    await RemoveAsync(originalId, originalId);
                    return await AddAsync(entity);
                }
                else
                {
                    return await UpdateAsync(entity);
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        public override async Task RemoveAsync(string id, string partitionId)
        {
            try
            {
                await Container.DeleteItemAsync<Role>(id, new PartitionKey(partitionId));
            }
            catch (CosmosException ex) when (ex.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                // Item doesn't exist, which is fine for a delete operation
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        public async Task<IEnumerable<Role>> GetRolesForGroupAsync(string group)
        {
            try
            {
                var queryDefinition = new QueryDefinition("SELECT * FROM c WHERE c.Group = @group")
                    .WithParameter("@group", group);

                var query = Container.GetItemQueryIterator<Role>(queryDefinition);
                var results = new List<Role>();

                while (query.HasMoreResults)
                {
                    var response = await query.ReadNextAsync();
                    results.AddRange(response.ToList());
                }

                return results;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        #endregion
    }
}
