import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class UtilsService {
  /**
   * Returns a new array with unique objects based on a specific key.
   *
   * @param array - The input array of objects.
   * @param key - The key of the object to check uniqueness by (e.g., 'id').
   * @returns A filtered array with unique values based on the specified key.
   */
  getUniqueBy<T extends Record<string, any>>(array: T[] | null | undefined, key: keyof T): T[] {
    // Handle null or undefined input arrays
    if (!Array.isArray(array)) {
      console.warn('[UtilsService] Invalid array provided to getUniqueBy');
      return [];
    }

    const encounteredKeys = new Set();

    try {
      return array.filter(item => {
        const keyValue = item[key];

        if (encounteredKeys.has(keyValue)) return false;

        encounteredKeys.add(keyValue);
        return true;
      });
    } catch (error) {
      console.error('[UtilsService] Error in getUniqueBy:', error);
      return [];
    }
  }
}

export function isAccessAllowedBasedOnRestrictedRoles(
  userRoles: string[],
  restrictedRoles: string[]
): boolean {
  const normalizedUserRoles = userRoles.map(r => r.toLowerCase().trim()) || [];

  // ✅ Allow if the user has ANY role not in restrictedRoles
  const hasAllowedRole = normalizedUserRoles.some(
    (role) => !restrictedRoles.includes(role)
  );

  return hasAllowedRole;
}
